import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon, Wedge, PathPatch, Ellipse
from matplotlib.path import Path
import matplotlib.patheffects as path_effects
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.colors as mcolors
from scipy.interpolate import interp1d

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['savefig.dpi'] = 600

# 数据
months = list(range(1, 13))
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'MRCNN': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [551, 548, 520, 547, 547, 547, 547, 547, 547, 547, 547, 547]
}

# 计算准确率 Accuracy = 1-(|Predicted-Real|)/Real
accuracy_csaf = [1 - (abs(csaf - real) / real) for csaf, real in zip(data['CSAF'], data['real'])]
accuracy_mrcnn = [1 - (abs(mrcnn - real) / real) for mrcnn, real in zip(data['MRCNN'], data['real'])]

# 设置颜色
petal_colors = ['#66c2a5', '#fc8d62', '#8da0cb', '#e78ac3', '#a6d854', '#ffd92f', 
                '#e5c494', '#b3b3b3', '#7fc97f', '#beaed4', '#fdc086', '#ffff99']

# 计算总和和平均准确率
total_real = sum(data['real'])
total_csaf = sum(data['CSAF'])
total_mrcnn = sum(data['MRCNN'])
avg_acc_csaf = sum(accuracy_csaf) / len(accuracy_csaf) * 100  # 转换为百分比
avg_acc_mrcnn = sum(accuracy_mrcnn) / len(accuracy_mrcnn) * 100  # 转换为百分比

# 创建两个图表
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7), facecolor='white')

def create_improved_flower(ax, predicted_values, real_values, accuracies, model_name, 
                          total_pred, total_real, avg_accuracy):
    # 中心多边形 - 使用五边形
    n_sides = 5
    center_radius = 2.5
    
    # 创建蓝色填充的中心多边形
    pentagon_verts = [(center_radius*np.cos(2*np.pi*i/n_sides), 
                      center_radius*np.sin(2*np.pi*i/n_sides)) 
                      for i in range(n_sides)]
    pentagon = Polygon(pentagon_verts, closed=True, 
                      facecolor='#4472c4', edgecolor='navy', 
                      linewidth=1.5, alpha=0.8, zorder=10)
    ax.add_patch(pentagon)
    
    # 花瓣位置计算
    n_petals = len(predicted_values)
    radius = 5.5  # 花瓣中心到整体中心的距离
    
    # 花瓣连接线路径
    petal_vertices = []
    
    # 创建花瓣
    for i in range(n_petals):
        angle = 2 * np.pi * i / n_petals - np.pi/2  # 从顶部开始
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # 根据值的大小设置椭圆大小
        width = 3.0 * (predicted_values[i] / max(predicted_values)) + 1.2
        height = width * 0.65  # 椭圆高宽比
        
        # 创建花瓣
        ellipse = Ellipse((x, y), width, height, angle=angle*180/np.pi, 
                         facecolor=petal_colors[i], alpha=0.7, 
                         edgecolor='none', zorder=5)
        ax.add_patch(ellipse)
        
        # 添加数值和准确率到花瓣
        ax.text(x, y, f"{predicted_values[i]}", 
                ha='center', va='center', fontsize=10, fontweight='bold', 
                color='white', zorder=6,
                path_effects=[path_effects.withStroke(linewidth=3, foreground=petal_colors[i])])
        
        # 收集花瓣顶点用于连接线
        petal_vertices.append((x, y))
        
        # 创建曲线连接线 (从中心到花瓣)
        verts = [
            (0, 0),  # 从中心开始
            (x*0.3, y*0.3),  # 控制点1
            (x*0.7, y*0.7),  # 控制点2
            (x, y)   # 到花瓣
        ]
        codes = [Path.MOVETO, Path.CURVE4, Path.CURVE4, Path.CURVE4]
        path = Path(verts, codes)
        patch = PathPatch(path, facecolor='none', edgecolor='#aaaaaa', 
                         linestyle='-', alpha=0.5, linewidth=0.8, zorder=1)
        ax.add_patch(patch)
    
    # 添加中心信息
    ax.text(0, 1.0, f"{model_name} Core", ha='center', va='center', 
            fontsize=11, fontweight='bold', color='white', zorder=11)
    ax.text(0, 0.3, f"{int(total_pred)}", ha='center', va='center', 
            fontsize=11, fontweight='bold', color='white', zorder=11)
    ax.text(0, -0.3, f"{int(total_real)}", ha='center', va='center', 
            fontsize=9, color='white', zorder=11)
    ax.text(0, -1.0, f"[{avg_accuracy:.1f}%]", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='white', zorder=11)
    
    # 设置图表范围和关闭坐标轴
    margin = 1.2
    ax.set_xlim(-radius*margin, radius*margin)
    ax.set_ylim(-radius*margin, radius*margin)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 添加月份标签
    for i in range(n_petals):
        angle = 2 * np.pi * i / n_petals - np.pi/2
        x = (radius + 2) * np.cos(angle)
        y = (radius + 2) * np.sin(angle)
        
        # 添加月份和精度标签
        month_text = f"Month {i+1}"
        acc_text = f"Acc: {accuracies[i]:.2f}"
        
        # 根据位置调整文本对齐方式
        ha = 'center'
        if x > radius*0.7:
            ha = 'left'
        elif x < -radius*0.7:
            ha = 'right'
            
        va = 'center'
        if y > radius*0.7:
            va = 'bottom'
        elif y < -radius*0.7:
            va = 'top'
            
        ax.text(x, y, month_text, ha=ha, va=va, fontsize=8, fontweight='bold')
        ax.text(x, y-0.6, acc_text, ha=ha, va=va, fontsize=7, color='#555555')

# 创建两个花瓣图
create_improved_flower(ax1, data['CSAF'], data['real'], accuracy_csaf, 
                      "CSAF", total_csaf, total_real, avg_acc_csaf)
create_improved_flower(ax2, data['MRCNN'], data['real'], accuracy_mrcnn, 
                      "Cascade Mask R-CNN", total_mrcnn, total_real, avg_acc_mrcnn)

# 添加标题和标记
fig.suptitle('Monthly Performance Comparison - Flower Diagram', fontsize=14, fontweight='bold', y=0.98)
ax1.text(-7, -7, 'A', fontsize=16, fontweight='bold')
ax2.text(-7, -7, 'B', fontsize=16, fontweight='bold')

# 保存图表
plt.tight_layout()
plt.savefig('improved_flower_diagram.png', dpi=600, bbox_inches='tight')
plt.savefig('improved_flower_diagram.pdf', bbox_inches='tight')

print("改进版花瓣图表已创建")
plt.show() 