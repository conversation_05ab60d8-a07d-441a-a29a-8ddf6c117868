import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon, PathPatch
from matplotlib.path import Path
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['savefig.dpi'] = 600

# 完整12个月数据
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'MRCNN': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [556, 548, 520, 551, 551, 551, 551, 551, 551, 551, 551, 551]
}

# 计算总和和平均准确率
total_real = sum(data['real'])
total_csaf = sum(data['CSAF'])
total_mrcnn = sum(data['MRCNN'])
avg_acc_csaf = np.mean([1 - (abs(csaf - real) / real) for csaf, real in zip(data['CSAF'], data['real'])]) * 100
avg_acc_mrcnn = np.mean([1 - (abs(mrcnn - real) / real) for mrcnn, real in zip(data['MRCNN'], data['real'])]) * 100

# 创建两个图表
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), facecolor='white')

# 更明亮的花瓣颜色
petal_colors = [
    '#80CFE8', '#80B1E8', '#A7A0E8', '#D197DD', '#F090C0',
    '#F2A088', '#F4B265', '#F8D35D', '#D7E07B', '#9ED688',
    '#68D0AE', '#60CBE5'
]

def create_simplified_flower(ax, model_values, real_values, model_name, total_model, total_real, avg_accuracy):
    # 中心五边形
    n_sides = 5
    center_radius = 2.2
    
    # 创建蓝色填充的中心多边形
    pentagon_verts = [(center_radius*np.cos(2*np.pi*i/n_sides), 
                      center_radius*np.sin(2*np.pi*i/n_sides)) 
                      for i in range(n_sides)]
    pentagon = Polygon(pentagon_verts, closed=True, 
                      facecolor='#B3C6E7', edgecolor='#2F5597', 
                      linewidth=1.5, alpha=0.9, zorder=10)
    ax.add_patch(pentagon)
    
    # 花瓣位置计算
    n_petals = len(model_values)
    radius = 7.5  # 花瓣中心到整体中心的距离
    petal_radius = 2.3  # 花瓣固定大小
    
    # 首先绘制连接线 - 更细、更透明
    for i in range(n_petals):
        angle = 2 * np.pi * i / n_petals
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # 创建到核心的连接线 - 直线而非曲线
        ax.plot([0, x], [0, y], color='#DDDDDD', linewidth=0.5, alpha=0.3, zorder=1)
    
    # 创建花瓣
    for i in range(n_petals):
        angle = 2 * np.pi * i / n_petals
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # 固定大小的圆形花瓣，色彩更均匀
        circle = Circle((x, y), petal_radius, 
                        facecolor=petal_colors[i], alpha=0.6, 
                        edgecolor='none', zorder=5)
        ax.add_patch(circle)
        
        # 只在花瓣中添加数值，加粗、更清晰
        ax.text(x, y, f"{model_values[i]}", 
                ha='center', va='center', fontsize=11, fontweight='bold', 
                color='black', zorder=6)
        
        # 月份标签位置调整 - 放在花瓣外侧
        month_x = (radius + petal_radius + 0.8) * np.cos(angle)
        month_y = (radius + petal_radius + 0.8) * np.sin(angle)
        
        # 根据位置调整文本对齐方式
        ha = 'center'
        if month_x > radius*0.7:
            ha = 'left'
        elif month_x < -radius*0.7:
            ha = 'right'
            
        va = 'center'
        if month_y > radius*0.7:
            va = 'bottom'
        elif month_y < -radius*0.7:
            va = 'top'
        
        # 添加月份标签 - 更简洁
        month_label = f"Month {i+1}"
        ax.text(month_x, month_y, month_label, 
                ha=ha, va=va, fontsize=9, fontweight='bold')
        
        # 添加真实值标签 - 位置更合理, 格式更简洁
        real_label_x = x + petal_radius * 0.6 * np.cos(angle+np.pi/4)
        real_label_y = y + petal_radius * 0.6 * np.sin(angle+np.pi/4)
        
        # 简化标签，只显示 "R:551"
        ax.text(real_label_x, real_label_y, f"R:{real_values[i]}", 
                ha='center', va='center', fontsize=7, color='dimgray')
    
    # 添加中心信息 - 更简洁明了
    ax.text(0, 0.8, f"{model_name}", ha='center', va='center', 
            fontsize=11, fontweight='bold', color='#2F5597', zorder=11)
    ax.text(0, 0, f"{int(total_model)}\n{int(total_real)}", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='#2F5597', zorder=11)
    ax.text(0, -0.8, f"[{avg_accuracy:.1f}%]", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='#2F5597', zorder=11)
    
    # 设置图表范围和关闭坐标轴
    margin = 1.2
    ax.set_xlim(-radius*margin, radius*margin)
    ax.set_ylim(-radius*margin, radius*margin)
    ax.set_aspect('equal')
    ax.axis('off')

# 创建两个花瓣图
create_simplified_flower(ax1, data['CSAF'], data['real'], 
                        "CSAF", total_csaf, total_real, avg_acc_csaf)
create_simplified_flower(ax2, data['MRCNN'], data['real'], 
                        "Mask R-CNN", total_mrcnn, total_real, avg_acc_mrcnn)

# 添加标题和标记
fig.suptitle('Model Performance Comparison - 12 Month Analysis', fontsize=14, fontweight='bold', y=0.98)
ax1.text(-9, -9, 'A', fontsize=16, fontweight='bold')
ax2.text(-9, -9, 'B', fontsize=16, fontweight='bold')

# 保存图表
plt.tight_layout()
plt.savefig('simplified_flower_diagram.png', dpi=600, bbox_inches='tight')
plt.savefig('simplified_flower_diagram.pdf', bbox_inches='tight')

print("简化版花瓣图表已创建")
plt.show() 