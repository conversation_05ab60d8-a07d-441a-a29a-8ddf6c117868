import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import matplotlib as mpl

# 设置专业学术图表参数
plt.rcParams.update({
    'font.family': 'Times New Roman',
    'font.size': 8,
    'axes.labelsize': 9,
    'xtick.labelsize': 8,
    'ytick.labelsize': 8,
    'legend.fontsize': 8,
    'figure.dpi': 600,
    'savefig.dpi': 600,
    'savefig.bbox': 'tight'
})

# 假设数据 - 根据上传图片中的分布创建
# 每个模型对应API评分的分布数据
np.random.seed(42)  # 设置随机种子以确保可重复性

# 模型列表
models = ['CSAF', 'DT', 'ST', 'HTC', 'MR', 'QI', 'YV', 'YL', 'SL']

# 根据图像创建模拟数据
data = {
    'CSAF': np.random.normal(42, 3, 100),  # 分数较高，集中在42左右
    'DT': np.random.normal(40, 2.5, 100),  # 分数较高，集中在40左右
    'ST': np.random.normal(39, 3, 100),     # 分数中等，集中在39左右
    'HTC': np.random.normal(35, 4, 100),    # 分数中等，较分散
    'MR': np.random.normal(30, 3, 100),     # 分数中等偏低
    'QI': np.random.normal(33, 2.5, 100),   # 分数中等
    'YV': np.random.normal(29, 2, 100),     # 分数较低
    'YL': np.random.normal(32, 2.2, 100),   # 分数中等偏低
    'SL': np.random.normal(10, 2, 100)      # 分数最低，集中在10左右
}

# 创建与第二个图相似的颜色映射
colors = [
    '#1A6840',  # 深绿色 - CSAF
    '#1E78B4',  # 深蓝色 - DT
    '#33A065',  # 绿色 - ST
    '#55C667',  # 浅绿色 - HTC
    '#61B2D0',  # 浅蓝色 - MR
    '#4C9380',  # 青绿色 - QI
    '#92C3AA',  # 极浅绿色 - YV
    '#9DC7E0',  # 极浅蓝色 - YL 
    '#D4E8D5'   # 最浅绿色 - SL
]

# 为每个模型创建一个箱型图
for i, model in enumerate(models):
    plt.figure(figsize=(3.5, 3))
    
    # 创建单一颜色的箱型图
    boxplot = plt.boxplot(data[model], 
                patch_artist=True, 
                widths=0.6,
                showfliers=True,  # 显示异常值
                medianprops={'color': 'black', 'linewidth': 1.5},
                boxprops={'facecolor': colors[i], 'alpha': 0.8, 'edgecolor': 'black', 'linewidth': 1})
    
    # 添加一个小提琴图在背景
    parts = plt.violinplot(data[model], showmeans=False, showmedians=False, showextrema=False)
    for pc in parts['bodies']:
        pc.set_facecolor(colors[i])
        pc.set_edgecolor('none')
        pc.set_alpha(0.3)
    
    # 添加散点图显示原始数据点
    y = data[model]
    x = np.random.normal(1, 0.08, len(y))  # 添加一点随机抖动
    plt.scatter(x, y, alpha=0.5, s=10, color=colors[i], edgecolor='none')
    
    # 设置图表标题和标签
    plt.title(f'API Score Distribution - {model}', fontweight='bold', fontsize=10)
    plt.ylabel('Score', fontweight='bold')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.yticks(np.arange(0, 51, 5))
    plt.xticks([])  # 隐藏x轴刻度
    
    # 添加平均值和中位数标注
    mean_val = np.mean(data[model])
    median_val = np.median(data[model])
    plt.axhline(mean_val, color='red', linestyle='-', linewidth=1.5, alpha=0.7)
    plt.text(1.3, mean_val, f'Mean: {mean_val:.2f}', verticalalignment='center', fontsize=8, fontweight='bold', color='red')
    plt.text(1.3, median_val, f'Median: {median_val:.2f}', verticalalignment='center', fontsize=8, fontweight='bold', color='black')
    
    # 设置y轴范围
    plt.ylim(0, 50)
    
    # 调整图表布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(f'API_Boxplot_{model}.svg', format='svg')
    plt.savefig(f'API_Boxplot_{model}.png', format='png', dpi=600)
    plt.close()

# 创建组合箱型图，所有模型一起比较
plt.figure(figsize=(9, 5))

# 准备数据为箱型图格式
box_data = [data[model] for model in models]

# 创建箱型图
boxplot = plt.boxplot(box_data, 
            patch_artist=True, 
            widths=0.6,
            showfliers=True,
            medianprops={'color': 'black', 'linewidth': 1.5})

# 为每个箱子设置颜色
for patch, color in zip(boxplot['boxes'], colors):
    patch.set_facecolor(color)
    patch.set_alpha(0.8)
    patch.set_edgecolor('black')
    patch.set_linewidth(1)

# 在箱型图后面添加小提琴图
positions = range(1, len(models) + 1)
for i, (pos, model) in enumerate(zip(positions, models)):
    parts = plt.violinplot(data[model], [pos], showmeans=False, showmedians=False, showextrema=False)
    for pc in parts['bodies']:
        pc.set_facecolor(colors[i])
        pc.set_edgecolor('none')
        pc.set_alpha(0.2)

# 设置图表标题和标签
plt.title('API Score Distribution Across All Models', fontweight='bold', fontsize=12)
plt.ylabel('Score', fontweight='bold')
plt.xlabel('Model', fontweight='bold')
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.xticks(range(1, len(models) + 1), models)
plt.yticks(np.arange(0, 51, 5))

# 设置y轴范围
plt.ylim(0, 50)

# 添加平均值标注
for i, model in enumerate(models):
    mean_val = np.mean(data[model])
    plt.text(i+1, 0, f'{mean_val:.1f}', ha='center', va='top', fontweight='bold', 
             color='red', bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))

# 调整图表布局
plt.tight_layout()

# 保存图表
plt.savefig(f'API_Boxplot_All_Models.svg', format='svg')
plt.savefig(f'API_Boxplot_All_Models.png', format='png', dpi=600)
plt.close()

# 创建一个包含所有数据点的图表
plt.figure(figsize=(9, 5))

# 为每个模型创建一个箱型图并添加数据点
for i, model in enumerate(models):
    # 获取数据
    y = data[model]
    # 创建与箱型图对应的位置
    x = np.random.normal(i+1, 0.08, len(y))
    # 绘制数据点
    plt.scatter(x, y, alpha=0.5, s=15, color=colors[i], edgecolor='none')
    
    # 计算四分位数
    q1, median, q3 = np.percentile(y, [25, 50, 75])
    # 计算IQR
    iqr = q3 - q1
    # 计算上下须的范围
    lower_whisker = max(min(y), q1 - 1.5 * iqr)
    upper_whisker = min(max(y), q3 + 1.5 * iqr)
    
    # 绘制箱型图的核心元素
    plt.plot([i+0.7, i+1.3], [median, median], 'k-', linewidth=2)  # 中位数线
    plt.plot([i+0.7, i+1.3], [q1, q1], 'k-', linewidth=1)  # Q1线
    plt.plot([i+0.7, i+1.3], [q3, q3], 'k-', linewidth=1)  # Q3线
    plt.plot([i+1, i+1], [q1, q3], 'k-', linewidth=1)  # 箱子的垂直边
    plt.plot([i+1, i+1], [lower_whisker, q1], 'k--', linewidth=1)  # 下须
    plt.plot([i+1, i+1], [q3, upper_whisker], 'k--', linewidth=1)  # 上须
    
    # 添加均值标记
    mean_val = np.mean(y)
    plt.plot([i+0.7, i+1.3], [mean_val, mean_val], 'r-', linewidth=1.5)

# 设置图表标题和标签
plt.title('API Score Distribution with Data Points', fontweight='bold', fontsize=12)
plt.ylabel('Score', fontweight='bold')
plt.xlabel('Model', fontweight='bold')
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.xticks(range(1, len(models) + 1), models)
plt.yticks(np.arange(0, 51, 5))

# 设置y轴范围
plt.ylim(0, 50)

# 调整图表布局
plt.tight_layout()

# 保存图表
plt.savefig(f'API_Boxplot_With_Points.svg', format='svg')
plt.savefig(f'API_Boxplot_With_Points.png', format='png', dpi=600)
plt.close()

print("所有箱型图已生成完毕！") 