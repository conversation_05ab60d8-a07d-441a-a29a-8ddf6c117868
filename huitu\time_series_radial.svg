<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1569.6pt" height="539.60125pt" viewBox="0 0 1569.6 539.60125" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-26T14:08:22.536831</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 539.60125 
L 1569.6 539.60125 
L 1569.6 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 784.8 249.20804 
C 790.76015 249.20804 796.47699 246.840047 800.691452 242.625585 
C 804.905915 238.411122 807.273907 232.694283 807.273907 226.734133 
C 807.273907 220.773983 804.905915 215.057143 800.691452 210.84268 
C 796.47699 206.628218 790.76015 204.260225 784.8 204.260225 
C 778.83985 204.260225 773.12301 206.628218 768.908548 210.84268 
C 764.694085 215.057143 762.326093 220.773983 762.326093 226.734133 
C 762.326093 232.694283 764.694085 238.411122 768.908548 242.625585 
C 773.12301 246.840047 778.83985 249.20804 784.8 249.20804 
L 784.8 249.20804 
z
" clip-path="url(#paa11a24f58)" style="fill: none; opacity: 0.3; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_3">
    <path d="M 784.8 271.681947 
C 796.7203 271.681947 808.15398 266.945962 816.582904 258.517037 
C 825.011829 250.088112 829.747814 238.654432 829.747814 226.734133 
C 829.747814 214.813833 825.011829 203.380153 816.582904 194.951228 
C 808.15398 186.522303 796.7203 181.786318 784.8 181.786318 
C 772.8797 181.786318 761.44602 186.522303 753.017096 194.951228 
C 744.588171 203.380153 739.852186 214.813833 739.852186 226.734133 
C 739.852186 238.654432 744.588171 250.088112 753.017096 258.517037 
C 761.44602 266.945962 772.8797 271.681947 784.8 271.681947 
L 784.8 271.681947 
z
" clip-path="url(#paa11a24f58)" style="fill: none; opacity: 0.3; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 784.8 294.155854 
C 802.68045 294.155854 819.830969 287.051876 832.474357 274.408489 
C 845.117744 261.765102 852.221722 244.614582 852.221722 226.734133 
C 852.221722 208.853683 845.117744 191.703163 832.474357 179.059776 
C 819.830969 166.416389 802.68045 159.312411 784.8 159.312411 
C 766.91955 159.312411 749.769031 166.416389 737.125643 179.059776 
C 724.482256 191.703163 717.378278 208.853683 717.378278 226.734133 
C 717.378278 244.614582 724.482256 261.765102 737.125643 274.408489 
C 749.769031 287.051876 766.91955 294.155854 784.8 294.155854 
L 784.8 294.155854 
z
" clip-path="url(#paa11a24f58)" style="fill: none; opacity: 0.3; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 784.8 316.629762 
C 808.640599 316.629762 831.507959 307.157791 848.365809 290.299942 
C 865.223658 273.442092 874.695629 250.574732 874.695629 226.734133 
C 874.695629 202.893533 865.223658 180.026173 848.365809 163.168324 
C 831.507959 146.310474 808.640599 136.838504 784.8 136.838504 
C 760.959401 136.838504 738.092041 146.310474 721.234191 163.168324 
C 704.376342 180.026173 694.904371 202.893533 694.904371 226.734133 
C 694.904371 250.574732 704.376342 273.442092 721.234191 290.299942 
C 738.092041 307.157791 760.959401 316.629762 784.8 316.629762 
L 784.8 316.629762 
z
" clip-path="url(#paa11a24f58)" style="fill: none; opacity: 0.3; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_6">
    <path d="M 784.8 339.103669 
C 814.600749 339.103669 843.184949 327.263706 864.257261 306.191394 
C 885.329573 285.119082 897.169536 256.534882 897.169536 226.734133 
C 897.169536 196.933383 885.329573 168.349184 864.257261 147.276872 
C 843.184949 126.20456 814.600749 114.364596 784.8 114.364596 
C 754.999251 114.364596 726.415051 126.20456 705.342739 147.276872 
C 684.270427 168.349184 672.430464 196.933383 672.430464 226.734133 
C 672.430464 256.534882 684.270427 285.119082 705.342739 306.191394 
C 726.415051 327.263706 754.999251 339.103669 784.8 339.103669 
L 784.8 339.103669 
z
" clip-path="url(#paa11a24f58)" style="fill: none; opacity: 0.3; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_7">
    <path d="M 784.8 361.577576 
C 820.560899 361.577576 854.861939 347.36962 880.148713 322.082846 
C 905.435488 296.796072 919.643443 262.495032 919.643443 226.734133 
C 919.643443 190.973233 905.435488 156.672194 880.148713 131.385419 
C 854.861939 106.098645 820.560899 91.890689 784.8 91.890689 
C 749.039101 91.890689 714.738061 106.098645 689.451287 131.385419 
C 664.164512 156.672194 649.956557 190.973233 649.956557 226.734133 
C 649.956557 262.495032 664.164512 296.796072 689.451287 322.082846 
C 714.738061 347.36962 749.039101 361.577576 784.8 361.577576 
L 784.8 361.577576 
z
" clip-path="url(#paa11a24f58)" style="fill: none; opacity: 0.3; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_8">
    <path d="M 784.8 226.734133 
L 905.709621 226.734133 
L 905.709621 190.461246 
L 784.8 190.461246 
z
" clip-path="url(#paa11a24f58)" style="fill: #00441b; opacity: 0.8; stroke: #00441b; stroke-linejoin: miter"/>
   </g>
   <g id="patch_9">
    <path d="M 784.8 226.734133 
L 901.15545 218.597767 
L 898.714541 183.691132 
L 782.35909 191.827498 
z
" clip-path="url(#paa11a24f58)" style="fill: #083b7c; opacity: 0.8; stroke: #083b7c; stroke-linejoin: miter"/>
   </g>
   <g id="patch_10">
    <path d="M 784.8 226.734133 
L 891.860152 170.54463 
L 875.003302 138.426584 
L 767.943149 194.616087 
z
" clip-path="url(#paa11a24f58)" style="fill: #00441b; opacity: 0.8; stroke: #00441b; stroke-linejoin: miter"/>
   </g>
   <g id="patch_11">
    <path d="M 784.8 226.734133 
L 883.090345 166.047006 
L 864.884207 136.559903 
L 766.593862 197.247029 
z
" clip-path="url(#paa11a24f58)" style="fill: #083e81; opacity: 0.8; stroke: #083e81; stroke-linejoin: miter"/>
   </g>
   <g id="patch_12">
    <path d="M 784.8 226.734133 
L 807.779942 193.441939 
L 797.792284 186.547957 
L 774.812342 219.84015 
z
" clip-path="url(#paa11a24f58)" style="fill: #dbf1d5; opacity: 0.8; stroke: #dbf1d5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_13">
    <path d="M 784.8 226.734133 
L 804.14263 194.047566 
L 794.33666 188.244776 
L 774.99403 220.931344 
z
" clip-path="url(#paa11a24f58)" style="fill: #dbe9f6; opacity: 0.8; stroke: #dbe9f6; stroke-linejoin: miter"/>
   </g>
   <g id="patch_14">
    <path d="M 784.8 226.734133 
L 788.646681 195.053866 
L 779.142601 193.899861 
L 775.29592 225.580128 
z
" clip-path="url(#paa11a24f58)" style="fill: #ecf8e8; opacity: 0.8; stroke: #ecf8e8; stroke-linejoin: miter"/>
   </g>
   <g id="patch_15">
    <path d="M 784.8 226.734133 
L 786.30134 197.33162 
L 777.480586 196.881218 
L 775.979246 226.283731 
z
" clip-path="url(#paa11a24f58)" style="fill: #ecf4fb; opacity: 0.8; stroke: #ecf4fb; stroke-linejoin: miter"/>
   </g>
   <g id="patch_16">
    <path d="M 784.8 226.734133 
L 775.157078 201.307836 
L 767.529189 204.200713 
L 777.172111 229.627009 
z
" clip-path="url(#paa11a24f58)" style="fill: #f2faf0; opacity: 0.8; stroke: #f2faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_17">
    <path d="M 784.8 226.734133 
L 774.442653 204.28713 
L 767.708552 207.394334 
L 778.065899 229.841337 
z
" clip-path="url(#paa11a24f58)" style="fill: #f6faff; opacity: 0.8; stroke: #f6faff; stroke-linejoin: miter"/>
   </g>
   <g id="patch_18">
    <path d="M 784.8 226.734133 
L 764.445427 208.701555 
L 759.035654 214.807926 
L 779.390227 232.840505 
z
" clip-path="url(#paa11a24f58)" style="fill: #f2faf0; opacity: 0.8; stroke: #f2faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_19">
    <path d="M 784.8 226.734133 
L 765.375588 211.80853 
L 760.897907 217.635853 
L 780.322319 232.561456 
z
" clip-path="url(#paa11a24f58)" style="fill: #f6faff; opacity: 0.8; stroke: #f6faff; stroke-linejoin: miter"/>
   </g>
   <g id="patch_20">
    <path d="M 784.8 226.734133 
L 758.396764 220.226319 
L 756.44442 228.14729 
L 782.847656 234.655104 
z
" clip-path="url(#paa11a24f58)" style="fill: #f2faf0; opacity: 0.8; stroke: #f2faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_21">
    <path d="M 784.8 226.734133 
L 759.999978 222.429849 
L 758.708692 229.869855 
L 783.508715 234.174139 
z
" clip-path="url(#paa11a24f58)" style="fill: #f5fafe; opacity: 0.8; stroke: #f5fafe; stroke-linejoin: miter"/>
   </g>
   <g id="patch_22">
    <path d="M 784.8 226.734133 
L 758.396764 233.241946 
L 760.349108 241.162917 
L 786.752344 234.655104 
z
" clip-path="url(#paa11a24f58)" style="fill: #f2faf0; opacity: 0.8; stroke: #f2faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_23">
    <path d="M 784.8 226.734133 
L 761.054296 234.37915 
L 763.347801 241.502861 
L 787.093505 233.857844 
z
" clip-path="url(#paa11a24f58)" style="fill: #f5fafe; opacity: 0.8; stroke: #f5fafe; stroke-linejoin: miter"/>
   </g>
   <g id="patch_24">
    <path d="M 784.8 226.734133 
L 764.445427 244.766711 
L 769.8552 250.873083 
L 790.209773 232.840505 
z
" clip-path="url(#paa11a24f58)" style="fill: #f2faf0; opacity: 0.8; stroke: #f2faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_25">
    <path d="M 784.8 226.734133 
L 767.169625 244.699039 
L 772.559097 249.988151 
L 790.189472 232.023245 
z
" clip-path="url(#paa11a24f58)" style="fill: #f5fafe; opacity: 0.8; stroke: #f5fafe; stroke-linejoin: miter"/>
   </g>
   <g id="patch_26">
    <path d="M 784.8 226.734133 
L 775.157078 252.160429 
L 782.784967 255.053306 
L 792.427889 229.627009 
z
" clip-path="url(#paa11a24f58)" style="fill: #f2faf0; opacity: 0.8; stroke: #f2faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_27">
    <path d="M 784.8 226.734133 
L 777.667469 250.404147 
L 784.768473 252.543906 
L 791.901004 228.873892 
z
" clip-path="url(#paa11a24f58)" style="fill: #f6faff; opacity: 0.8; stroke: #f6faff; stroke-linejoin: miter"/>
   </g>
   <g id="patch_28">
    <path d="M 784.8 226.734133 
L 788.077806 253.72929 
L 796.176353 252.745948 
L 792.898547 225.750791 
z
" clip-path="url(#paa11a24f58)" style="fill: #f2faf0; opacity: 0.8; stroke: #f2faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_29">
    <path d="M 784.8 226.734133 
L 789.527048 251.228209 
L 796.875271 249.810095 
L 792.148223 225.316018 
z
" clip-path="url(#paa11a24f58)" style="fill: #f5fafe; opacity: 0.8; stroke: #f5fafe; stroke-linejoin: miter"/>
   </g>
   <g id="patch_30">
    <path d="M 784.8 226.734133 
L 799.992295 248.743972 
L 806.595247 244.186283 
L 791.402952 222.176444 
z
" clip-path="url(#paa11a24f58)" style="fill: #f3faf0; opacity: 0.8; stroke: #f3faf0; stroke-linejoin: miter"/>
   </g>
   <g id="patch_31">
    <path d="M 784.8 226.734133 
L 799.947786 245.69899 
L 805.637243 241.154654 
L 790.489457 222.189797 
z
" clip-path="url(#paa11a24f58)" style="fill: #f7fbff; opacity: 0.8; stroke: #f7fbff; stroke-linejoin: miter"/>
   </g>
   <g id="patch_32">
    <path d="M 784.8 226.734133 
L 808.082598 238.953783 
L 811.748493 231.969003 
L 788.465895 219.749353 
z
" clip-path="url(#paa11a24f58)" style="fill: #f4fbf2; opacity: 0.8; stroke: #f4fbf2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_33">
    <path d="M 784.8 226.734133 
L 806.82031 236.396847 
L 809.719124 229.790754 
L 787.698814 220.12804 
z
" clip-path="url(#paa11a24f58)" style="fill: #f7fbff; opacity: 0.8; stroke: #f7fbff; stroke-linejoin: miter"/>
   </g>
   <g id="text_1">
    <!-- 100 -->
    <g style="fill: #808080; opacity: 0.7" transform="translate(776.458594 202.272725) scale(0.1 -0.1)">
     <defs>
      <path id="ArialMT-31" d="M 2384 0 
L 1822 0 
L 1822 3584 
Q 1619 3391 1289 3197 
Q 959 3003 697 2906 
L 697 3450 
Q 1169 3672 1522 3987 
Q 1875 4303 2022 4600 
L 2384 4600 
L 2384 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-30" d="M 266 2259 
Q 266 3072 433 3567 
Q 600 4063 929 4331 
Q 1259 4600 1759 4600 
Q 2128 4600 2406 4451 
Q 2684 4303 2865 4023 
Q 3047 3744 3150 3342 
Q 3253 2941 3253 2259 
Q 3253 1453 3087 958 
Q 2922 463 2592 192 
Q 2263 -78 1759 -78 
Q 1097 -78 719 397 
Q 266 969 266 2259 
z
M 844 2259 
Q 844 1131 1108 757 
Q 1372 384 1759 384 
Q 2147 384 2411 759 
Q 2675 1134 2675 2259 
Q 2675 3391 2411 3762 
Q 2147 4134 1753 4134 
Q 1366 4134 1134 3806 
Q 844 3388 844 2259 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_2">
    <!-- 200 -->
    <g style="fill: #808080; opacity: 0.7" transform="translate(776.458594 179.798818) scale(0.1 -0.1)">
     <defs>
      <path id="ArialMT-32" d="M 3222 541 
L 3222 0 
L 194 0 
Q 188 203 259 391 
Q 375 700 629 1000 
Q 884 1300 1366 1694 
Q 2113 2306 2375 2664 
Q 2638 3022 2638 3341 
Q 2638 3675 2398 3904 
Q 2159 4134 1775 4134 
Q 1369 4134 1125 3890 
Q 881 3647 878 3216 
L 300 3275 
Q 359 3922 746 4261 
Q 1134 4600 1788 4600 
Q 2447 4600 2831 4234 
Q 3216 3869 3216 3328 
Q 3216 3053 3103 2787 
Q 2991 2522 2730 2228 
Q 2469 1934 1863 1422 
Q 1356 997 1212 845 
Q 1069 694 975 541 
L 3222 541 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-32"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_3">
    <!-- 300 -->
    <g style="fill: #808080; opacity: 0.7" transform="translate(776.458594 157.324911) scale(0.1 -0.1)">
     <defs>
      <path id="ArialMT-33" d="M 269 1209 
L 831 1284 
Q 928 806 1161 595 
Q 1394 384 1728 384 
Q 2125 384 2398 659 
Q 2672 934 2672 1341 
Q 2672 1728 2419 1979 
Q 2166 2231 1775 2231 
Q 1616 2231 1378 2169 
L 1441 2663 
Q 1497 2656 1531 2656 
Q 1891 2656 2178 2843 
Q 2466 3031 2466 3422 
Q 2466 3731 2256 3934 
Q 2047 4138 1716 4138 
Q 1388 4138 1169 3931 
Q 950 3725 888 3313 
L 325 3413 
Q 428 3978 793 4289 
Q 1159 4600 1703 4600 
Q 2078 4600 2393 4439 
Q 2709 4278 2876 4000 
Q 3044 3722 3044 3409 
Q 3044 3113 2884 2869 
Q 2725 2625 2413 2481 
Q 2819 2388 3044 2092 
Q 3269 1797 3269 1353 
Q 3269 753 2831 336 
Q 2394 -81 1725 -81 
Q 1122 -81 723 278 
Q 325 638 269 1209 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-33"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_4">
    <!-- 400 -->
    <g style="fill: #808080; opacity: 0.7" transform="translate(776.458594 134.851004) scale(0.1 -0.1)">
     <defs>
      <path id="ArialMT-34" d="M 2069 0 
L 2069 1097 
L 81 1097 
L 81 1613 
L 2172 4581 
L 2631 4581 
L 2631 1613 
L 3250 1613 
L 3250 1097 
L 2631 1097 
L 2631 0 
L 2069 0 
z
M 2069 1613 
L 2069 3678 
L 634 1613 
L 2069 1613 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-34"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_5">
    <!-- 500 -->
    <g style="fill: #808080; opacity: 0.7" transform="translate(776.458594 112.377096) scale(0.1 -0.1)">
     <defs>
      <path id="ArialMT-35" d="M 266 1200 
L 856 1250 
Q 922 819 1161 601 
Q 1400 384 1738 384 
Q 2144 384 2425 690 
Q 2706 997 2706 1503 
Q 2706 1984 2436 2262 
Q 2166 2541 1728 2541 
Q 1456 2541 1237 2417 
Q 1019 2294 894 2097 
L 366 2166 
L 809 4519 
L 3088 4519 
L 3088 3981 
L 1259 3981 
L 1013 2750 
Q 1425 3038 1878 3038 
Q 2478 3038 2890 2622 
Q 3303 2206 3303 1553 
Q 3303 931 2941 478 
Q 2500 -78 1738 -78 
Q 1113 -78 717 272 
Q 322 622 266 1200 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-35"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_6">
    <!-- 600 -->
    <g style="fill: #808080; opacity: 0.7" transform="translate(776.458594 89.903189) scale(0.1 -0.1)">
     <defs>
      <path id="ArialMT-36" d="M 3184 3459 
L 2625 3416 
Q 2550 3747 2413 3897 
Q 2184 4138 1850 4138 
Q 1581 4138 1378 3988 
Q 1113 3794 959 3422 
Q 806 3050 800 2363 
Q 1003 2672 1297 2822 
Q 1591 2972 1913 2972 
Q 2475 2972 2870 2558 
Q 3266 2144 3266 1488 
Q 3266 1056 3080 686 
Q 2894 316 2569 119 
Q 2244 -78 1831 -78 
Q 1128 -78 684 439 
Q 241 956 241 2144 
Q 241 3472 731 4075 
Q 1159 4600 1884 4600 
Q 2425 4600 2770 4297 
Q 3116 3994 3184 3459 
z
M 888 1484 
Q 888 1194 1011 928 
Q 1134 663 1356 523 
Q 1578 384 1822 384 
Q 2178 384 2434 671 
Q 2691 959 2691 1453 
Q 2691 1928 2437 2201 
Q 2184 2475 1800 2475 
Q 1419 2475 1153 2201 
Q 888 1928 888 1484 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-36"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_7">
    <!-- 0809 -->
    <g transform="translate(919.232176 229.83632) scale(0.12 -0.12)">
     <defs>
      <path id="ArialMT-38" d="M 1131 2484 
Q 781 2613 612 2850 
Q 444 3088 444 3419 
Q 444 3919 803 4259 
Q 1163 4600 1759 4600 
Q 2359 4600 2725 4251 
Q 3091 3903 3091 3403 
Q 3091 3084 2923 2848 
Q 2756 2613 2416 2484 
Q 2838 2347 3058 2040 
Q 3278 1734 3278 1309 
Q 3278 722 2862 322 
Q 2447 -78 1769 -78 
Q 1091 -78 675 323 
Q 259 725 259 1325 
Q 259 1772 486 2073 
Q 713 2375 1131 2484 
z
M 1019 3438 
Q 1019 3113 1228 2906 
Q 1438 2700 1772 2700 
Q 2097 2700 2305 2904 
Q 2513 3109 2513 3406 
Q 2513 3716 2298 3927 
Q 2084 4138 1766 4138 
Q 1444 4138 1231 3931 
Q 1019 3725 1019 3438 
z
M 838 1322 
Q 838 1081 952 856 
Q 1066 631 1291 507 
Q 1516 384 1775 384 
Q 2178 384 2440 643 
Q 2703 903 2703 1303 
Q 2703 1709 2433 1975 
Q 2163 2241 1756 2241 
Q 1359 2241 1098 1978 
Q 838 1716 838 1322 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-39" d="M 350 1059 
L 891 1109 
Q 959 728 1153 556 
Q 1347 384 1650 384 
Q 1909 384 2104 503 
Q 2300 622 2425 820 
Q 2550 1019 2634 1356 
Q 2719 1694 2719 2044 
Q 2719 2081 2716 2156 
Q 2547 1888 2255 1720 
Q 1963 1553 1622 1553 
Q 1053 1553 659 1965 
Q 266 2378 266 3053 
Q 266 3750 677 4175 
Q 1088 4600 1706 4600 
Q 2153 4600 2523 4359 
Q 2894 4119 3086 3673 
Q 3278 3228 3278 2384 
Q 3278 1506 3087 986 
Q 2897 466 2520 194 
Q 2144 -78 1638 -78 
Q 1100 -78 759 220 
Q 419 519 350 1059 
z
M 2653 3081 
Q 2653 3566 2395 3850 
Q 2138 4134 1775 4134 
Q 1400 4134 1122 3828 
Q 844 3522 844 3034 
Q 844 2597 1108 2323 
Q 1372 2050 1759 2050 
Q 2150 2050 2401 2323 
Q 2653 2597 2653 3081 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-30"/>
     <use xlink:href="#ArialMT-38" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-39" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_8">
    <!-- 0905 -->
    <g transform="translate(905.275438 167.007236) rotate(-27.692308) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-30"/>
     <use xlink:href="#ArialMT-39" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-35" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_9">
    <!-- 0909 -->
    <g transform="translate(863.71923 117.860864) rotate(-55.384615) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-30"/>
     <use xlink:href="#ArialMT-39" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-39" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_10">
    <!-- 0926 -->
    <g transform="translate(804.083577 93.656046) rotate(-83.076923) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-30"/>
     <use xlink:href="#ArialMT-39" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-32" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-36" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_11">
    <!-- 1009 -->
    <g transform="translate(740.030289 99.937814) rotate(-110.769231) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-39" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_12">
    <!-- 1024 -->
    <g transform="translate(686.233202 135.26709) rotate(-138.461538) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-32" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-34" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_13">
    <!-- 1031 -->
    <g transform="translate(655.016581 191.550364) rotate(-166.153846) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-33" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-31" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_14">
    <!-- 1108 -->
    <g transform="translate(629.529358 268.199938) rotate(-13.846154) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-31" transform="translate(48.240234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(103.855469 0)"/>
     <use xlink:href="#ArialMT-38" transform="translate(159.470703 0)"/>
    </g>
   </g>
   <g id="text_15">
    <!-- 1115 -->
    <g transform="translate(666.916011 335.314713) rotate(-41.538462) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-31" transform="translate(48.240234 0)"/>
     <use xlink:href="#ArialMT-31" transform="translate(96.480469 0)"/>
     <use xlink:href="#ArialMT-35" transform="translate(152.095703 0)"/>
    </g>
   </g>
   <g id="text_16">
    <!-- 1128 -->
    <g transform="translate(730.721911 378.074628) rotate(-69.230769) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-31" transform="translate(48.240234 0)"/>
     <use xlink:href="#ArialMT-32" transform="translate(103.855469 0)"/>
     <use xlink:href="#ArialMT-38" transform="translate(159.470703 0)"/>
    </g>
   </g>
   <g id="text_17">
    <!-- 1218 -->
    <g transform="translate(807.301003 386.310101) rotate(-96.923077) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-31"/>
     <use xlink:href="#ArialMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-31" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-38" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_18">
    <!-- 0124 -->
    <g transform="translate(878.882298 357.574898) rotate(-124.615385) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-30"/>
     <use xlink:href="#ArialMT-31" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-32" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-34" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_19">
    <!-- 0206 -->
    <g transform="translate(928.910473 298.865652) rotate(-152.307692) scale(0.12 -0.12)">
     <use xlink:href="#ArialMT-30"/>
     <use xlink:href="#ArialMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
     <use xlink:href="#ArialMT-36" transform="translate(166.845703 0)"/>
    </g>
   </g>
   <g id="text_20">
    <g id="patch_34">
     <path d="M 873.186291 233.299758 
L 889.869103 233.299758 
Q 891.869103 233.299758 891.869103 231.299758 
L 891.869103 222.168508 
Q 891.869103 220.168508 889.869103 220.168508 
L 873.186291 220.168508 
Q 871.186291 220.168508 871.186291 222.168508 
L 871.186291 231.299758 
Q 871.186291 233.299758 873.186291 233.299758 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 538 -->
    <g style="fill: #ffffff" transform="translate(873.186291 229.32632) scale(0.1 -0.1)">
     <defs>
      <path id="Arial-BoldMT-35" d="M 284 1178 
L 1159 1269 
Q 1197 972 1381 798 
Q 1566 625 1806 625 
Q 2081 625 2272 848 
Q 2463 1072 2463 1522 
Q 2463 1944 2273 2155 
Q 2084 2366 1781 2366 
Q 1403 2366 1103 2031 
L 391 2134 
L 841 4519 
L 3163 4519 
L 3163 3697 
L 1506 3697 
L 1369 2919 
Q 1663 3066 1969 3066 
Q 2553 3066 2959 2641 
Q 3366 2216 3366 1538 
Q 3366 972 3038 528 
Q 2591 -78 1797 -78 
Q 1163 -78 763 262 
Q 363 603 284 1178 
z
" transform="scale(0.015625)"/>
      <path id="Arial-BoldMT-33" d="M 241 1216 
L 1091 1319 
Q 1131 994 1309 822 
Q 1488 650 1741 650 
Q 2013 650 2198 856 
Q 2384 1063 2384 1413 
Q 2384 1744 2206 1937 
Q 2028 2131 1772 2131 
Q 1603 2131 1369 2066 
L 1466 2781 
Q 1822 2772 2009 2936 
Q 2197 3100 2197 3372 
Q 2197 3603 2059 3740 
Q 1922 3878 1694 3878 
Q 1469 3878 1309 3722 
Q 1150 3566 1116 3266 
L 306 3403 
Q 391 3819 561 4067 
Q 731 4316 1036 4458 
Q 1341 4600 1719 4600 
Q 2366 4600 2756 4188 
Q 3078 3850 3078 3425 
Q 3078 2822 2419 2463 
Q 2813 2378 3048 2084 
Q 3284 1791 3284 1375 
Q 3284 772 2843 347 
Q 2403 -78 1747 -78 
Q 1125 -78 715 280 
Q 306 638 241 1216 
z
" transform="scale(0.015625)"/>
      <path id="Arial-BoldMT-38" d="M 1025 2472 
Q 684 2616 529 2867 
Q 375 3119 375 3419 
Q 375 3931 733 4265 
Q 1091 4600 1750 4600 
Q 2403 4600 2764 4265 
Q 3125 3931 3125 3419 
Q 3125 3100 2959 2851 
Q 2794 2603 2494 2472 
Q 2875 2319 3073 2025 
Q 3272 1731 3272 1347 
Q 3272 713 2867 316 
Q 2463 -81 1791 -81 
Q 1166 -81 750 247 
Q 259 634 259 1309 
Q 259 1681 443 1992 
Q 628 2303 1025 2472 
z
M 1206 3356 
Q 1206 3094 1354 2947 
Q 1503 2800 1750 2800 
Q 2000 2800 2150 2948 
Q 2300 3097 2300 3359 
Q 2300 3606 2151 3754 
Q 2003 3903 1759 3903 
Q 1506 3903 1356 3753 
Q 1206 3603 1206 3356 
z
M 1125 1394 
Q 1125 1031 1311 828 
Q 1497 625 1775 625 
Q 2047 625 2225 820 
Q 2403 1016 2403 1384 
Q 2403 1706 2222 1901 
Q 2041 2097 1763 2097 
Q 1441 2097 1283 1875 
Q 1125 1653 1125 1394 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#Arial-BoldMT-35"/>
     <use xlink:href="#Arial-BoldMT-33" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-38" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_21">
    <g id="patch_35">
     <path d="M 869.542954 226.790665 
L 886.225767 226.790665 
Q 888.225767 226.790665 888.225767 224.790665 
L 888.225767 215.659415 
Q 888.225767 213.659415 886.225767 213.659415 
L 869.542954 213.659415 
Q 867.542954 213.659415 867.542954 215.659415 
L 867.542954 224.790665 
Q 867.542954 226.790665 869.542954 226.790665 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 519 -->
    <g style="fill: #ffffff" transform="translate(869.542954 222.817228) scale(0.1 -0.1)">
     <defs>
      <path id="Arial-BoldMT-31" d="M 2519 0 
L 1641 0 
L 1641 3309 
Q 1159 2859 506 2644 
L 506 3441 
Q 850 3553 1253 3867 
Q 1656 4181 1806 4600 
L 2519 4600 
L 2519 0 
z
" transform="scale(0.015625)"/>
      <path id="Arial-BoldMT-39" d="M 291 1059 
L 1141 1153 
Q 1172 894 1303 769 
Q 1434 644 1650 644 
Q 1922 644 2112 894 
Q 2303 1144 2356 1931 
Q 2025 1547 1528 1547 
Q 988 1547 595 1964 
Q 203 2381 203 3050 
Q 203 3747 617 4173 
Q 1031 4600 1672 4600 
Q 2369 4600 2816 4061 
Q 3263 3522 3263 2288 
Q 3263 1031 2797 475 
Q 2331 -81 1584 -81 
Q 1047 -81 715 205 
Q 384 491 291 1059 
z
M 2278 2978 
Q 2278 3403 2083 3637 
Q 1888 3872 1631 3872 
Q 1388 3872 1227 3680 
Q 1066 3488 1066 3050 
Q 1066 2606 1241 2398 
Q 1416 2191 1678 2191 
Q 1931 2191 2104 2391 
Q 2278 2591 2278 2978 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#Arial-BoldMT-35"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_22">
    <!-- 0.96 -->
    <g transform="translate(859.706266 229.32632) scale(0.1 -0.1)">
     <defs>
      <path id="Arial-BoldMT-30" d="M 1756 4600 
Q 2422 4600 2797 4125 
Q 3244 3563 3244 2259 
Q 3244 959 2794 391 
Q 2422 -78 1756 -78 
Q 1088 -78 678 436 
Q 269 950 269 2269 
Q 269 3563 719 4131 
Q 1091 4600 1756 4600 
z
M 1756 3872 
Q 1597 3872 1472 3770 
Q 1347 3669 1278 3406 
Q 1188 3066 1188 2259 
Q 1188 1453 1269 1151 
Q 1350 850 1473 750 
Q 1597 650 1756 650 
Q 1916 650 2041 751 
Q 2166 853 2234 1116 
Q 2325 1453 2325 2259 
Q 2325 3066 2244 3367 
Q 2163 3669 2039 3770 
Q 1916 3872 1756 3872 
z
" transform="scale(0.015625)"/>
      <path id="Arial-BoldMT-2e" d="M 459 0 
L 459 878 
L 1338 878 
L 1338 0 
L 459 0 
z
" transform="scale(0.015625)"/>
      <path id="Arial-BoldMT-36" d="M 3247 3459 
L 2397 3366 
Q 2366 3628 2234 3753 
Q 2103 3878 1894 3878 
Q 1616 3878 1423 3628 
Q 1231 3378 1181 2588 
Q 1509 2975 1997 2975 
Q 2547 2975 2939 2556 
Q 3331 2138 3331 1475 
Q 3331 772 2918 347 
Q 2506 -78 1859 -78 
Q 1166 -78 719 461 
Q 272 1000 272 2228 
Q 272 3488 737 4044 
Q 1203 4600 1947 4600 
Q 2469 4600 2811 4308 
Q 3153 4016 3247 3459 
z
M 1256 1544 
Q 1256 1116 1453 883 
Q 1650 650 1903 650 
Q 2147 650 2309 840 
Q 2472 1031 2472 1466 
Q 2472 1913 2297 2120 
Q 2122 2328 1859 2328 
Q 1606 2328 1431 2129 
Q 1256 1931 1256 1544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-36" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_36">
     <path d="M 862.106716 188.348156 
L 878.789528 188.348156 
Q 880.789528 188.348156 880.789528 186.348156 
L 880.789528 177.216906 
Q 880.789528 175.216906 878.789528 175.216906 
L 862.106716 175.216906 
Q 860.106716 175.216906 860.106716 177.216906 
L 860.106716 186.348156 
Q 860.106716 188.348156 862.106716 188.348156 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 538 -->
    <g style="fill: #ffffff" transform="translate(862.106716 184.374718) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-35"/>
     <use xlink:href="#Arial-BoldMT-33" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-38" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_24">
    <g id="patch_37">
     <path d="M 855.09087 184.750057 
L 871.773682 184.750057 
Q 873.773682 184.750057 873.773682 182.750057 
L 873.773682 173.618807 
Q 873.773682 171.618807 871.773682 171.618807 
L 855.09087 171.618807 
Q 853.09087 171.618807 853.09087 173.618807 
L 853.09087 182.750057 
Q 853.09087 184.750057 855.09087 184.750057 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 514 -->
    <g style="fill: #ffffff" transform="translate(855.09087 180.776619) scale(0.1 -0.1)">
     <defs>
      <path id="Arial-BoldMT-34" d="M 1994 0 
L 1994 922 
L 119 922 
L 119 1691 
L 2106 4600 
L 2844 4600 
L 2844 1694 
L 3413 1694 
L 3413 922 
L 2844 922 
L 2844 0 
L 1994 0 
z
M 1994 1694 
L 1994 3259 
L 941 1694 
L 1994 1694 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#Arial-BoldMT-35"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-34" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_25">
    <!-- 0.96 -->
    <g transform="translate(850.011638 189.993668) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-36" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_26">
    <g id="patch_38">
     <path d="M 794.842547 206.666003 
L 811.52536 206.666003 
Q 813.52536 206.666003 813.52536 204.666003 
L 813.52536 195.534753 
Q 813.52536 193.534753 811.52536 193.534753 
L 794.842547 193.534753 
Q 792.842547 193.534753 792.842547 195.534753 
L 792.842547 204.666003 
Q 792.842547 206.666003 794.842547 206.666003 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 180 -->
    <g style="fill: #ffffff" transform="translate(794.842547 202.692565) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-38" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-30" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_27">
    <g id="patch_39">
     <path d="M 791.932698 207.150504 
L 808.61551 207.150504 
Q 810.61551 207.150504 810.61551 205.150504 
L 810.61551 196.019254 
Q 810.61551 194.019254 808.61551 194.019254 
L 791.932698 194.019254 
Q 789.932698 194.019254 789.932698 196.019254 
L 789.932698 205.150504 
Q 789.932698 207.150504 791.932698 207.150504 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 169 -->
    <g style="fill: #ffffff" transform="translate(791.932698 203.177066) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-36" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_28">
    <!-- 0.94 -->
    <g transform="translate(791.155491 206.021785) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-34" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_29">
    <g id="patch_40">
     <path d="M 779.535938 207.955544 
L 796.218751 207.955544 
Q 798.218751 207.955544 798.218751 205.955544 
L 798.218751 196.824294 
Q 798.218751 194.824294 796.218751 194.824294 
L 779.535938 194.824294 
Q 777.535938 194.824294 777.535938 196.824294 
L 777.535938 205.955544 
Q 777.535938 207.955544 779.535938 207.955544 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 142 -->
    <g style="fill: #ffffff" transform="translate(779.535938 203.982107) scale(0.1 -0.1)">
     <defs>
      <path id="Arial-BoldMT-32" d="M 3238 816 
L 3238 0 
L 159 0 
Q 209 463 459 877 
Q 709 1291 1447 1975 
Q 2041 2528 2175 2725 
Q 2356 2997 2356 3263 
Q 2356 3556 2198 3714 
Q 2041 3872 1763 3872 
Q 1488 3872 1325 3706 
Q 1163 3541 1138 3156 
L 263 3244 
Q 341 3969 753 4284 
Q 1166 4600 1784 4600 
Q 2463 4600 2850 4234 
Q 3238 3869 3238 3325 
Q 3238 3016 3127 2736 
Q 3016 2456 2775 2150 
Q 2616 1947 2200 1565 
Q 1784 1184 1673 1059 
Q 1563 934 1494 816 
L 3238 816 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-34" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_30">
    <g id="patch_41">
     <path d="M 777.659666 209.777747 
L 794.342478 209.777747 
Q 796.342478 209.777747 796.342478 207.777747 
L 796.342478 198.646497 
Q 796.342478 196.646497 794.342478 196.646497 
L 777.659666 196.646497 
Q 775.659666 196.646497 775.659666 198.646497 
L 775.659666 207.777747 
Q 775.659666 209.777747 777.659666 209.777747 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 131 -->
    <g style="fill: #ffffff" transform="translate(777.659666 205.80431) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-33" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_31">
    <!-- 0.92 -->
    <g transform="translate(777.762208 207.150133) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_32">
    <g id="patch_42">
     <path d="M 768.744256 212.95872 
L 785.427068 212.95872 
Q 787.427068 212.95872 787.427068 210.95872 
L 787.427068 201.82747 
Q 787.427068 199.82747 785.427068 199.82747 
L 768.744256 199.82747 
Q 766.744256 199.82747 766.744256 201.82747 
L 766.744256 210.95872 
Q 766.744256 212.95872 768.744256 212.95872 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 121 -->
    <g style="fill: #ffffff" transform="translate(768.744256 208.985283) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_33">
    <g id="patch_43">
     <path d="M 768.447716 215.342155 
L 784.580529 215.342155 
Q 786.580529 215.342155 786.580529 213.342155 
L 786.580529 204.210905 
Q 786.580529 202.210905 784.580529 202.210905 
L 768.447716 202.210905 
Q 766.447716 202.210905 766.447716 204.210905 
L 766.447716 213.342155 
Q 766.447716 215.342155 768.447716 215.342155 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 110 -->
    <g style="fill: #ffffff" transform="translate(768.447716 211.368718) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-30" transform="translate(105.730469 0)"/>
    </g>
   </g>
   <g id="text_34">
    <!-- 0.91 -->
    <g transform="translate(768.319486 211.527912) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_35">
    <g id="patch_44">
     <path d="M 760.174935 218.873695 
L 776.857748 218.873695 
Q 778.857748 218.873695 778.857748 216.873695 
L 778.857748 207.742445 
Q 778.857748 205.742445 776.857748 205.742445 
L 760.174935 205.742445 
Q 758.174935 205.742445 758.174935 207.742445 
L 758.174935 216.873695 
Q 758.174935 218.873695 760.174935 218.873695 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 121 -->
    <g style="fill: #ffffff" transform="translate(760.174935 214.900258) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_36">
    <g id="patch_45">
     <path d="M 760.919064 221.359275 
L 777.601877 221.359275 
Q 779.601877 221.359275 779.601877 219.359275 
L 779.601877 210.228025 
Q 779.601877 208.228025 777.601877 208.228025 
L 760.919064 208.228025 
Q 758.919064 208.228025 758.919064 210.228025 
L 758.919064 219.359275 
Q 758.919064 221.359275 760.919064 221.359275 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 109 -->
    <g style="fill: #ffffff" transform="translate(760.919064 217.385838) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_37">
    <!-- 0.90 -->
    <g transform="translate(760.82133 216.703515) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-30" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_38">
    <g id="patch_46">
     <path d="M 755.336005 228.093507 
L 772.018817 228.093507 
Q 774.018817 228.093507 774.018817 226.093507 
L 774.018817 216.962257 
Q 774.018817 214.962257 772.018817 214.962257 
L 755.336005 214.962257 
Q 753.336005 214.962257 753.336005 216.962257 
L 753.336005 226.093507 
Q 753.336005 228.093507 755.336005 228.093507 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 121 -->
    <g style="fill: #ffffff" transform="translate(755.336005 224.12007) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_39">
    <g id="patch_47">
     <path d="M 756.893576 229.856331 
L 773.026388 229.856331 
Q 775.026388 229.856331 775.026388 227.856331 
L 775.026388 218.725081 
Q 775.026388 216.725081 773.026388 216.725081 
L 756.893576 216.725081 
Q 754.893576 216.725081 754.893576 218.725081 
L 754.893576 227.856331 
Q 754.893576 229.856331 756.893576 229.856331 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 112 -->
    <g style="fill: #ffffff" transform="translate(756.893576 225.882893) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(105.730469 0)"/>
    </g>
   </g>
   <g id="text_40">
    <!-- 0.93 -->
    <g transform="translate(756.587266 224.770851) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-33" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_41">
    <g id="patch_48">
     <path d="M 755.336005 238.506008 
L 772.018817 238.506008 
Q 774.018817 238.506008 774.018817 236.506008 
L 774.018817 227.374758 
Q 774.018817 225.374758 772.018817 225.374758 
L 755.336005 225.374758 
Q 753.336005 225.374758 753.336005 227.374758 
L 753.336005 236.506008 
Q 753.336005 238.506008 755.336005 238.506008 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 121 -->
    <g style="fill: #ffffff" transform="translate(755.336005 234.532571) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_42">
    <g id="patch_49">
     <path d="M 758.01203 239.415771 
L 773.594843 239.415771 
Q 775.594843 239.415771 775.594843 237.415771 
L 775.594843 228.284521 
Q 775.594843 226.284521 773.594843 226.284521 
L 758.01203 226.284521 
Q 756.01203 226.284521 756.01203 228.284521 
L 756.01203 237.415771 
Q 756.01203 239.415771 758.01203 239.415771 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 111 -->
    <g style="fill: #ffffff" transform="translate(758.01203 235.442334) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(100.230469 0)"/>
    </g>
   </g>
   <g id="text_43">
    <!-- 0.92 -->
    <g transform="translate(756.587266 233.881789) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_44">
    <g id="patch_50">
     <path d="M 760.174935 247.72582 
L 776.857748 247.72582 
Q 778.857748 247.72582 778.857748 245.72582 
L 778.857748 236.59457 
Q 778.857748 234.59457 776.857748 234.59457 
L 760.174935 234.59457 
Q 758.174935 234.59457 758.174935 236.59457 
L 758.174935 245.72582 
Q 758.174935 247.72582 760.174935 247.72582 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 121 -->
    <g style="fill: #ffffff" transform="translate(760.174935 243.752383) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_45">
    <g id="patch_51">
     <path d="M 762.629294 247.671683 
L 778.762107 247.671683 
Q 780.762107 247.671683 780.762107 245.671683 
L 780.762107 236.540433 
Q 780.762107 234.540433 778.762107 234.540433 
L 762.629294 234.540433 
Q 760.629294 234.540433 760.629294 236.540433 
L 760.629294 245.671683 
Q 760.629294 247.671683 762.629294 247.671683 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 112 -->
    <g style="fill: #ffffff" transform="translate(762.629294 243.698245) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(105.730469 0)"/>
    </g>
   </g>
   <g id="text_46">
    <!-- 0.93 -->
    <g transform="translate(760.82133 241.949125) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-33" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_47">
    <g id="patch_52">
     <path d="M 768.744256 253.640795 
L 785.427068 253.640795 
Q 787.427068 253.640795 787.427068 251.640795 
L 787.427068 242.509545 
Q 787.427068 240.509545 785.427068 240.509545 
L 768.744256 240.509545 
Q 766.744256 240.509545 766.744256 242.509545 
L 766.744256 251.640795 
Q 766.744256 253.640795 768.744256 253.640795 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 121 -->
    <g style="fill: #ffffff" transform="translate(768.744256 249.667357) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_48">
    <g id="patch_53">
     <path d="M 771.027569 252.235769 
L 787.160382 252.235769 
Q 789.160382 252.235769 789.160382 250.235769 
L 789.160382 241.104519 
Q 789.160382 239.104519 787.160382 239.104519 
L 771.027569 239.104519 
Q 769.027569 239.104519 769.027569 241.104519 
L 769.027569 250.235769 
Q 769.027569 252.235769 771.027569 252.235769 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 110 -->
    <g style="fill: #ffffff" transform="translate(771.027569 248.262332) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-30" transform="translate(105.730469 0)"/>
    </g>
   </g>
   <g id="text_49">
    <!-- 0.91 -->
    <g transform="translate(768.319486 247.124728) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_50">
    <g id="patch_54">
     <path d="M 779.080838 254.895883 
L 795.763651 254.895883 
Q 797.763651 254.895883 797.763651 252.895883 
L 797.763651 243.764633 
Q 797.763651 241.764633 795.763651 241.764633 
L 779.080838 241.764633 
Q 777.080838 241.764633 777.080838 243.764633 
L 777.080838 252.895883 
Q 777.080838 254.895883 779.080838 254.895883 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 121 -->
    <g style="fill: #ffffff" transform="translate(779.080838 250.922446) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_51">
    <g id="patch_55">
     <path d="M 780.790232 252.895019 
L 796.373044 252.895019 
Q 798.373044 252.895019 798.373044 250.895019 
L 798.373044 241.763769 
Q 798.373044 239.763769 796.373044 239.763769 
L 780.790232 239.763769 
Q 778.790232 239.763769 778.790232 241.763769 
L 778.790232 250.895019 
Q 778.790232 252.895019 780.790232 252.895019 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 111 -->
    <g style="fill: #ffffff" transform="translate(780.790232 248.921581) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(100.230469 0)"/>
    </g>
   </g>
   <g id="text_52">
    <!-- 0.92 -->
    <g transform="translate(777.363995 248.22293) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-32" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_53">
    <g id="patch_56">
     <path d="M 788.88743 250.907629 
L 805.020242 250.907629 
Q 807.020242 250.907629 807.020242 248.907629 
L 807.020242 239.776379 
Q 807.020242 237.776379 805.020242 237.776379 
L 788.88743 237.776379 
Q 786.88743 237.776379 786.88743 239.776379 
L 786.88743 248.907629 
Q 786.88743 250.907629 788.88743 250.907629 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 119 -->
    <g style="fill: #ffffff" transform="translate(788.88743 246.934191) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(105.730469 0)"/>
    </g>
   </g>
   <g id="text_54">
    <g id="patch_57">
     <path d="M 788.576823 248.471644 
L 805.259635 248.471644 
Q 807.259635 248.471644 807.259635 246.471644 
L 807.259635 237.340394 
Q 807.259635 235.340394 805.259635 235.340394 
L 788.576823 235.340394 
Q 786.576823 235.340394 786.576823 237.340394 
L 786.576823 246.471644 
Q 786.576823 248.471644 788.576823 248.471644 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 108 -->
    <g style="fill: #ffffff" transform="translate(788.576823 244.498206) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-38" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_55">
    <!-- 0.91 -->
    <g transform="translate(785.704138 244.733207) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_56">
    <g id="patch_58">
     <path d="M 795.359672 243.075478 
L 811.492485 243.075478 
Q 813.492485 243.075478 813.492485 241.075478 
L 813.492485 231.944228 
Q 813.492485 229.944228 811.492485 229.944228 
L 795.359672 229.944228 
Q 793.359672 229.944228 793.359672 231.944228 
L 793.359672 241.075478 
Q 793.359672 243.075478 795.359672 243.075478 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 117 -->
    <g style="fill: #ffffff" transform="translate(795.359672 239.10204) scale(0.1 -0.1)">
     <defs>
      <path id="Arial-BoldMT-37" d="M 272 3703 
L 272 4519 
L 3275 4519 
L 3275 3881 
Q 2903 3516 2518 2831 
Q 2134 2147 1932 1376 
Q 1731 606 1734 0 
L 888 0 
Q 909 950 1279 1937 
Q 1650 2925 2269 3703 
L 272 3703 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(50.115234 0)"/>
     <use xlink:href="#Arial-BoldMT-37" transform="translate(105.730469 0)"/>
    </g>
   </g>
   <g id="text_57">
    <g id="patch_59">
     <path d="M 794.074842 241.029929 
L 810.757654 241.029929 
Q 812.757654 241.029929 812.757654 239.029929 
L 812.757654 229.898679 
Q 812.757654 227.898679 810.757654 227.898679 
L 794.074842 227.898679 
Q 792.074842 227.898679 792.074842 229.898679 
L 792.074842 239.029929 
Q 792.074842 241.029929 794.074842 241.029929 
z
" style="fill: #1a1a1a; fill-opacity: 0.5"/>
    </g>
    <!-- 107 -->
    <g style="fill: #ffffff" transform="translate(794.074842 237.056492) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-31"/>
     <use xlink:href="#Arial-BoldMT-30" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-37" transform="translate(111.230469 0)"/>
    </g>
   </g>
   <g id="text_58">
    <!-- 0.91 -->
    <g transform="translate(791.36735 237.880075) scale(0.1 -0.1)">
     <use xlink:href="#Arial-BoldMT-30"/>
     <use xlink:href="#Arial-BoldMT-2e" transform="translate(55.615234 0)"/>
     <use xlink:href="#Arial-BoldMT-39" transform="translate(83.398438 0)"/>
     <use xlink:href="#Arial-BoldMT-31" transform="translate(139.013672 0)"/>
    </g>
   </g>
   <g id="text_59">
    <!-- Time Series Data Visualization -->
    <g transform="translate(676.31125 18.6525) scale(0.16 -0.16)">
     <defs>
      <path id="ArialMT-54" d="M 1659 0 
L 1659 4041 
L 150 4041 
L 150 4581 
L 3781 4581 
L 3781 4041 
L 2266 4041 
L 2266 0 
L 1659 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-69" d="M 425 3934 
L 425 4581 
L 988 4581 
L 988 3934 
L 425 3934 
z
M 425 0 
L 425 3319 
L 988 3319 
L 988 0 
L 425 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-6d" d="M 422 0 
L 422 3319 
L 925 3319 
L 925 2853 
Q 1081 3097 1340 3245 
Q 1600 3394 1931 3394 
Q 2300 3394 2536 3241 
Q 2772 3088 2869 2813 
Q 3263 3394 3894 3394 
Q 4388 3394 4653 3120 
Q 4919 2847 4919 2278 
L 4919 0 
L 4359 0 
L 4359 2091 
Q 4359 2428 4304 2576 
Q 4250 2725 4106 2815 
Q 3963 2906 3769 2906 
Q 3419 2906 3187 2673 
Q 2956 2441 2956 1928 
L 2956 0 
L 2394 0 
L 2394 2156 
Q 2394 2531 2256 2718 
Q 2119 2906 1806 2906 
Q 1569 2906 1367 2781 
Q 1166 2656 1075 2415 
Q 984 2175 984 1722 
L 984 0 
L 422 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-65" d="M 2694 1069 
L 3275 997 
Q 3138 488 2766 206 
Q 2394 -75 1816 -75 
Q 1088 -75 661 373 
Q 234 822 234 1631 
Q 234 2469 665 2931 
Q 1097 3394 1784 3394 
Q 2450 3394 2872 2941 
Q 3294 2488 3294 1666 
Q 3294 1616 3291 1516 
L 816 1516 
Q 847 969 1125 678 
Q 1403 388 1819 388 
Q 2128 388 2347 550 
Q 2566 713 2694 1069 
z
M 847 1978 
L 2700 1978 
Q 2663 2397 2488 2606 
Q 2219 2931 1791 2931 
Q 1403 2931 1139 2672 
Q 875 2413 847 1978 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-20" transform="scale(0.015625)"/>
      <path id="ArialMT-53" d="M 288 1472 
L 859 1522 
Q 900 1178 1048 958 
Q 1197 738 1509 602 
Q 1822 466 2213 466 
Q 2559 466 2825 569 
Q 3091 672 3220 851 
Q 3350 1031 3350 1244 
Q 3350 1459 3225 1620 
Q 3100 1781 2813 1891 
Q 2628 1963 1997 2114 
Q 1366 2266 1113 2400 
Q 784 2572 623 2826 
Q 463 3081 463 3397 
Q 463 3744 659 4045 
Q 856 4347 1234 4503 
Q 1613 4659 2075 4659 
Q 2584 4659 2973 4495 
Q 3363 4331 3572 4012 
Q 3781 3694 3797 3291 
L 3216 3247 
Q 3169 3681 2898 3903 
Q 2628 4125 2100 4125 
Q 1550 4125 1298 3923 
Q 1047 3722 1047 3438 
Q 1047 3191 1225 3031 
Q 1400 2872 2139 2705 
Q 2878 2538 3153 2413 
Q 3553 2228 3743 1945 
Q 3934 1663 3934 1294 
Q 3934 928 3725 604 
Q 3516 281 3123 101 
Q 2731 -78 2241 -78 
Q 1619 -78 1198 103 
Q 778 284 539 648 
Q 300 1013 288 1472 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-72" d="M 416 0 
L 416 3319 
L 922 3319 
L 922 2816 
Q 1116 3169 1280 3281 
Q 1444 3394 1641 3394 
Q 1925 3394 2219 3213 
L 2025 2691 
Q 1819 2813 1613 2813 
Q 1428 2813 1281 2702 
Q 1134 2591 1072 2394 
Q 978 2094 978 1738 
L 978 0 
L 416 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-73" d="M 197 991 
L 753 1078 
Q 800 744 1014 566 
Q 1228 388 1613 388 
Q 2000 388 2187 545 
Q 2375 703 2375 916 
Q 2375 1106 2209 1216 
Q 2094 1291 1634 1406 
Q 1016 1563 777 1677 
Q 538 1791 414 1992 
Q 291 2194 291 2438 
Q 291 2659 392 2848 
Q 494 3038 669 3163 
Q 800 3259 1026 3326 
Q 1253 3394 1513 3394 
Q 1903 3394 2198 3281 
Q 2494 3169 2634 2976 
Q 2775 2784 2828 2463 
L 2278 2388 
Q 2241 2644 2061 2787 
Q 1881 2931 1553 2931 
Q 1166 2931 1000 2803 
Q 834 2675 834 2503 
Q 834 2394 903 2306 
Q 972 2216 1119 2156 
Q 1203 2125 1616 2013 
Q 2213 1853 2448 1751 
Q 2684 1650 2818 1456 
Q 2953 1263 2953 975 
Q 2953 694 2789 445 
Q 2625 197 2315 61 
Q 2006 -75 1616 -75 
Q 969 -75 630 194 
Q 291 463 197 991 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-44" d="M 494 0 
L 494 4581 
L 2072 4581 
Q 2606 4581 2888 4516 
Q 3281 4425 3559 4188 
Q 3922 3881 4101 3404 
Q 4281 2928 4281 2316 
Q 4281 1794 4159 1391 
Q 4038 988 3847 723 
Q 3656 459 3429 307 
Q 3203 156 2883 78 
Q 2563 0 2147 0 
L 494 0 
z
M 1100 541 
L 2078 541 
Q 2531 541 2789 625 
Q 3047 709 3200 863 
Q 3416 1078 3536 1442 
Q 3656 1806 3656 2325 
Q 3656 3044 3420 3430 
Q 3184 3816 2847 3947 
Q 2603 4041 2063 4041 
L 1100 4041 
L 1100 541 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-61" d="M 2588 409 
Q 2275 144 1986 34 
Q 1697 -75 1366 -75 
Q 819 -75 525 192 
Q 231 459 231 875 
Q 231 1119 342 1320 
Q 453 1522 633 1644 
Q 813 1766 1038 1828 
Q 1203 1872 1538 1913 
Q 2219 1994 2541 2106 
Q 2544 2222 2544 2253 
Q 2544 2597 2384 2738 
Q 2169 2928 1744 2928 
Q 1347 2928 1158 2789 
Q 969 2650 878 2297 
L 328 2372 
Q 403 2725 575 2942 
Q 747 3159 1072 3276 
Q 1397 3394 1825 3394 
Q 2250 3394 2515 3294 
Q 2781 3194 2906 3042 
Q 3031 2891 3081 2659 
Q 3109 2516 3109 2141 
L 3109 1391 
Q 3109 606 3145 398 
Q 3181 191 3288 0 
L 2700 0 
Q 2613 175 2588 409 
z
M 2541 1666 
Q 2234 1541 1622 1453 
Q 1275 1403 1131 1340 
Q 988 1278 909 1158 
Q 831 1038 831 891 
Q 831 666 1001 516 
Q 1172 366 1500 366 
Q 1825 366 2078 508 
Q 2331 650 2450 897 
Q 2541 1088 2541 1459 
L 2541 1666 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-74" d="M 1650 503 
L 1731 6 
Q 1494 -44 1306 -44 
Q 1000 -44 831 53 
Q 663 150 594 308 
Q 525 466 525 972 
L 525 2881 
L 113 2881 
L 113 3319 
L 525 3319 
L 525 4141 
L 1084 4478 
L 1084 3319 
L 1650 3319 
L 1650 2881 
L 1084 2881 
L 1084 941 
Q 1084 700 1114 631 
Q 1144 563 1211 522 
Q 1278 481 1403 481 
Q 1497 481 1650 503 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-56" d="M 1803 0 
L 28 4581 
L 684 4581 
L 1875 1253 
Q 2019 853 2116 503 
Q 2222 878 2363 1253 
L 3600 4581 
L 4219 4581 
L 2425 0 
L 1803 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-75" d="M 2597 0 
L 2597 488 
Q 2209 -75 1544 -75 
Q 1250 -75 995 37 
Q 741 150 617 320 
Q 494 491 444 738 
Q 409 903 409 1263 
L 409 3319 
L 972 3319 
L 972 1478 
Q 972 1038 1006 884 
Q 1059 663 1231 536 
Q 1403 409 1656 409 
Q 1909 409 2131 539 
Q 2353 669 2445 892 
Q 2538 1116 2538 1541 
L 2538 3319 
L 3100 3319 
L 3100 0 
L 2597 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-6c" d="M 409 0 
L 409 4581 
L 972 4581 
L 972 0 
L 409 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-7a" d="M 125 0 
L 125 456 
L 2238 2881 
Q 1878 2863 1603 2863 
L 250 2863 
L 250 3319 
L 2963 3319 
L 2963 2947 
L 1166 841 
L 819 456 
Q 1197 484 1528 484 
L 3063 484 
L 3063 0 
L 125 0 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-6f" d="M 213 1659 
Q 213 2581 725 3025 
Q 1153 3394 1769 3394 
Q 2453 3394 2887 2945 
Q 3322 2497 3322 1706 
Q 3322 1066 3130 698 
Q 2938 331 2570 128 
Q 2203 -75 1769 -75 
Q 1072 -75 642 372 
Q 213 819 213 1659 
z
M 791 1659 
Q 791 1022 1069 705 
Q 1347 388 1769 388 
Q 2188 388 2466 706 
Q 2744 1025 2744 1678 
Q 2744 2294 2464 2611 
Q 2184 2928 1769 2928 
Q 1347 2928 1069 2612 
Q 791 2297 791 1659 
z
" transform="scale(0.015625)"/>
      <path id="ArialMT-6e" d="M 422 0 
L 422 3319 
L 928 3319 
L 928 2847 
Q 1294 3394 1984 3394 
Q 2284 3394 2536 3286 
Q 2788 3178 2913 3003 
Q 3038 2828 3088 2588 
Q 3119 2431 3119 2041 
L 3119 0 
L 2556 0 
L 2556 2019 
Q 2556 2363 2490 2533 
Q 2425 2703 2258 2804 
Q 2091 2906 1866 2906 
Q 1506 2906 1245 2678 
Q 984 2450 984 1813 
L 984 0 
L 422 0 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#ArialMT-54"/>
     <use xlink:href="#ArialMT-69" transform="translate(57.333984 0)"/>
     <use xlink:href="#ArialMT-6d" transform="translate(79.550781 0)"/>
     <use xlink:href="#ArialMT-65" transform="translate(162.851562 0)"/>
     <use xlink:href="#ArialMT-20" transform="translate(218.466797 0)"/>
     <use xlink:href="#ArialMT-53" transform="translate(246.25 0)"/>
     <use xlink:href="#ArialMT-65" transform="translate(312.949219 0)"/>
     <use xlink:href="#ArialMT-72" transform="translate(368.564453 0)"/>
     <use xlink:href="#ArialMT-69" transform="translate(401.865234 0)"/>
     <use xlink:href="#ArialMT-65" transform="translate(424.082031 0)"/>
     <use xlink:href="#ArialMT-73" transform="translate(479.697266 0)"/>
     <use xlink:href="#ArialMT-20" transform="translate(529.697266 0)"/>
     <use xlink:href="#ArialMT-44" transform="translate(557.480469 0)"/>
     <use xlink:href="#ArialMT-61" transform="translate(629.697266 0)"/>
     <use xlink:href="#ArialMT-74" transform="translate(685.3125 0)"/>
     <use xlink:href="#ArialMT-61" transform="translate(713.095703 0)"/>
     <use xlink:href="#ArialMT-20" transform="translate(768.710938 0)"/>
     <use xlink:href="#ArialMT-56" transform="translate(796.494141 0)"/>
     <use xlink:href="#ArialMT-69" transform="translate(861.443359 0)"/>
     <use xlink:href="#ArialMT-73" transform="translate(883.660156 0)"/>
     <use xlink:href="#ArialMT-75" transform="translate(933.660156 0)"/>
     <use xlink:href="#ArialMT-61" transform="translate(989.275391 0)"/>
     <use xlink:href="#ArialMT-6c" transform="translate(1044.890625 0)"/>
     <use xlink:href="#ArialMT-69" transform="translate(1067.107422 0)"/>
     <use xlink:href="#ArialMT-7a" transform="translate(1089.324219 0)"/>
     <use xlink:href="#ArialMT-61" transform="translate(1139.324219 0)"/>
     <use xlink:href="#ArialMT-74" transform="translate(1194.939453 0)"/>
     <use xlink:href="#ArialMT-69" transform="translate(1222.722656 0)"/>
     <use xlink:href="#ArialMT-6f" transform="translate(1244.939453 0)"/>
     <use xlink:href="#ArialMT-6e" transform="translate(1300.554688 0)"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_60">
     <path d="M 837.369758 82.20125 
L 964.481633 82.20125 
Q 966.881633 82.20125 966.881633 79.80125 
L 966.881633 47.0525 
Q 966.881633 44.6525 964.481633 44.6525 
L 837.369758 44.6525 
Q 834.969758 44.6525 834.969758 47.0525 
L 834.969758 79.80125 
Q 834.969758 82.20125 837.369758 82.20125 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_61">
     <path d="M 839.769758 58.041875 
L 863.769758 58.041875 
L 863.769758 49.641875 
L 839.769758 49.641875 
z
" style="fill: #2f974e; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <g id="text_60">
     <!-- Real values -->
     <g transform="translate(873.369758 58.041875) scale(0.12 -0.12)">
      <defs>
       <path id="ArialMT-52" d="M 503 0 
L 503 4581 
L 2534 4581 
Q 3147 4581 3465 4457 
Q 3784 4334 3975 4021 
Q 4166 3709 4166 3331 
Q 4166 2844 3850 2509 
Q 3534 2175 2875 2084 
Q 3116 1969 3241 1856 
Q 3506 1613 3744 1247 
L 4541 0 
L 3778 0 
L 3172 953 
Q 2906 1366 2734 1584 
Q 2563 1803 2427 1890 
Q 2291 1978 2150 2013 
Q 2047 2034 1813 2034 
L 1109 2034 
L 1109 0 
L 503 0 
z
M 1109 2559 
L 2413 2559 
Q 2828 2559 3062 2645 
Q 3297 2731 3419 2920 
Q 3541 3109 3541 3331 
Q 3541 3656 3305 3865 
Q 3069 4075 2559 4075 
L 1109 4075 
L 1109 2559 
z
" transform="scale(0.015625)"/>
       <path id="ArialMT-76" d="M 1344 0 
L 81 3319 
L 675 3319 
L 1388 1331 
Q 1503 1009 1600 663 
Q 1675 925 1809 1294 
L 2547 3319 
L 3125 3319 
L 1869 0 
L 1344 0 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#ArialMT-52"/>
      <use xlink:href="#ArialMT-65" transform="translate(72.216797 0)"/>
      <use xlink:href="#ArialMT-61" transform="translate(127.832031 0)"/>
      <use xlink:href="#ArialMT-6c" transform="translate(183.447266 0)"/>
      <use xlink:href="#ArialMT-20" transform="translate(205.664062 0)"/>
      <use xlink:href="#ArialMT-76" transform="translate(233.447266 0)"/>
      <use xlink:href="#ArialMT-61" transform="translate(283.447266 0)"/>
      <use xlink:href="#ArialMT-6c" transform="translate(339.0625 0)"/>
      <use xlink:href="#ArialMT-75" transform="translate(361.279297 0)"/>
      <use xlink:href="#ArialMT-65" transform="translate(416.894531 0)"/>
      <use xlink:href="#ArialMT-73" transform="translate(472.509766 0)"/>
     </g>
    </g>
    <g id="patch_62">
     <path d="M 839.769758 75.01625 
L 863.769758 75.01625 
L 863.769758 66.61625 
L 839.769758 66.61625 
z
" style="fill: #2e7ebc; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <g id="text_61">
     <!-- Predicted values -->
     <g transform="translate(873.369758 75.01625) scale(0.12 -0.12)">
      <defs>
       <path id="ArialMT-50" d="M 494 0 
L 494 4581 
L 2222 4581 
Q 2678 4581 2919 4538 
Q 3256 4481 3484 4323 
Q 3713 4166 3852 3881 
Q 3991 3597 3991 3256 
Q 3991 2672 3619 2267 
Q 3247 1863 2275 1863 
L 1100 1863 
L 1100 0 
L 494 0 
z
M 1100 2403 
L 2284 2403 
Q 2872 2403 3119 2622 
Q 3366 2841 3366 3238 
Q 3366 3525 3220 3729 
Q 3075 3934 2838 4000 
Q 2684 4041 2272 4041 
L 1100 4041 
L 1100 2403 
z
" transform="scale(0.015625)"/>
       <path id="ArialMT-64" d="M 2575 0 
L 2575 419 
Q 2259 -75 1647 -75 
Q 1250 -75 917 144 
Q 584 363 401 755 
Q 219 1147 219 1656 
Q 219 2153 384 2558 
Q 550 2963 881 3178 
Q 1213 3394 1622 3394 
Q 1922 3394 2156 3267 
Q 2391 3141 2538 2938 
L 2538 4581 
L 3097 4581 
L 3097 0 
L 2575 0 
z
M 797 1656 
Q 797 1019 1065 703 
Q 1334 388 1700 388 
Q 2069 388 2326 689 
Q 2584 991 2584 1609 
Q 2584 2291 2321 2609 
Q 2059 2928 1675 2928 
Q 1300 2928 1048 2622 
Q 797 2316 797 1656 
z
" transform="scale(0.015625)"/>
       <path id="ArialMT-63" d="M 2588 1216 
L 3141 1144 
Q 3050 572 2676 248 
Q 2303 -75 1759 -75 
Q 1078 -75 664 370 
Q 250 816 250 1647 
Q 250 2184 428 2587 
Q 606 2991 970 3192 
Q 1334 3394 1763 3394 
Q 2303 3394 2647 3120 
Q 2991 2847 3088 2344 
L 2541 2259 
Q 2463 2594 2264 2762 
Q 2066 2931 1784 2931 
Q 1359 2931 1093 2626 
Q 828 2322 828 1663 
Q 828 994 1084 691 
Q 1341 388 1753 388 
Q 2084 388 2306 591 
Q 2528 794 2588 1216 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#ArialMT-50"/>
      <use xlink:href="#ArialMT-72" transform="translate(66.699219 0)"/>
      <use xlink:href="#ArialMT-65" transform="translate(100 0)"/>
      <use xlink:href="#ArialMT-64" transform="translate(155.615234 0)"/>
      <use xlink:href="#ArialMT-69" transform="translate(211.230469 0)"/>
      <use xlink:href="#ArialMT-63" transform="translate(233.447266 0)"/>
      <use xlink:href="#ArialMT-74" transform="translate(283.447266 0)"/>
      <use xlink:href="#ArialMT-65" transform="translate(311.230469 0)"/>
      <use xlink:href="#ArialMT-64" transform="translate(366.845703 0)"/>
      <use xlink:href="#ArialMT-20" transform="translate(422.460938 0)"/>
      <use xlink:href="#ArialMT-76" transform="translate(450.244141 0)"/>
      <use xlink:href="#ArialMT-61" transform="translate(500.244141 0)"/>
      <use xlink:href="#ArialMT-6c" transform="translate(555.859375 0)"/>
      <use xlink:href="#ArialMT-75" transform="translate(578.076172 0)"/>
      <use xlink:href="#ArialMT-65" transform="translate(633.691406 0)"/>
      <use xlink:href="#ArialMT-73" transform="translate(689.306641 0)"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_63">
    <path d="M 7.2 499.4525 
L 1562.4 499.4525 
L 1562.4 436.758622 
L 7.2 436.758622 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image91ce9ae2f4" transform="scale(1 -1) translate(0 -62.88)" x="7.2" y="-436.56" width="1555.2" height="62.88"/>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="mb24748f55e" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb24748f55e" x="162.359165" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_62">
      <!-- 150 -->
      <g transform="translate(152.349477 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-31"/>
       <use xlink:href="#ArialMT-35" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#mb24748f55e" x="342.776798" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_63">
      <!-- 200 -->
      <g transform="translate(332.767111 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-32"/>
       <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#mb24748f55e" x="523.194432" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_64">
      <!-- 250 -->
      <g transform="translate(513.184744 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-32"/>
       <use xlink:href="#ArialMT-35" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#mb24748f55e" x="703.612065" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_65">
      <!-- 300 -->
      <g transform="translate(693.602377 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-33"/>
       <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#mb24748f55e" x="884.029698" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_66">
      <!-- 350 -->
      <g transform="translate(874.020011 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-33"/>
       <use xlink:href="#ArialMT-35" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#mb24748f55e" x="1064.447332" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_67">
      <!-- 400 -->
      <g transform="translate(1054.437644 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-34"/>
       <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#mb24748f55e" x="1244.864965" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_68">
      <!-- 450 -->
      <g transform="translate(1234.855278 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-34"/>
       <use xlink:href="#ArialMT-35" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_8">
      <g>
       <use xlink:href="#mb24748f55e" x="1425.282599" y="499.4525" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_69">
      <!-- 500 -->
      <g transform="translate(1415.272911 515.041875) scale(0.12 -0.12)">
       <use xlink:href="#ArialMT-35"/>
       <use xlink:href="#ArialMT-30" transform="translate(55.615234 0)"/>
       <use xlink:href="#ArialMT-30" transform="translate(111.230469 0)"/>
      </g>
     </g>
    </g>
    <g id="text_70">
     <!-- Value Size -->
     <g transform="translate(756.55875 530.01625) scale(0.12 -0.12)">
      <use xlink:href="#ArialMT-56"/>
      <use xlink:href="#ArialMT-61" transform="translate(59.324219 0)"/>
      <use xlink:href="#ArialMT-6c" transform="translate(114.939453 0)"/>
      <use xlink:href="#ArialMT-75" transform="translate(137.15625 0)"/>
      <use xlink:href="#ArialMT-65" transform="translate(192.771484 0)"/>
      <use xlink:href="#ArialMT-20" transform="translate(248.386719 0)"/>
      <use xlink:href="#ArialMT-53" transform="translate(276.169922 0)"/>
      <use xlink:href="#ArialMT-69" transform="translate(342.869141 0)"/>
      <use xlink:href="#ArialMT-7a" transform="translate(365.085938 0)"/>
      <use xlink:href="#ArialMT-65" transform="translate(415.085938 0)"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2"/>
   <g id="LineCollection_1"/>
   <g id="patch_64">
    <path d="M 7.2 499.4525 
L 7.2 468.105561 
L 7.2 436.758622 
L 1562.4 436.758622 
L 1562.4 468.105561 
L 1562.4 499.4525 
L 7.2 499.4525 
z
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="paa11a24f58">
   <rect x="596.718367" y="38.6525" width="376.163265" height="376.163265"/>
  </clipPath>
 </defs>
</svg>
