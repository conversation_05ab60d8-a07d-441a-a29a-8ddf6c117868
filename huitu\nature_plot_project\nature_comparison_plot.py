import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import matplotlib.patches as patches
from matplotlib.path import Path

# 设置绘图风格，模拟Nature图表风格
plt.style.use('default')
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['xtick.minor.width'] = 0.6
plt.rcParams['ytick.minor.width'] = 0.6
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['xtick.labelsize'] = 8
plt.rcParams['ytick.labelsize'] = 8
plt.rcParams['legend.fontsize'] = 8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 用户提供的数据
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'DT': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [556, 548, 520, 551, 551, 551, 551, 551, 551, 551, 551, 551]
}

# 创建数据框
df = pd.DataFrame(data)

# 计算图表指标
max_value = max(df.max()) * 1.1
min_value = min(df.min()) * 0.9


def create_comparison_plot(df, x_col, title):
    """创建对比图表"""

    # 创建图和子图
    fig = plt.figure(figsize=(12, 6))

    # 左侧子图：真实值
    ax_left = fig.add_subplot(121)

    # 右侧子图：预测值
    ax_right = fig.add_subplot(122, sharey=ax_left)

    # 设置背景颜色
    fig.patch.set_facecolor('white')
    ax_left.set_facecolor('#f7f7f7')
    ax_right.set_facecolor('#f7f7f7')

    # ================ 左侧真实值图表 ================
    # 绘制散点图
    for i, row in df.iterrows():
        ax_left.scatter(0, row['real'], s=50, color='#2ca02c', edgecolors='white', linewidth=0.5, zorder=3, alpha=0.85)

        # 添加日期标签
        ax_left.text(-0.25, row['real'], str(i + 1), ha='right', va='center', fontsize=7)

    # 设置轴标签和标题
    ax_left.set_xlabel('Real Values', fontweight='bold')
    ax_left.set_ylabel('Values', fontweight='bold')
    ax_left.set_title('Real Values', fontsize=10, fontweight='bold')

    # 设置坐标轴范围和刻度
    ax_left.set_xlim(-0.5, 0.5)
    ax_left.set_ylim(min_value, max_value)
    ax_left.set_xticks([0])
    ax_left.set_xticklabels(['Real'])

    # 添加网格线
    ax_left.grid(True, axis='y', linestyle='--', alpha=0.3)

    # ================ 右侧预测值图表 ================
    # 绘制散点图
    for i, row in df.iterrows():
        ax_right.scatter(0, row[x_col], s=50, color='#ff7f0e', edgecolors='white', linewidth=0.5, zorder=3, alpha=0.85)

        # 添加日期标签
        ax_right.text(0.25, row[x_col], str(i + 1), ha='left', va='center', fontsize=7)

    # 设置轴标签和标题
    ax_right.set_xlabel(f'{x_col} Values', fontweight='bold')
    ax_right.set_title(f'{x_col} Values', fontsize=10, fontweight='bold')

    # 设置坐标轴范围和刻度
    ax_right.set_xlim(-0.5, 0.5)
    ax_right.set_xticks([0])
    ax_right.set_xticklabels([x_col])

    # 添加网格线
    ax_right.grid(True, axis='y', linestyle='--', alpha=0.3)

    # 移除右边的y轴刻度标签
    ax_right.tick_params(axis='y', labelleft=False)

    # ================ 添加连接线 ================
    # 为每个日期添加连接线，连接真实值和预测值
    for i, row in df.iterrows():
        # 计算连接点坐标
        x1, y1 = ax_left.transData.transform((0, row['real']))
        x3, y3 = ax_right.transData.transform((0, row[x_col]))

        # 获取中间位置
        x2, y2 = (x1 + x3) / 2, (y1 + y3) / 2

        # 转换为图表坐标
        x1, y1 = fig.transFigure.inverted().transform((x1, y1))
        x2, y2 = fig.transFigure.inverted().transform((x2, y2))
        x3, y3 = fig.transFigure.inverted().transform((x3, y3))

        # 创建贝塞尔曲线路径
        verts = [(x1, y1), (x1 + (x2 - x1) * 0.4, y1), (x2, y2), (x3 - (x3 - x2) * 0.4, y3), (x3, y3)]
        codes = [Path.MOVETO, Path.CURVE4, Path.CURVE4, Path.CURVE4, Path.LINETO]

        # 设置颜色基于差异
        color = '#2ca02c' if abs(row[x_col] - row['real']) < 10 else '#ff7f0e' if abs(
            row[x_col] - row['real']) < 20 else '#d62728'
        alpha = 0.3  # 设置透明度

        # 创建路径
        path = Path(verts, codes)
        patch = patches.PathPatch(path, facecolor='none', edgecolor=color, lw=1, alpha=alpha)
        fig.add_artist(patch)

    # ================ 添加注释和图例 ================
    # 添加解释性文本
    plt.figtext(0.5, 0.02,
                f'The figure shows real values vs. {x_col} across different dates.\n'
                'Green connections indicate small differences, orange moderate, and red large differences.',
                ha='center', fontsize=8, style='italic')

    # 调整布局
    plt.tight_layout(rect=[0, 0.03, 1, 0.93])

    # 保存图像

    plt.savefig(f'{x_col}_comparison_plot.svg', dpi=600, bbox_inches='tight')

    print(f"图表已生成: {x_col}_comparison_plot.pdf 和 {x_col}_comparison_plot.png")
    return fig


# 运行生成图表的函数
if __name__ == "__main__":
    fig1 = create_comparison_plot(df, 'CSAF', 'CSAF vs Real')
    plt.show()
    fig2 = create_comparison_plot(df, 'DT', 'DT vs Real')
    plt.show()
