import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Wedge, Rectangle
import matplotlib.patheffects as path_effects
from matplotlib.ticker import MaxNLocator

# 设置Nature风格的图表参数 (单栏宽度图表，转换为英寸)
width_cm, height_cm = 8.9, 8.9  # Nature单栏宽度
width_inch, height_inch = width_cm/2.54, height_cm/2.54

# 设置专业学术图表参数
plt.rcParams.update({
    'font.family': 'Arial',  # Nature推荐字体
    'font.size': 8,
    'axes.labelsize': 9,
    'xtick.labelsize': 8,
    'ytick.labelsize': 8,
    'legend.fontsize': 7,
    'figure.dpi': 600,
    'figure.figsize': (width_inch, height_inch),
    'savefig.dpi': 600,
    'savefig.bbox': 'tight'
})

# 数据准备
data = {
    'Zone': ['Zone 1', 'Zone 2', 'Zone 3'],
    'CSAF': [10248, 11027, 3294],
    'DT': [8649, 9031, 2779],
    'Real': [10982, 11563, 3589]
}

# 计算准确率
csaf_accuracy = [1 - abs(c - r) / r for c, r in zip(data['CSAF'], data['Real'])]
DT_accuracy = [1 - abs(c - r) / r for c, r in zip(data['DT'], data['Real'])]

# 创建图形和子图
fig = plt.figure(figsize=(width_inch, height_inch))
ax = fig.add_subplot(111, polar=True)

# Nature风格的配色 - 高对比度、清晰可辨
csaf_color = '#3182bd'  # 蓝色
DT_color = '#e6550d'   # 橙色
real_color = '#31a354'  # 绿色
accuracy_cmap = plt.cm.viridis  # 用于准确率可视化的渐变色

# 定义扇形角度间隔
n_zones = len(data['Zone'])
width = 2 * np.pi / n_zones * 0.8  # 扇形宽度
spacing = 2 * np.pi / n_zones * 0.2  # 间隔宽度
max_radius = 1.0  # 最大半径

# 计算每个区域的角度位置
angles = [(i * (width + spacing) + width/2) for i in range(n_zones)]

# 数据归一化 - 使用最大值作为基准
max_value = max(max(data['CSAF']), max(data['DT']), max(data['Real']))
csaf_normalized = [v / max_value * max_radius for v in data['CSAF']]
DT_normalized = [v / max_value * max_radius for v in data['DT']]
real_normalized = [v / max_value * max_radius for v in data['Real']]

# 绘制刻度和网格 - Nature风格的精简网格
tick_positions = np.linspace(0, max_radius, 5)  # 5个刻度
for r in tick_positions:
    circle = plt.Circle((0, 0), r, fill=False, color='#cccccc', 
                       linestyle='-', linewidth=0.5, alpha=0.7)
    ax.add_artist(circle)
    if r > 0:  # 不显示0值
        ax.text(0, r, f"{int(r * max_value)}", 
               va='center', ha='center', fontsize=6, color='#666666',
               bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.9, 
                        edgecolor='none'))

# 准备用于绘制准确率条带的数据
bar_width = 0.15
accuracy_positions = []
for angle in angles:
    # 每个区域有三个条带：CSAF准确率、DT准确率、CSAF-DT差异
    accuracies = [
        (angle - bar_width, bar_width, csaf_accuracy[angles.index(angle)], csaf_color, 'CSAF'),
        (angle, bar_width, DT_accuracy[angles.index(angle)], DT_color, 'DT'),
        (angle + bar_width, bar_width, 
         abs(csaf_accuracy[angles.index(angle)] - DT_accuracy[angles.index(angle)]), 
         '#888888', 'Diff')
    ]
    accuracy_positions.extend(accuracies)

# 创建径向条带（内圈）- 展示准确率和差异
min_radius = 0.2  # 内圈起始位置
max_acc_radius = 0.4  # 准确率条带最大半径

# 绘制区域分隔线
for angle in angles:
    ax.plot([angle - width/2, angle - width/2], [0, 1.3], 
            color='#cccccc', linestyle='-', linewidth=0.5, alpha=0.7)

# 绘制准确率条带（使用半径方向的条带）
for pos, w, acc, color, label in accuracy_positions:
    # 计算条带高度，准确率越高，条带越高
    height = acc * (max_acc_radius - min_radius)
    
    # 开始和结束角度
    start_angle = np.degrees(pos - w/2)
    end_angle = np.degrees(pos + w/2)
    
    # 创建条带
    wedge = Wedge((0, 0), min_radius + height, start_angle, end_angle, 
                 width=height, color=color, alpha=0.7, 
                 edgecolor='white', linewidth=0.5)
    ax.add_patch(wedge)

# 绘制数据点和连线 - 展示三种测量方法的值
for i, angle in enumerate(angles):
    # 绘制实际值点
    real_x = real_normalized[i] * np.cos(angle)
    real_y = real_normalized[i] * np.sin(angle)
    
    # 绘制CSAF值点
    csaf_x = csaf_normalized[i] * np.cos(angle)
    csaf_y = csaf_normalized[i] * np.sin(angle)
    
    # 绘制DT值点
    DT_x = DT_normalized[i] * np.cos(angle)
    DT_y = DT_normalized[i] * np.sin(angle)
    
    # 连接三种测量方法的点 - 形成三角形
    ax.plot([real_x, csaf_x, DT_x, real_x], 
            [real_y, csaf_y, DT_y, real_y], 
            'o-', ms=5, linewidth=1.2, color='#888888', alpha=0.5)
    
    # 为每个点添加不同的标记
    ax.plot(real_x, real_y, 'o', ms=7, color=real_color, mec='white', mew=1)
    ax.plot(csaf_x, csaf_y, 'D', ms=7, color=csaf_color, mec='white', mew=1)
    ax.plot(DT_x, DT_y, 's', ms=7, color=DT_color, mec='white', mew=1)
    
    # 添加数值标签
    for x, y, val, c, offset, marker in [
        (real_x, real_y, data['Real'][i], real_color, 0.08, 'Real'),
        (csaf_x, csaf_y, data['CSAF'][i], csaf_color, 0.08, 'CSAF'),
        (DT_x, DT_y, data['DT'][i], DT_color, 0.08, 'DT')
    ]:
        label_angle = np.arctan2(y, x)
        label_x = (np.sqrt(x**2 + y**2) + offset) * np.cos(label_angle)
        label_y = (np.sqrt(x**2 + y**2) + offset) * np.sin(label_angle)
        
        # 添加值标签
        text = ax.text(label_x, label_y, f"{marker}\n{val}", 
                      fontsize=6, color='#333333', fontweight='bold',
                      ha='center', va='center',
                      bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.9, 
                               edgecolor=c, linewidth=1.5))
        
        # 添加文本阴影效果
        text.set_path_effects([
            path_effects.withStroke(linewidth=2, foreground='white')
        ])

# 添加区域标签
for i, angle in enumerate(angles):
    # 区域标签位置
    label_distance = 1.22
    zone_name = data['Zone'][i]
    
    # 放置区域标签
    label_x = label_distance * np.cos(angle)
    label_y = label_distance * np.sin(angle)
    
    # 创建背景矩形
    text = ax.text(label_x, label_y, zone_name, fontsize=9, fontweight='bold', 
                  ha='center', va='center',
                  bbox=dict(boxstyle='round,pad=0.3', facecolor='#f0f0f0', 
                           alpha=0.9, edgecolor='#cccccc', linewidth=1))
    
    # 添加文本阴影效果
    text.set_path_effects([
        path_effects.withStroke(linewidth=3, foreground='white')
    ])

# 添加准确率图例
accuracy_legend_elements = []
for title, color in [('CSAF Accuracy', csaf_color), 
                    ('DT Accuracy', DT_color), 
                    ('|CSAF-DT| Diff', '#888888')]:
    accuracy_legend_elements.append(
        Rectangle((0, 0), 1, 1, color=color, alpha=0.7, label=title)
    )

# 添加数据点图例
data_legend_elements = [
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=real_color, 
              markersize=8, markeredgecolor='white', markeredgewidth=1, label='Real Value'),
    plt.Line2D([0], [0], marker='D', color='w', markerfacecolor=csaf_color, 
              markersize=8, markeredgecolor='white', markeredgewidth=1, label='CSAF Value'),
    plt.Line2D([0], [0], marker='s', color='w', markerfacecolor=DT_color, 
              markersize=8, markeredgecolor='white', markeredgewidth=1, label='DT Value')
]

# 创建两行图例
first_legend = ax.legend(handles=accuracy_legend_elements, 
                        loc='upper center', bbox_to_anchor=(0.5, -0.02), 
                        ncol=3, frameon=True, framealpha=0.9,
                        edgecolor='#cccccc', fontsize=7)
ax.add_artist(first_legend)

second_legend = ax.legend(handles=data_legend_elements, 
                         loc='upper center', bbox_to_anchor=(0.5, -0.12), 
                         ncol=3, frameon=True, framealpha=0.9,
                         edgecolor='#cccccc', fontsize=7)

# 美化极坐标图 - Nature风格的简洁设计
ax.set_ylim(0, 1.3)
ax.set_yticks([])  # 隐藏径向刻度
ax.set_xticks([])  # 隐藏角度刻度
ax.spines['polar'].set_visible(False)  # 隐藏外圈线
ax.grid(False)  # 隐藏网格

# 添加标题
title = fig.suptitle('Multi-dimensional Comparison of Zone Measurements', 
                    fontsize=11, fontweight='bold', y=0.98)
title.set_path_effects([
    path_effects.withStroke(linewidth=3, foreground='white', alpha=0.8)
])

# 添加副标题
fig.text(0.5, 0.93, 'Values, accuracies and differences across zones', 
        fontsize=8, color='#666666', ha='center', va='center')

# 保存图像
plt.savefig('Nature_Style_Polar_Chart.png', format='png', bbox_inches='tight', dpi=600)
plt.savefig('Nature_Style_Polar_Chart.pdf', format='pdf', bbox_inches='tight', dpi=600)
plt.savefig('Nature_Style_Polar_Chart.svg', format='svg', bbox_inches='tight', dpi=600)

plt.show() 