import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体（如果需要）
plt.rcParams.update({
    'font.family': 'Arial',
    'font.size': 18,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 11,
    'ytick.labelsize': 11
})

# 数据
dates = ['0809', '0905', '0909', '0926', '1009', '1024', '1031', '1108', '1115', '1128', '1218', '0124', '0206']
real_values = [538, 538, 180, 142, 121, 121, 121, 121, 121, 121, 121, 119, 117]
pred_values = [519, 514, 169, 131, 107, 112, 110, 104, 109, 115, 113, 108, 107]

# 创建图表
fig, axes = plt.subplots(1, len(dates), figsize=(20, 3))
fig.patch.set_facecolor('white')

# 计算准确率
accuracies = [pred/real if real != 0 else 0 for pred, real in zip(pred_values, real_values)]

# 设置颜色映射
cmap = sns.color_palette("viridis", as_cmap=True)

# 找到最大值用于归一化
max_value = max(max(real_values), max(pred_values))

for i, (date, real, pred, acc) in enumerate(zip(dates, real_values, pred_values, accuracies)):
    # 创建每个日期的小型热图数据
    data = np.array([[real, pred]])
    
    # 绘制热图
    sns.heatmap(data, 
                ax=axes[i],
                cmap=cmap,
                vmin=0,
                vmax=max_value,
                cbar=False,
                xticklabels=False,
                yticklabels=False)
    
    # 添加文本标注
    axes[i].text(0.5, -0.2, f"{date}", ha='center', va='center', transform=axes[i].transAxes)
    axes[i].text(0.5, -0.4, f"Real:{real}", ha='center', va='center', transform=axes[i].transAxes)
    axes[i].text(0.5, -0.6, f"Pred:{pred}", ha='center', va='center', transform=axes[i].transAxes)
    axes[i].text(0.5, -0.8, f"Acc:{acc:.2f}", ha='center', va='center', transform=axes[i].transAxes)
    
    # 移除边框
    axes[i].set_frame_on(False)

# 调整布局
plt.subplots_adjust(bottom=0.3, wspace=0.1)

# 添加颜色条
cax = fig.add_axes([0.92, 0.3, 0.01, 0.4])
sm = plt.cm.ScalarMappable(cmap=cmap, norm=plt.Normalize(0, max_value))
plt.colorbar(sm, cax=cax, label='Value')

# 保存图表
plt.savefig('time_series_heatmap.png', dpi=300, bbox_inches='tight', pad_inches=0.5)
plt.savefig('time_series_heatmap.pdf', format='pdf', bbox_inches='tight', pad_inches=0.5)

plt.show() 