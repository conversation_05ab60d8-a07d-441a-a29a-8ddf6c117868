import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# --- 设置图表参数 ---
# Use Times New Roman font if available
try:
    plt.rcParams['font.family'] = 'Times New Roman'
except RuntimeError:
    print("Times New Roman font not found, using default.")

plt.rcParams.update({
    'font.size': 10,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# --- 数据准备 ---
# Data extracted from the user's image
models = [
    'CSAF', 'Cascade Mask R-CNN', 'Swin Transformer', 'Hybrid Task Cascade',
    'Mask R-CNN', 'YOLOv5', 'Yolact', 'QueryInst', 'Solov2'
]
metrics = ['AP', 'AP50', 'AP75', 'API']

data = np.array([
    [35.4, 76.7, 27.5, 35.4], # CSAF
    [29.3, 67.8, 24.6, 29.5], # Cascade Mask R-CNN
    [27.1, 59.4, 24.3, 27.3], # Swin Transformer
    [22.0, 53.5, 16.3, 22.3], # Hybrid Task Cascade
    [30.2, 63.9, 24.1, 30.1], # Mask R-CNN
    [18.3, 48.9, 11.0, 18.4], # YOLOv5
    [22.1, 53.0, 15.3, 22.2], # Yolact
    [13.8, 43.8, 6.2, 13.9],  # QueryInst
    [11.4, 39.6, 3.1, 11.4]   # Solov2
])

# Create DataFrame
df = pd.DataFrame(data, index=models, columns=metrics)

# --- 创建热力图 ---
plt.figure(figsize=(8, 6)) # Adjust figure size as needed

ax = sns.heatmap(
    df,
    annot=True,          # Show the values in the cells
    fmt=".1f",           # Format values to one decimal place
    cmap="Greens",       # Use a green color map (higher score = darker green)
    linewidths=0.5,      # Add lines between cells
    linecolor='lightgray', # Color of the lines
    cbar=True,           # Show the color bar
    cbar_kws={'label': 'Score'} # Label for the color bar
)

# --- 定制外观 ---
ax.set_title('Model Performance Comparison', fontsize=14, fontweight='bold', pad=15)
ax.set_xlabel('Metrics', fontsize=12, fontweight='bold')
ax.set_ylabel('Models', fontsize=12, fontweight='bold')

# Ensure labels are not cut off
plt.xticks(rotation=0) # Keep metric labels horizontal
plt.yticks(rotation=0) # Keep model labels horizontal

# Adjust layout
plt.tight_layout()

# --- 保存图像 ---
plt.savefig('Model_Performance_Heatmap.png', format='png')
plt.savefig('Model_Performance_Heatmap.svg', format='svg')
plt.savefig('Model_Performance_Heatmap.pdf', format='pdf')

plt.show() 