import vegetation_index_plot as vip
import argparse

def main():
    parser = argparse.ArgumentParser(description='植被指数图表生成工具')
    parser.add_argument('--matrix', action='store_true', help='生成植被指数时间序列图矩阵')
    parser.add_argument('--single', type=str, help='指定要生成的单个植被指数时间序列图，例如: ExG, VARI, GRVI等')
    parser.add_argument('--list', action='store_true', help='列出所有可用的植被指数')
    parser.add_argument('--indices', type=str, help='指定要包含在矩阵中的植被指数，用逗号分隔，例如: ExG,VARI,GRVI')
    parser.add_argument('--output', type=str, help='输出文件名（不包含扩展名）')
    parser.add_argument('--layout44', action='store_true', help='生成4x4布局的图表矩阵，不包括IKA_W')
    args = parser.parse_args()
    
    # 列出所有可用的指数
    if args.list:
        print("可用的植被指数:")
        for idx, index_name in enumerate(vip.df_scvi['index'].values):
            print(f"{idx+1}. {index_name}")
        return
    
    # 生成4x4布局图表
    if args.layout44:
        output_filename = args.output if args.output else 'vegetation_indices_4x4_matrix'
        # 获取所有植被指数，排除IKA_W
        all_indices = list(vip.df_scvi['index'].values)
        selected_indices = [idx for idx in all_indices if idx != 'IKA_W']
        # 确保正好有16个指数
        if len(selected_indices) > 16:
            selected_indices = selected_indices[:16]  # 只取前16个
        
        print(f"开始生成4x4布局的植被指数时间序列图矩阵...")
        vip.plot_fixed_layout_matrix(indices=selected_indices, output_filename=output_filename, n_rows=4, n_cols=4)
        print(f"4x4布局的植被指数时间序列图矩阵生成完成! 已保存为 {output_filename}.pdf 和 {output_filename}.png")
        return
    
    # 生成时间序列图矩阵
    if args.matrix:
        output_filename = args.output if args.output else 'vegetation_indices_timeseries'
        
        if args.indices:
            # 解析用户指定的指数列表
            selected_indices = [idx.strip() for idx in args.indices.split(',')]
            # 检查指数是否存在
            valid_indices = []
            for idx in selected_indices:
                if idx in vip.df_scvi['index'].values:
                    valid_indices.append(idx)
                else:
                    print(f"警告: 找不到指数 '{idx}'")
            
            if not valid_indices:
                print("错误: 没有有效的指数可以绘制")
                return
                
            print(f"开始生成包含指定指数的时间序列图矩阵，按指定顺序排列: {', '.join(valid_indices)}...")
            vip.plot_fixed_layout_matrix(indices=valid_indices, output_filename=output_filename)
        else:
            print("开始生成所有植被指数的时间序列图矩阵，按数据文件中的原始顺序排列...")
            vip.plot_fixed_layout_matrix(output_filename=output_filename)
        print(f"植被指数时间序列图矩阵生成完成! 已保存为 {output_filename}.pdf 和 {output_filename}.png")
    
    # 生成单个指数的时间序列图
    elif args.single:
        output_filename = args.output if args.output else f"{args.single}_timeseries"
        print(f"开始生成{args.single}植被指数时间序列图...")
        vip.plot_single_timeseries(args.single, output_filename=output_filename)
        print(f"{args.single}植被指数时间序列图生成完成！已保存为 {output_filename}.pdf 和 {output_filename}.png")
    
    # 如果没有指定选项，显示帮助信息
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 