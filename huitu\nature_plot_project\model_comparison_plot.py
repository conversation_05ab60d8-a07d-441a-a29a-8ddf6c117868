import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 数据
months = list(range(1, 13))
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'Cascade': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [551, 548, 520, 551, 551, 551, 551, 551, 551, 551, 551, 551]
}

# 计算精度
precision_csaf = [min(real/csaf, 1.0) if csaf > 0 else 0 for real, csaf in zip(data['real'], data['CSAF'])]
precision_cascade = [min(real/cascade, 1.0) if cascade > 0 else 0 for real, cascade in zip(data['real'], data['Cascade'])]

# 创建图表
fig, axes = plt.subplots(1, 12, figsize=(15, 3.5), facecolor='white')
plt.subplots_adjust(wspace=0.1)

# 颜色设置
colors = {
    'CSAF': '#4A7B8C',      # 蓝色
    'Cascade': '#8C4A59',   # 红色
    'real': '#2E5E4B',      # 绿色
    'overlap': '#6B929E'    # 灰蓝色
}

# 最大值用于缩放
max_value = max(max(data['CSAF']), max(data['Cascade']))

# 画每个月份的图表
for i, ax in enumerate(axes):
    month = months[i]
    csaf_val = data['CSAF'][i]
    cascade_val = data['Cascade'][i]
    real_val = data['real'][i]
    
    # 清除坐标轴
    ax.set_xlim(-1.5, 1.5)
    ax.set_ylim(-1.5, 1.5)
    ax.axis('off')
    
    # 添加月份标题
    ax.text(0, 1.4, f"Month {month}", ha='center', fontsize=10, fontweight='bold')
    
    # 计算圆的大小（根据值的大小）
    csaf_radius = 0.6 * np.sqrt(csaf_val / max_value)
    cascade_radius = 0.6 * np.sqrt(cascade_val / max_value)
    real_radius = 0.6 * np.sqrt(real_val / max_value)
    
    # 确定CSAF和real的关系
    if csaf_val <= real_val:
        # real包含CSAF
        real_circle = Circle((0, 0), real_radius, alpha=0.4, color=colors['real'])
        csaf_circle = Circle((0, 0), csaf_radius, alpha=0.4, color=colors['CSAF'])
        
        # 先画大圆，再画小圆
        ax.add_patch(real_circle)
        ax.add_patch(csaf_circle)
        
        # 显示real的值
        ax.text(0, 0, f"{real_val}", ha='center', va='center', fontsize=9,
               path_effects=[path_effects.withStroke(linewidth=3, foreground='white')])
    else:
        # CSAF和real有交集
        real_circle = Circle((-0.2, 0), real_radius, alpha=0.4, color=colors['real'])
        csaf_circle = Circle((0, 0), csaf_radius, alpha=0.4, color=colors['CSAF'])
        
        ax.add_patch(csaf_circle)
        ax.add_patch(real_circle)
        
        # 显示real的值在交叉区域
        ax.text(-0.2, 0, f"{real_val}", ha='center', va='center', fontsize=9,
               path_effects=[path_effects.withStroke(linewidth=3, foreground='white')])
    
    # 画Cascade和real的关系
    cascade_circle = Circle((0.3, 0), cascade_radius, alpha=0.4, color=colors['Cascade'])
    ax.add_patch(cascade_circle)
    
    # 添加模型值和精度
    ax.text(0, -1.0, f"CSAF: {csaf_val}", color=colors['CSAF'], ha='center', fontsize=8)
    ax.text(0, -1.2, f"Prec: {precision_csaf[i]:.2f}", color=colors['CSAF'], ha='center', fontsize=8)
    
    ax.text(0, -0.8, f"Casc: {cascade_val}", color=colors['Cascade'], ha='center', fontsize=8)
    ax.text(0, -0.6, f"Prec: {precision_cascade[i]:.2f}", color=colors['Cascade'], ha='center', fontsize=8)

# 添加图例
legend_ax = fig.add_axes([0.92, 0.15, 0.07, 0.7])
legend_ax.axis('off')

legend_items = []
y_pos = 0.9
for model, color in colors.items():
    if model != 'overlap':
        circle = Circle((0.5, y_pos), 0.1, alpha=0.6, color=color)
        legend_ax.add_patch(circle)
        legend_ax.text(0.65, y_pos, model, va='center', fontsize=9)
        y_pos -= 0.2

# 添加标题
fig.suptitle('Monthly Model Performance Comparison', fontsize=12, y=0.98)

# 添加说明
fig.text(0.5, 0.02, 'Circle size represents value magnitude. Overlap represents shared values.', 
         ha='center', fontsize=8, style='italic')

# 保存图表
plt.savefig('model_comparison_nature.png', dpi=600, bbox_inches='tight')
plt.savefig('model_comparison_nature.pdf', bbox_inches='tight')

print("Nature-style model comparison chart created")
plt.show() 