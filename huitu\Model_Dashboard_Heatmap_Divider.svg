<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1139.10565pt" height="941.796563pt" viewBox="0 0 1139.10565 941.796563" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-26T12:43:43.272801</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 941.796563 
L 1139.10565 941.796563 
L 1139.10565 -0 
L 0 -0 
L 0 941.796563 
z
" style="fill: none; opacity: 0"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 324.073775 797.76 
L 948.073775 797.76 
L 948.073775 210.96 
L 324.073775 210.96 
L 324.073775 797.76 
z
" style="fill: none; opacity: 0"/>
   </g>
   <g id="QuadMesh_1">
    <path d="M 324.073775 210.96 
L 393.407109 210.96 
L 393.407109 357.66 
L 324.073775 357.66 
L 324.073775 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #26828e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 393.407109 210.96 
L 462.740442 210.96 
L 462.740442 357.66 
L 393.407109 357.66 
L 393.407109 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #2e6e8e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 462.740442 210.96 
L 532.073775 210.96 
L 532.073775 357.66 
L 462.740442 357.66 
L 462.740442 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #31668e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 532.073775 210.96 
L 601.407109 210.96 
L 601.407109 357.66 
L 532.073775 357.66 
L 532.073775 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #3a538b; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 601.407109 210.96 
L 670.740442 210.96 
L 670.740442 357.66 
L 601.407109 357.66 
L 601.407109 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #2d718e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 670.740442 210.96 
L 740.073775 210.96 
L 740.073775 357.66 
L 670.740442 357.66 
L 670.740442 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #404588; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 740.073775 210.96 
L 809.407109 210.96 
L 809.407109 357.66 
L 740.073775 357.66 
L 740.073775 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #3a548c; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 809.407109 210.96 
L 878.740442 210.96 
L 878.740442 357.66 
L 809.407109 357.66 
L 809.407109 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #46337f; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 878.740442 210.96 
L 948.073775 210.96 
L 948.073775 357.66 
L 878.740442 357.66 
L 878.740442 210.96 
" clip-path="url(#p285d8b6577)" style="fill: #482878; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 324.073775 357.66 
L 393.407109 357.66 
L 393.407109 504.36 
L 324.073775 504.36 
L 324.073775 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #fde725; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 393.407109 357.66 
L 462.740442 357.66 
L 462.740442 504.36 
L 393.407109 504.36 
L 393.407109 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #b0dd2f; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 462.740442 357.66 
L 532.073775 357.66 
L 532.073775 504.36 
L 462.740442 504.36 
L 462.740442 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #65cb5e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 532.073775 357.66 
L 601.407109 357.66 
L 601.407109 504.36 
L 532.073775 504.36 
L 532.073775 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #3dbc74; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 601.407109 357.66 
L 670.740442 357.66 
L 670.740442 504.36 
L 601.407109 504.36 
L 601.407109 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #8bd646; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 670.740442 357.66 
L 740.073775 357.66 
L 740.073775 504.36 
L 670.740442 504.36 
L 670.740442 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #27ad81; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 740.073775 357.66 
L 809.407109 357.66 
L 809.407109 504.36 
L 740.073775 504.36 
L 740.073775 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #3aba76; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 809.407109 357.66 
L 878.740442 357.66 
L 878.740442 504.36 
L 809.407109 504.36 
L 809.407109 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #1e9d89; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 878.740442 357.66 
L 948.073775 357.66 
L 948.073775 504.36 
L 878.740442 504.36 
L 878.740442 357.66 
" clip-path="url(#p285d8b6577)" style="fill: #218f8d; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 324.073775 504.36 
L 393.407109 504.36 
L 393.407109 651.06 
L 324.073775 651.06 
L 324.073775 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #31678e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 393.407109 504.36 
L 462.740442 504.36 
L 462.740442 651.06 
L 393.407109 651.06 
L 393.407109 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #365d8d; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 462.740442 504.36 
L 532.073775 504.36 
L 532.073775 651.06 
L 462.740442 651.06 
L 462.740442 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #365c8d; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 532.073775 504.36 
L 601.407109 504.36 
L 601.407109 651.06 
L 532.073775 651.06 
L 532.073775 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #433d84; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 601.407109 504.36 
L 670.740442 504.36 
L 670.740442 651.06 
L 601.407109 651.06 
L 601.407109 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #365c8d; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 670.740442 504.36 
L 740.073775 504.36 
L 740.073775 651.06 
L 670.740442 651.06 
L 670.740442 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #482677; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 740.073775 504.36 
L 809.407109 504.36 
L 809.407109 651.06 
L 740.073775 651.06 
L 740.073775 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #443983; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 809.407109 504.36 
L 878.740442 504.36 
L 878.740442 651.06 
L 809.407109 651.06 
L 809.407109 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #471063; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 878.740442 504.36 
L 948.073775 504.36 
L 948.073775 651.06 
L 878.740442 651.06 
L 878.740442 504.36 
" clip-path="url(#p285d8b6577)" style="fill: #440154; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 324.073775 651.06 
L 393.407109 651.06 
L 393.407109 797.76 
L 324.073775 797.76 
L 324.073775 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #26828e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 393.407109 651.06 
L 462.740442 651.06 
L 462.740442 797.76 
L 393.407109 797.76 
L 393.407109 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #2e6e8e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 462.740442 651.06 
L 532.073775 651.06 
L 532.073775 797.76 
L 462.740442 797.76 
L 462.740442 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #31678e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 532.073775 651.06 
L 601.407109 651.06 
L 601.407109 797.76 
L 532.073775 797.76 
L 532.073775 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #3a548c; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 601.407109 651.06 
L 670.740442 651.06 
L 670.740442 797.76 
L 601.407109 797.76 
L 601.407109 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #2d708e; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 670.740442 651.06 
L 740.073775 651.06 
L 740.073775 797.76 
L 670.740442 797.76 
L 670.740442 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #404688; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 740.073775 651.06 
L 809.407109 651.06 
L 809.407109 797.76 
L 740.073775 797.76 
L 740.073775 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #3a548c; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 809.407109 651.06 
L 878.740442 651.06 
L 878.740442 797.76 
L 809.407109 797.76 
L 809.407109 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #46337f; stroke: #ffffff; stroke-width: 0.5"/>
    <path d="M 878.740442 651.06 
L 948.073775 651.06 
L 948.073775 797.76 
L 878.740442 797.76 
L 878.740442 651.06 
" clip-path="url(#p285d8b6577)" style="fill: #482878; stroke: #ffffff; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m5fc366f3e4" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5fc366f3e4" x="358.740442" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- CSAF -->
      <g transform="translate(364.986536 869.796563) rotate(-90) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-43" d="M 3853 4334 
L 3950 2894 
L 3853 2894 
Q 3659 3541 3300 3825 
Q 2941 4109 2438 4109 
Q 2016 4109 1675 3895 
Q 1334 3681 1139 3212 
Q 944 2744 944 2047 
Q 944 1472 1128 1050 
Q 1313 628 1683 403 
Q 2053 178 2528 178 
Q 2941 178 3256 354 
Q 3572 531 3950 1056 
L 4047 994 
Q 3728 428 3303 165 
Q 2878 -97 2294 -97 
Q 1241 -97 663 684 
Q 231 1266 231 2053 
Q 231 2688 515 3219 
Q 800 3750 1298 4042 
Q 1797 4334 2388 4334 
Q 2847 4334 3294 4109 
Q 3425 4041 3481 4041 
Q 3566 4041 3628 4100 
Q 3709 4184 3744 4334 
L 3853 4334 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-53" d="M 2934 4334 
L 2934 2869 
L 2819 2869 
Q 2763 3291 2617 3541 
Q 2472 3791 2203 3937 
Q 1934 4084 1647 4084 
Q 1322 4084 1109 3886 
Q 897 3688 897 3434 
Q 897 3241 1031 3081 
Q 1225 2847 1953 2456 
Q 2547 2138 2764 1967 
Q 2981 1797 3098 1565 
Q 3216 1334 3216 1081 
Q 3216 600 2842 251 
Q 2469 -97 1881 -97 
Q 1697 -97 1534 -69 
Q 1438 -53 1133 45 
Q 828 144 747 144 
Q 669 144 623 97 
Q 578 50 556 -97 
L 441 -97 
L 441 1356 
L 556 1356 
Q 638 900 775 673 
Q 913 447 1195 297 
Q 1478 147 1816 147 
Q 2206 147 2432 353 
Q 2659 559 2659 841 
Q 2659 997 2573 1156 
Q 2488 1316 2306 1453 
Q 2184 1547 1640 1851 
Q 1097 2156 867 2337 
Q 638 2519 519 2737 
Q 400 2956 400 3219 
Q 400 3675 750 4004 
Q 1100 4334 1641 4334 
Q 1978 4334 2356 4169 
Q 2531 4091 2603 4091 
Q 2684 4091 2736 4139 
Q 2788 4188 2819 4334 
L 2934 4334 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-41" d="M 2928 1419 
L 1288 1419 
L 1000 750 
Q 894 503 894 381 
Q 894 284 986 211 
Q 1078 138 1384 116 
L 1384 0 
L 50 0 
L 50 116 
Q 316 163 394 238 
Q 553 388 747 847 
L 2238 4334 
L 2347 4334 
L 3822 809 
Q 4000 384 4145 257 
Q 4291 131 4550 116 
L 4550 0 
L 2878 0 
L 2878 116 
Q 3131 128 3220 200 
Q 3309 272 3309 375 
Q 3309 513 3184 809 
L 2928 1419 
z
M 2841 1650 
L 2122 3363 
L 1384 1650 
L 2841 1650 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-46" d="M 1309 4006 
L 1309 2341 
L 2081 2341 
Q 2347 2341 2470 2458 
Q 2594 2575 2634 2922 
L 2750 2922 
L 2750 1488 
L 2634 1488 
Q 2631 1734 2570 1850 
Q 2509 1966 2401 2023 
Q 2294 2081 2081 2081 
L 1309 2081 
L 1309 750 
Q 1309 428 1350 325 
Q 1381 247 1481 191 
Q 1619 116 1769 116 
L 1922 116 
L 1922 0 
L 103 0 
L 103 116 
L 253 116 
Q 516 116 634 269 
Q 709 369 709 750 
L 709 3488 
Q 709 3809 669 3913 
Q 638 3991 541 4047 
Q 406 4122 253 4122 
L 103 4122 
L 103 4238 
L 3256 4238 
L 3297 3306 
L 3188 3306 
Q 3106 3603 2998 3742 
Q 2891 3881 2733 3943 
Q 2575 4006 2244 4006 
L 1309 4006 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-43"/>
       <use xlink:href="#TimesNewRomanPSMT-53" transform="translate(66.699219 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-41" transform="translate(122.314453 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-46" transform="translate(194.53125 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m5fc366f3e4" x="428.073775" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- DT -->
      <g transform="translate(434.319869 862.565313) rotate(-90) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-4d" d="M 2619 0 
L 981 3566 
L 981 734 
Q 981 344 1066 247 
Q 1181 116 1431 116 
L 1581 116 
L 1581 0 
L 106 0 
L 106 116 
L 256 116 
Q 525 116 638 278 
Q 706 378 706 734 
L 706 3503 
Q 706 3784 644 3909 
Q 600 4000 483 4061 
Q 366 4122 106 4122 
L 106 4238 
L 1306 4238 
L 2844 922 
L 4356 4238 
L 5556 4238 
L 5556 4122 
L 5409 4122 
Q 5138 4122 5025 3959 
Q 4956 3859 4956 3503 
L 4956 734 
Q 4956 344 5044 247 
Q 5159 116 5409 116 
L 5556 116 
L 5556 0 
L 3756 0 
L 3756 116 
L 3906 116 
Q 4178 116 4288 278 
Q 4356 378 4356 734 
L 4356 3566 
L 2722 0 
L 2619 0 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-52" d="M 4325 0 
L 3194 0 
L 1759 1981 
Q 1600 1975 1500 1975 
Q 1459 1975 1412 1976 
Q 1366 1978 1316 1981 
L 1316 750 
Q 1316 350 1403 253 
Q 1522 116 1759 116 
L 1925 116 
L 1925 0 
L 109 0 
L 109 116 
L 269 116 
Q 538 116 653 291 
Q 719 388 719 750 
L 719 3488 
Q 719 3888 631 3984 
Q 509 4122 269 4122 
L 109 4122 
L 109 4238 
L 1653 4238 
Q 2328 4238 2648 4139 
Q 2969 4041 3192 3777 
Q 3416 3513 3416 3147 
Q 3416 2756 3161 2468 
Q 2906 2181 2372 2063 
L 3247 847 
Q 3547 428 3762 290 
Q 3978 153 4325 116 
L 4325 0 
z
M 1316 2178 
Q 1375 2178 1419 2176 
Q 1463 2175 1491 2175 
Q 2097 2175 2405 2437 
Q 2713 2700 2713 3106 
Q 2713 3503 2464 3751 
Q 2216 4000 1806 4000 
Q 1625 4000 1316 3941 
L 1316 2178 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-43"/>
       <use xlink:href="#TimesNewRomanPSMT-4d" transform="translate(66.699219 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-52" transform="translate(155.615234 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m5fc366f3e4" x="497.407109" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- ST -->
      <g transform="translate(503.653202 835.09875) rotate(-90) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-54" d="M 3703 4238 
L 3750 3244 
L 3631 3244 
Q 3597 3506 3538 3619 
Q 3441 3800 3280 3886 
Q 3119 3972 2856 3972 
L 2259 3972 
L 2259 734 
Q 2259 344 2344 247 
Q 2463 116 2709 116 
L 2856 116 
L 2856 0 
L 1059 0 
L 1059 116 
L 1209 116 
Q 1478 116 1591 278 
Q 1659 378 1659 734 
L 1659 3972 
L 1150 3972 
Q 853 3972 728 3928 
Q 566 3869 450 3700 
Q 334 3531 313 3244 
L 194 3244 
L 244 4238 
L 3703 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-53"/>
       <use xlink:href="#TimesNewRomanPSMT-54" transform="translate(55.615234 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m5fc366f3e4" x="566.740442" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- HTC -->
      <g transform="translate(572.986536 856.76) rotate(-90) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-48" d="M 1316 2272 
L 3284 2272 
L 3284 3484 
Q 3284 3809 3244 3913 
Q 3213 3991 3113 4047 
Q 2978 4122 2828 4122 
L 2678 4122 
L 2678 4238 
L 4491 4238 
L 4491 4122 
L 4341 4122 
Q 4191 4122 4056 4050 
Q 3956 4000 3920 3898 
Q 3884 3797 3884 3484 
L 3884 750 
Q 3884 428 3925 325 
Q 3956 247 4053 191 
Q 4191 116 4341 116 
L 4491 116 
L 4491 0 
L 2678 0 
L 2678 116 
L 2828 116 
Q 3088 116 3206 269 
Q 3284 369 3284 750 
L 3284 2041 
L 1316 2041 
L 1316 750 
Q 1316 428 1356 325 
Q 1388 247 1488 191 
Q 1622 116 1772 116 
L 1925 116 
L 1925 0 
L 109 0 
L 109 116 
L 259 116 
Q 522 116 641 269 
Q 716 369 716 750 
L 716 3484 
Q 716 3809 675 3913 
Q 644 3991 547 4047 
Q 409 4122 259 4122 
L 109 4122 
L 109 4238 
L 1925 4238 
L 1925 4122 
L 1772 4122 
Q 1622 4122 1488 4050 
Q 1391 4000 1353 3898 
Q 1316 3797 1316 3484 
L 1316 2272 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-48"/>
       <use xlink:href="#TimesNewRomanPSMT-54" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-43" transform="translate(133.300781 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m5fc366f3e4" x="636.073775" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- MR -->
      <g transform="translate(642.319869 845.2225) rotate(-90) scale(0.26 -0.26)">
       <use xlink:href="#TimesNewRomanPSMT-4d"/>
       <use xlink:href="#TimesNewRomanPSMT-52" transform="translate(88.916016 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m5fc366f3e4" x="705.407109" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- YV -->
      <g transform="translate(711.653202 842.31375) rotate(-90) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-59" d="M 3050 4238 
L 4528 4238 
L 4528 4122 
L 4447 4122 
Q 4366 4122 4209 4050 
Q 4053 3978 3925 3843 
Q 3797 3709 3609 3406 
L 2588 1797 
L 2588 734 
Q 2588 344 2675 247 
Q 2794 116 3050 116 
L 3188 116 
L 3188 0 
L 1388 0 
L 1388 116 
L 1538 116 
Q 1806 116 1919 278 
Q 1988 378 1988 734 
L 1988 1738 
L 825 3513 
Q 619 3825 545 3903 
Q 472 3981 241 4091 
Q 178 4122 59 4122 
L 59 4238 
L 1872 4238 
L 1872 4122 
L 1778 4122 
Q 1631 4122 1507 4053 
Q 1384 3984 1384 3847 
Q 1384 3734 1575 3441 
L 2459 2075 
L 3291 3381 
Q 3478 3675 3478 3819 
Q 3478 3906 3433 3975 
Q 3388 4044 3303 4083 
Q 3219 4122 3050 4122 
L 3050 4238 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-56" d="M 4544 4238 
L 4544 4122 
Q 4319 4081 4203 3978 
Q 4038 3825 3909 3509 
L 2431 -97 
L 2316 -97 
L 728 3556 
Q 606 3838 556 3900 
Q 478 3997 364 4051 
Q 250 4106 56 4122 
L 56 4238 
L 1788 4238 
L 1788 4122 
Q 1494 4094 1406 4022 
Q 1319 3950 1319 3838 
Q 1319 3681 1463 3350 
L 2541 866 
L 3541 3319 
Q 3688 3681 3688 3822 
Q 3688 3913 3597 3995 
Q 3506 4078 3291 4113 
Q 3275 4116 3238 4122 
L 3238 4238 
L 4544 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-59"/>
       <use xlink:href="#TimesNewRomanPSMT-56" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m5fc366f3e4" x="774.740442" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- YL -->
      <g transform="translate(780.986536 839.417188) rotate(-90) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-4c" d="M 3669 1172 
L 3772 1150 
L 3409 0 
L 128 0 
L 128 116 
L 288 116 
Q 556 116 672 291 
Q 738 391 738 753 
L 738 3488 
Q 738 3884 650 3984 
Q 528 4122 288 4122 
L 128 4122 
L 128 4238 
L 2047 4238 
L 2047 4122 
Q 1709 4125 1573 4059 
Q 1438 3994 1388 3894 
Q 1338 3794 1338 3416 
L 1338 753 
Q 1338 494 1388 397 
Q 1425 331 1503 300 
Q 1581 269 1991 269 
L 2300 269 
Q 2788 269 2984 341 
Q 3181 413 3343 595 
Q 3506 778 3669 1172 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-59"/>
       <use xlink:href="#TimesNewRomanPSMT-4c" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m5fc366f3e4" x="844.073775" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- QI -->
      <g transform="translate(850.319869 832.194063) rotate(-90) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-51" d="M 2819 -47 
Q 3138 -597 3508 -856 
Q 3878 -1116 4350 -1153 
L 4350 -1253 
Q 3919 -1238 3428 -1080 
Q 2938 -922 2498 -642 
Q 2059 -363 1741 -47 
Q 1291 134 1028 338 
Q 647 641 436 1083 
Q 225 1525 225 2128 
Q 225 3078 840 3706 
Q 1456 4334 2331 4334 
Q 3163 4334 3770 3704 
Q 4378 3075 4378 2116 
Q 4378 1338 3945 750 
Q 3513 163 2819 -47 
z
M 2294 4094 
Q 1725 4094 1378 3688 
Q 941 3178 941 2128 
Q 941 1100 1384 544 
Q 1728 116 2294 116 
Q 2881 116 3241 544 
Q 3663 1050 3663 2038 
Q 3663 2797 3431 3316 
Q 3253 3716 2958 3905 
Q 2663 4094 2294 4094 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-49" d="M 1975 116 
L 1975 0 
L 159 0 
L 159 116 
L 309 116 
Q 572 116 691 269 
Q 766 369 766 750 
L 766 3488 
Q 766 3809 725 3913 
Q 694 3991 597 4047 
Q 459 4122 309 4122 
L 159 4122 
L 159 4238 
L 1975 4238 
L 1975 4122 
L 1822 4122 
Q 1563 4122 1444 3969 
Q 1366 3869 1366 3488 
L 1366 750 
Q 1366 428 1406 325 
Q 1438 247 1538 191 
Q 1672 116 1822 116 
L 1975 116 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-51"/>
       <use xlink:href="#TimesNewRomanPSMT-49" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m5fc366f3e4" x="913.407109" y="797.76" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- SL -->
      <g transform="translate(919.653202 835.09875) rotate(-90) scale(0.26 -0.26)">
       <use xlink:href="#TimesNewRomanPSMT-53"/>
       <use xlink:href="#TimesNewRomanPSMT-4c" transform="translate(55.615234 0)"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_10">
      <defs>
       <path id="m58b0d461c6" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m58b0d461c6" x="324.073775" y="284.31" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- AP -->
      <g transform="translate(283.838463 290.556094) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-50" d="M 1313 1984 
L 1313 750 
Q 1313 350 1400 253 
Q 1519 116 1759 116 
L 1922 116 
L 1922 0 
L 106 0 
L 106 116 
L 266 116 
Q 534 116 650 291 
Q 713 388 713 750 
L 713 3488 
Q 713 3888 628 3984 
Q 506 4122 266 4122 
L 106 4122 
L 106 4238 
L 1659 4238 
Q 2228 4238 2556 4120 
Q 2884 4003 3109 3725 
Q 3334 3447 3334 3066 
Q 3334 2547 2992 2222 
Q 2650 1897 2025 1897 
Q 1872 1897 1694 1919 
Q 1516 1941 1313 1984 
z
M 1313 2163 
Q 1478 2131 1606 2115 
Q 1734 2100 1825 2100 
Q 2150 2100 2386 2351 
Q 2622 2603 2622 3003 
Q 2622 3278 2509 3514 
Q 2397 3750 2190 3867 
Q 1984 3984 1722 3984 
Q 1563 3984 1313 3925 
L 1313 2163 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m58b0d461c6" x="324.073775" y="431.01" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- AP50 -->
      <g transform="translate(257.838463 437.256094) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-35" d="M 2778 4238 
L 2534 3706 
L 1259 3706 
L 981 3138 
Q 1809 3016 2294 2522 
Q 2709 2097 2709 1522 
Q 2709 1188 2573 903 
Q 2438 619 2231 419 
Q 2025 219 1772 97 
Q 1413 -75 1034 -75 
Q 653 -75 479 54 
Q 306 184 306 341 
Q 306 428 378 495 
Q 450 563 559 563 
Q 641 563 702 538 
Q 763 513 909 409 
Q 1144 247 1384 247 
Q 1750 247 2026 523 
Q 2303 800 2303 1197 
Q 2303 1581 2056 1914 
Q 1809 2247 1375 2428 
Q 1034 2569 447 2591 
L 1259 4238 
L 2778 4238 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-30" d="M 231 2094 
Q 231 2819 450 3342 
Q 669 3866 1031 4122 
Q 1313 4325 1613 4325 
Q 2100 4325 2488 3828 
Q 2972 3213 2972 2159 
Q 2972 1422 2759 906 
Q 2547 391 2217 158 
Q 1888 -75 1581 -75 
Q 975 -75 572 641 
Q 231 1244 231 2094 
z
M 844 2016 
Q 844 1141 1059 588 
Q 1238 122 1591 122 
Q 1759 122 1940 273 
Q 2122 425 2216 781 
Q 2359 1319 2359 2297 
Q 2359 3022 2209 3506 
Q 2097 3866 1919 4016 
Q 1791 4119 1609 4119 
Q 1397 4119 1231 3928 
Q 1006 3669 925 3112 
Q 844 2556 844 2016 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(127.832031 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(177.832031 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m58b0d461c6" x="324.073775" y="577.71" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- AP75 -->
      <g transform="translate(257.838463 583.956094) scale(0.26 -0.26)">
       <defs>
        <path id="TimesNewRomanPSMT-37" d="M 644 4238 
L 2916 4238 
L 2916 4119 
L 1503 -88 
L 1153 -88 
L 2419 3728 
L 1253 3728 
Q 900 3728 750 3644 
Q 488 3500 328 3200 
L 238 3234 
L 644 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(127.832031 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(177.832031 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_13">
      <g>
       <use xlink:href="#m58b0d461c6" x="324.073775" y="724.41" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- API -->
      <g transform="translate(275.181275 730.656094) scale(0.26 -0.26)">
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-49" transform="translate(127.832031 0)"/>
      </g>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_3">
    <path d="M 324.073775 189.36 
L 948.073775 189.36 
L 948.073775 72 
L 324.073775 72 
L 324.073775 189.36 
z
" style="fill: none; opacity: 0"/>
   </g>
   <g id="patch_4">
    <path d="M 324.073775 189.36 
L 393.407109 189.36 
L 393.407109 77.588571 
L 324.073775 77.588571 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 393.407109 189.36 
L 462.740442 189.36 
L 462.740442 92.789486 
L 393.407109 92.789486 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_6">
    <path d="M 462.740442 189.36 
L 532.073775 189.36 
L 532.073775 101.156376 
L 462.740442 101.156376 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_7">
    <path d="M 532.073775 189.36 
L 601.407109 189.36 
L 601.407109 116.485029 
L 532.073775 116.485029 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_8">
    <path d="M 601.407109 189.36 
L 670.740442 189.36 
L 670.740442 94.641698 
L 601.407109 94.641698 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_9">
    <path d="M 670.740442 189.36 
L 740.073775 189.36 
L 740.073775 127.662171 
L 670.740442 127.662171 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_10">
    <path d="M 740.073775 189.36 
L 809.407109 189.36 
L 809.407109 117.443069 
L 740.073775 117.443069 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_11">
    <path d="M 809.407109 189.36 
L 878.740442 189.36 
L 878.740442 139.733486 
L 809.407109 139.733486 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="patch_12">
    <path d="M 878.740442 189.36 
L 948.073775 189.36 
L 948.073775 147.525551 
L 878.740442 147.525551 
z
" clip-path="url(#p43a6a43f05)" style="fill: #21918c; opacity: 0.8; stroke: #808080; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10"/>
    <g id="xtick_11"/>
    <g id="xtick_12"/>
    <g id="xtick_13"/>
    <g id="xtick_14"/>
    <g id="xtick_15"/>
    <g id="xtick_16"/>
    <g id="xtick_17"/>
    <g id="xtick_18"/>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_5">
     <g id="line2d_14">
      <path d="M 324.073775 189.36 
L 948.073775 189.36 
" clip-path="url(#p43a6a43f05)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.6; stroke-width: 0.8"/>
     </g>
     <g id="line2d_15">
      <defs>
       <path id="m83ff84fbe9" d="M 0 0 
L 3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m83ff84fbe9" x="948.073775" y="189.36" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 0 -->
      <g transform="translate(955.073775 197.6925) scale(0.24 -0.24)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_16">
      <path d="M 324.073775 125.490612 
L 948.073775 125.490612 
" clip-path="url(#p43a6a43f05)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.6; stroke-width: 0.8"/>
     </g>
     <g id="line2d_17">
      <g>
       <use xlink:href="#m83ff84fbe9" x="948.073775" y="125.490612" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 100 -->
      <g transform="translate(955.073775 133.823112) scale(0.24 -0.24)">
       <defs>
        <path id="TimesNewRomanPSMT-31" d="M 750 3822 
L 1781 4325 
L 1884 4325 
L 1884 747 
Q 1884 391 1914 303 
Q 1944 216 2037 169 
Q 2131 122 2419 116 
L 2419 0 
L 825 0 
L 825 116 
Q 1125 122 1212 167 
Q 1300 213 1334 289 
Q 1369 366 1369 747 
L 1369 3034 
Q 1369 3497 1338 3628 
Q 1316 3728 1258 3775 
Q 1200 3822 1119 3822 
Q 1003 3822 797 3725 
L 750 3822 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-31"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(100 0)"/>
      </g>
     </g>
    </g>
    <g id="text_16">
     <!-- Total Score (Model) -->
     <g transform="translate(1061.116275 243.996875) rotate(-90) scale(0.28 -0.28)">
      <defs>
       <path id="TimesNewRomanPSMT-6f" d="M 1600 2947 
Q 2250 2947 2644 2453 
Q 2978 2031 2978 1484 
Q 2978 1100 2793 706 
Q 2609 313 2286 112 
Q 1963 -88 1566 -88 
Q 919 -88 538 428 
Q 216 863 216 1403 
Q 216 1797 411 2186 
Q 606 2575 925 2761 
Q 1244 2947 1600 2947 
z
M 1503 2744 
Q 1338 2744 1170 2645 
Q 1003 2547 900 2300 
Q 797 2053 797 1666 
Q 797 1041 1045 587 
Q 1294 134 1700 134 
Q 2003 134 2200 384 
Q 2397 634 2397 1244 
Q 2397 2006 2069 2444 
Q 1847 2744 1503 2744 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-74" d="M 1031 3803 
L 1031 2863 
L 1700 2863 
L 1700 2644 
L 1031 2644 
L 1031 788 
Q 1031 509 1111 412 
Q 1191 316 1316 316 
Q 1419 316 1516 380 
Q 1613 444 1666 569 
L 1788 569 
Q 1678 263 1478 108 
Q 1278 -47 1066 -47 
Q 922 -47 784 33 
Q 647 113 581 261 
Q 516 409 516 719 
L 516 2644 
L 63 2644 
L 63 2747 
Q 234 2816 414 2980 
Q 594 3144 734 3369 
Q 806 3488 934 3803 
L 1031 3803 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-61" d="M 1822 413 
Q 1381 72 1269 19 
Q 1100 -59 909 -59 
Q 613 -59 420 144 
Q 228 347 228 678 
Q 228 888 322 1041 
Q 450 1253 767 1440 
Q 1084 1628 1822 1897 
L 1822 2009 
Q 1822 2438 1686 2597 
Q 1550 2756 1291 2756 
Q 1094 2756 978 2650 
Q 859 2544 859 2406 
L 866 2225 
Q 866 2081 792 2003 
Q 719 1925 600 1925 
Q 484 1925 411 2006 
Q 338 2088 338 2228 
Q 338 2497 613 2722 
Q 888 2947 1384 2947 
Q 1766 2947 2009 2819 
Q 2194 2722 2281 2516 
Q 2338 2381 2338 1966 
L 2338 994 
Q 2338 584 2353 492 
Q 2369 400 2405 369 
Q 2441 338 2488 338 
Q 2538 338 2575 359 
Q 2641 400 2828 588 
L 2828 413 
Q 2478 -56 2159 -56 
Q 2006 -56 1915 50 
Q 1825 156 1822 413 
z
M 1822 616 
L 1822 1706 
Q 1350 1519 1213 1441 
Q 966 1303 859 1153 
Q 753 1003 753 825 
Q 753 600 887 451 
Q 1022 303 1197 303 
Q 1434 303 1822 616 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-6c" d="M 1184 4444 
L 1184 647 
Q 1184 378 1223 290 
Q 1263 203 1344 158 
Q 1425 113 1647 113 
L 1647 0 
L 244 0 
L 244 113 
Q 441 113 512 153 
Q 584 194 625 287 
Q 666 381 666 647 
L 666 3247 
Q 666 3731 644 3842 
Q 622 3953 573 3993 
Q 525 4034 450 4034 
Q 369 4034 244 3984 
L 191 4094 
L 1044 4444 
L 1184 4444 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-20" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-63" d="M 2631 1088 
Q 2516 522 2178 217 
Q 1841 -88 1431 -88 
Q 944 -88 581 321 
Q 219 731 219 1428 
Q 219 2103 620 2525 
Q 1022 2947 1584 2947 
Q 2006 2947 2278 2723 
Q 2550 2500 2550 2259 
Q 2550 2141 2473 2067 
Q 2397 1994 2259 1994 
Q 2075 1994 1981 2113 
Q 1928 2178 1911 2362 
Q 1894 2547 1784 2644 
Q 1675 2738 1481 2738 
Q 1169 2738 978 2506 
Q 725 2200 725 1697 
Q 725 1184 976 792 
Q 1228 400 1656 400 
Q 1963 400 2206 609 
Q 2378 753 2541 1131 
L 2631 1088 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-72" d="M 1038 2947 
L 1038 2303 
Q 1397 2947 1775 2947 
Q 1947 2947 2059 2842 
Q 2172 2738 2172 2600 
Q 2172 2478 2090 2393 
Q 2009 2309 1897 2309 
Q 1788 2309 1652 2417 
Q 1516 2525 1450 2525 
Q 1394 2525 1328 2463 
Q 1188 2334 1038 2041 
L 1038 669 
Q 1038 431 1097 309 
Q 1138 225 1241 169 
Q 1344 113 1538 113 
L 1538 0 
L 72 0 
L 72 113 
Q 291 113 397 181 
Q 475 231 506 341 
Q 522 394 522 644 
L 522 1753 
Q 522 2253 501 2348 
Q 481 2444 426 2487 
Q 372 2531 291 2531 
Q 194 2531 72 2484 
L 41 2597 
L 906 2947 
L 1038 2947 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-65" d="M 681 1784 
Q 678 1147 991 784 
Q 1303 422 1725 422 
Q 2006 422 2214 576 
Q 2422 731 2563 1106 
L 2659 1044 
Q 2594 616 2278 264 
Q 1963 -88 1488 -88 
Q 972 -88 605 314 
Q 238 716 238 1394 
Q 238 2128 614 2539 
Q 991 2950 1559 2950 
Q 2041 2950 2350 2633 
Q 2659 2316 2659 1784 
L 681 1784 
z
M 681 1966 
L 2006 1966 
Q 1991 2241 1941 2353 
Q 1863 2528 1708 2628 
Q 1553 2728 1384 2728 
Q 1125 2728 920 2526 
Q 716 2325 681 1966 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-28" d="M 1988 -1253 
L 1988 -1369 
Q 1516 -1131 1200 -813 
Q 750 -359 506 256 
Q 263 872 263 1534 
Q 263 2503 741 3301 
Q 1219 4100 1988 4444 
L 1988 4313 
Q 1603 4100 1356 3731 
Q 1109 3363 987 2797 
Q 866 2231 866 1616 
Q 866 947 969 400 
Q 1050 -31 1165 -292 
Q 1281 -553 1476 -793 
Q 1672 -1034 1988 -1253 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-64" d="M 2222 322 
Q 2013 103 1813 7 
Q 1613 -88 1381 -88 
Q 913 -88 563 304 
Q 213 697 213 1313 
Q 213 1928 600 2439 
Q 988 2950 1597 2950 
Q 1975 2950 2222 2709 
L 2222 3238 
Q 2222 3728 2198 3840 
Q 2175 3953 2125 3993 
Q 2075 4034 2000 4034 
Q 1919 4034 1784 3984 
L 1744 4094 
L 2597 4444 
L 2738 4444 
L 2738 1134 
Q 2738 631 2761 520 
Q 2784 409 2836 365 
Q 2888 322 2956 322 
Q 3041 322 3181 375 
L 3216 266 
L 2366 -88 
L 2222 -88 
L 2222 322 
z
M 2222 541 
L 2222 2016 
Q 2203 2228 2109 2403 
Q 2016 2578 1861 2667 
Q 1706 2756 1559 2756 
Q 1284 2756 1069 2509 
Q 784 2184 784 1559 
Q 784 928 1059 592 
Q 1334 256 1672 256 
Q 1956 256 2222 541 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-29" d="M 144 4313 
L 144 4444 
Q 619 4209 934 3891 
Q 1381 3434 1625 2820 
Q 1869 2206 1869 1541 
Q 1869 572 1392 -226 
Q 916 -1025 144 -1369 
L 144 -1253 
Q 528 -1038 776 -670 
Q 1025 -303 1145 264 
Q 1266 831 1266 1447 
Q 1266 2113 1163 2663 
Q 1084 3094 967 3353 
Q 850 3613 656 3853 
Q 463 4094 144 4313 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-54"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(54.083984 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-74" transform="translate(104.083984 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-61" transform="translate(131.867188 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6c" transform="translate(176.251953 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-20" transform="translate(204.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-53" transform="translate(229.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-63" transform="translate(284.650391 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(329.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-72" transform="translate(379.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-65" transform="translate(412.335938 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-20" transform="translate(456.720703 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-28" transform="translate(481.720703 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-4d" transform="translate(515.021484 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(603.9375 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-64" transform="translate(653.9375 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-65" transform="translate(703.9375 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6c" transform="translate(748.322266 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-29" transform="translate(776.105469 0)"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_13">
    <path d="M 79.273775 797.76 
L 266.473775 797.76 
L 266.473775 210.96 
L 79.273775 210.96 
L 79.273775 797.76 
z
" style="fill: none; opacity: 0"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_19">
     <g id="line2d_18">
      <path d="M 257.964684 797.76 
L 257.964684 210.96 
" clip-path="url(#p686fd13a4b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.6; stroke-width: 0.8"/>
     </g>
     <g id="line2d_19">
      <defs>
       <path id="m517574225b" d="M 0 0 
L 0 -3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m517574225b" x="257.964684" y="210.96" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 0 -->
      <g transform="translate(251.964684 198.82625) scale(0.24 -0.24)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_20">
      <path d="M 90 797.76 
L 90 210.96 
" clip-path="url(#p686fd13a4b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.6; stroke-width: 0.8"/>
     </g>
     <g id="line2d_21">
      <g>
       <use xlink:href="#m517574225b" x="90" y="210.96" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 500 -->
      <g transform="translate(72 198.82625) scale(0.24 -0.24)">
       <use xlink:href="#TimesNewRomanPSMT-35"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(100 0)"/>
      </g>
     </g>
    </g>
    <g id="text_19">
     <!-- Total Score (Metric) -->
     <g transform="translate(58.791275 122.94) scale(0.28 -0.28)">
      <defs>
       <path id="TimesNewRomanPSMT-69" d="M 928 4444 
Q 1059 4444 1151 4351 
Q 1244 4259 1244 4128 
Q 1244 3997 1151 3903 
Q 1059 3809 928 3809 
Q 797 3809 703 3903 
Q 609 3997 609 4128 
Q 609 4259 701 4351 
Q 794 4444 928 4444 
z
M 1188 2947 
L 1188 647 
Q 1188 378 1227 289 
Q 1266 200 1342 156 
Q 1419 113 1622 113 
L 1622 0 
L 231 0 
L 231 113 
Q 441 113 512 153 
Q 584 194 626 287 
Q 669 381 669 647 
L 669 1750 
Q 669 2216 641 2353 
Q 619 2453 572 2492 
Q 525 2531 444 2531 
Q 356 2531 231 2484 
L 188 2597 
L 1050 2947 
L 1188 2947 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-54"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(54.083984 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-74" transform="translate(104.083984 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-61" transform="translate(131.867188 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6c" transform="translate(176.251953 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-20" transform="translate(204.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-53" transform="translate(229.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-63" transform="translate(284.650391 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(329.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-72" transform="translate(379.035156 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-65" transform="translate(412.335938 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-20" transform="translate(456.720703 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-28" transform="translate(481.720703 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-4d" transform="translate(515.021484 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-65" transform="translate(603.9375 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-74" transform="translate(648.322266 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-72" transform="translate(676.105469 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-69" transform="translate(709.40625 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-63" transform="translate(737.189453 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-29" transform="translate(781.574219 0)"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_7"/>
    <g id="ytick_8"/>
    <g id="ytick_9"/>
    <g id="ytick_10"/>
   </g>
   <g id="LineCollection_1">
    <path d="M 187.553889 210.96 
L 257.964684 210.96 
" clip-path="url(#p686fd13a4b)" style="fill: none; stroke: #21918c; stroke-opacity: 0.8; stroke-width: 2"/>
    <path d="M 87.782866 357.66 
L 257.964684 357.66 
" clip-path="url(#p686fd13a4b)" style="fill: none; stroke: #21918c; stroke-opacity: 0.8; stroke-width: 2"/>
    <path d="M 206.769049 504.36 
L 257.964684 504.36 
" clip-path="url(#p686fd13a4b)" style="fill: none; stroke: #21918c; stroke-opacity: 0.8; stroke-width: 2"/>
    <path d="M 187.251552 651.06 
L 257.964684 651.06 
" clip-path="url(#p686fd13a4b)" style="fill: none; stroke: #21918c; stroke-opacity: 0.8; stroke-width: 2"/>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="mc2bfd0f769" d="M 0 4.472136 
C 1.186024 4.472136 2.323632 4.000923 3.162278 3.162278 
C 4.000923 2.323632 4.472136 1.186024 4.472136 0 
C 4.472136 -1.186024 4.000923 -2.323632 3.162278 -3.162278 
C 2.323632 -4.000923 1.186024 -4.472136 0 -4.472136 
C -1.186024 -4.472136 -2.323632 -4.000923 -3.162278 -3.162278 
C -4.000923 -2.323632 -4.472136 -1.186024 -4.472136 0 
C -4.472136 1.186024 -4.000923 2.323632 -3.162278 3.162278 
C -2.323632 4.000923 -1.186024 4.472136 0 4.472136 
z
" style="stroke: #21918c"/>
    </defs>
    <g clip-path="url(#p686fd13a4b)">
     <use xlink:href="#mc2bfd0f769" x="187.553889" y="210.96" style="fill: #21918c; stroke: #21918c"/>
     <use xlink:href="#mc2bfd0f769" x="87.782866" y="357.66" style="fill: #21918c; stroke: #21918c"/>
     <use xlink:href="#mc2bfd0f769" x="206.769049" y="504.36" style="fill: #21918c; stroke: #21918c"/>
     <use xlink:href="#mc2bfd0f769" x="187.251552" y="651.06" style="fill: #21918c; stroke: #21918c"/>
    </g>
   </g>
  </g>
  <g id="axes_4">
   <g id="patch_14">
    <path d="M 969.673775 797.76 
L 1000.873775 797.76 
L 1000.873775 210.96 
L 969.673775 210.96 
L 969.673775 797.76 
z
" style="fill: none; opacity: 0"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image0d74446526" transform="scale(1 -1) translate(0 -586.8)" x="969.6" y="-210.96" width="31.2" height="586.8"/>
   <g id="matplotlib.axis_7"/>
   <g id="matplotlib.axis_8">
    <g id="ytick_11">
     <g id="line2d_22">
      <g>
       <use xlink:href="#m83ff84fbe9" x="1000.873775" y="742.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 10 -->
      <g transform="translate(1007.873775 751.08) scale(0.24 -0.24)">
       <use xlink:href="#TimesNewRomanPSMT-31"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m83ff84fbe9" x="1000.873775" y="663.019239" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 20 -->
      <g transform="translate(1007.873775 671.351739) scale(0.24 -0.24)">
       <defs>
        <path id="TimesNewRomanPSMT-32" d="M 2934 816 
L 2638 0 
L 138 0 
L 138 116 
Q 1241 1122 1691 1759 
Q 2141 2397 2141 2925 
Q 2141 3328 1894 3587 
Q 1647 3847 1303 3847 
Q 991 3847 742 3664 
Q 494 3481 375 3128 
L 259 3128 
Q 338 3706 661 4015 
Q 984 4325 1469 4325 
Q 1984 4325 2329 3994 
Q 2675 3663 2675 3213 
Q 2675 2891 2525 2569 
Q 2294 2063 1775 1497 
Q 997 647 803 472 
L 1909 472 
Q 2247 472 2383 497 
Q 2519 522 2628 598 
Q 2738 675 2819 816 
L 2934 816 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-32"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m83ff84fbe9" x="1000.873775" y="583.290978" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- 30 -->
      <g transform="translate(1007.873775 591.623478) scale(0.24 -0.24)">
       <defs>
        <path id="TimesNewRomanPSMT-33" d="M 325 3431 
Q 506 3859 782 4092 
Q 1059 4325 1472 4325 
Q 1981 4325 2253 3994 
Q 2459 3747 2459 3466 
Q 2459 3003 1878 2509 
Q 2269 2356 2469 2072 
Q 2669 1788 2669 1403 
Q 2669 853 2319 450 
Q 1863 -75 997 -75 
Q 569 -75 414 31 
Q 259 138 259 259 
Q 259 350 332 419 
Q 406 488 509 488 
Q 588 488 669 463 
Q 722 447 909 348 
Q 1097 250 1169 231 
Q 1284 197 1416 197 
Q 1734 197 1970 444 
Q 2206 691 2206 1028 
Q 2206 1275 2097 1509 
Q 2016 1684 1919 1775 
Q 1784 1900 1550 2001 
Q 1316 2103 1072 2103 
L 972 2103 
L 972 2197 
Q 1219 2228 1467 2375 
Q 1716 2522 1828 2728 
Q 1941 2934 1941 3181 
Q 1941 3503 1739 3701 
Q 1538 3900 1238 3900 
Q 753 3900 428 3381 
L 325 3431 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-33"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_25">
      <g>
       <use xlink:href="#m83ff84fbe9" x="1000.873775" y="503.562717" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- 40 -->
      <g transform="translate(1007.873775 511.895217) scale(0.24 -0.24)">
       <defs>
        <path id="TimesNewRomanPSMT-34" d="M 2978 1563 
L 2978 1119 
L 2409 1119 
L 2409 0 
L 1894 0 
L 1894 1119 
L 100 1119 
L 100 1519 
L 2066 4325 
L 2409 4325 
L 2409 1563 
L 2978 1563 
z
M 1894 1563 
L 1894 3666 
L 406 1563 
L 1894 1563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-34"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_26">
      <g>
       <use xlink:href="#m83ff84fbe9" x="1000.873775" y="423.834457" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 50 -->
      <g transform="translate(1007.873775 432.166957) scale(0.24 -0.24)">
       <use xlink:href="#TimesNewRomanPSMT-35"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_27">
      <g>
       <use xlink:href="#m83ff84fbe9" x="1000.873775" y="344.106196" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 60 -->
      <g transform="translate(1007.873775 352.438696) scale(0.24 -0.24)">
       <defs>
        <path id="TimesNewRomanPSMT-36" d="M 2869 4325 
L 2869 4209 
Q 2456 4169 2195 4045 
Q 1934 3922 1679 3669 
Q 1425 3416 1258 3105 
Q 1091 2794 978 2366 
Q 1428 2675 1881 2675 
Q 2316 2675 2634 2325 
Q 2953 1975 2953 1425 
Q 2953 894 2631 456 
Q 2244 -75 1606 -75 
Q 1172 -75 869 213 
Q 275 772 275 1663 
Q 275 2231 503 2743 
Q 731 3256 1154 3653 
Q 1578 4050 1965 4187 
Q 2353 4325 2688 4325 
L 2869 4325 
z
M 925 2138 
Q 869 1716 869 1456 
Q 869 1156 980 804 
Q 1091 453 1309 247 
Q 1469 100 1697 100 
Q 1969 100 2183 356 
Q 2397 613 2397 1088 
Q 2397 1622 2184 2012 
Q 1972 2403 1581 2403 
Q 1463 2403 1327 2353 
Q 1191 2303 925 2138 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-36"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_28">
      <g>
       <use xlink:href="#m83ff84fbe9" x="1000.873775" y="264.377935" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 70 -->
      <g transform="translate(1007.873775 272.710435) scale(0.24 -0.24)">
       <use xlink:href="#TimesNewRomanPSMT-37"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- Score -->
     <g transform="translate(1055.316275 536.23625) rotate(-90) scale(0.28 -0.28)">
      <use xlink:href="#TimesNewRomanPSMT-53"/>
      <use xlink:href="#TimesNewRomanPSMT-63" transform="translate(55.615234 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(100 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-72" transform="translate(150 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-65" transform="translate(183.300781 0)"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_2"/>
   <g id="patch_15">
    <path d="M 969.673775 797.76 
L 985.273775 797.76 
L 1000.873775 797.76 
L 1000.873775 210.96 
L 985.273775 210.96 
L 969.673775 210.96 
L 969.673775 797.76 
z
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p285d8b6577">
   <rect x="324.073775" y="210.96" width="624" height="586.8"/>
  </clipPath>
  <clipPath id="p43a6a43f05">
   <rect x="324.073775" y="72" width="624" height="117.36"/>
  </clipPath>
  <clipPath id="p686fd13a4b">
   <rect x="79.273775" y="210.96" width="187.2" height="586.8"/>
  </clipPath>
 </defs>
</svg>
