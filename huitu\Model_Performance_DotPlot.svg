<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="427.050781pt" height="712.475938pt" viewBox="0 0 427.050781 712.475938" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-25T18:19:35.474365</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 712.475938 
L 427.050781 712.475938 
L 427.050781 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 53.727188 674.68 
L 337.735187 674.68 
L 337.735187 7.2 
L 53.727188 7.2 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_1">
    <path d="M 66.636642 49.591971 
C 69.832862 49.591971 72.898603 48.322099 75.158672 46.06203 
C 77.418741 43.801961 78.688613 40.73622 78.688613 37.54 
C 78.688613 34.34378 77.418741 31.278039 75.158672 29.01797 
C 72.898603 26.757901 69.832862 25.488029 66.636642 25.488029 
C 63.440422 25.488029 60.374681 26.757901 58.114612 29.01797 
C 55.854543 31.278039 54.584671 34.34378 54.584671 37.54 
C 54.584671 40.73622 55.854543 43.801961 58.114612 46.06203 
C 60.374681 48.322099 63.440422 49.591971 66.636642 49.591971 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #8ace88; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 54.864116 
C 157.294082 54.864116 161.700934 53.038738 164.949672 49.79 
C 168.19841 46.541262 170.023788 42.134409 170.023788 37.54 
C 170.023788 32.945591 168.19841 28.538738 164.949672 25.29 
C 161.700934 22.041262 157.294082 20.215884 152.699672 20.215884 
C 148.105263 20.215884 143.69841 22.041262 140.449672 25.29 
C 137.200934 28.538738 135.375556 32.945591 135.375556 37.54 
C 135.375556 42.134409 137.200934 46.541262 140.449672 49.79 
C 143.69841 53.038738 148.105263 54.864116 152.699672 54.864116 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #00441b; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 48.292907 
C 241.614407 48.292907 244.349696 47.159913 246.366156 45.143453 
C 248.382615 43.126994 249.515609 40.391704 249.515609 37.54 
C 249.515609 34.688296 248.382615 31.953006 246.366156 29.936547 
C 244.349696 27.920087 241.614407 26.787093 238.762703 26.787093 
C 235.910998 26.787093 233.175709 27.920087 231.159249 29.936547 
C 229.14279 31.953006 228.009796 34.688296 228.009796 37.54 
C 228.009796 40.391704 229.14279 43.126994 231.159249 45.143453 
C 233.175709 47.159913 235.910998 48.292907 238.762703 48.292907 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #afdfa8; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 49.591971 
C 328.021953 49.591971 331.087694 48.322099 333.347763 46.06203 
C 335.607832 43.801961 336.877704 40.73622 336.877704 37.54 
C 336.877704 34.34378 335.607832 31.278039 333.347763 29.01797 
C 331.087694 26.757901 328.021953 25.488029 324.825733 25.488029 
C 321.629513 25.488029 318.563772 26.757901 316.303703 29.01797 
C 314.043634 31.278039 312.773762 34.34378 312.773762 37.54 
C 312.773762 40.73622 314.043634 43.801961 316.303703 46.06203 
C 318.563772 48.322099 321.629513 49.591971 324.825733 49.591971 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #8ace88; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 124.452323 
C 69.570405 124.452323 72.384403 123.286727 74.458886 121.212244 
C 76.533369 119.137761 77.698965 116.323762 77.698965 113.39 
C 77.698965 110.456238 76.533369 107.642239 74.458886 105.567756 
C 72.384403 103.493273 69.570405 102.327677 66.636642 102.327677 
C 63.70288 102.327677 60.888881 103.493273 58.814398 105.567756 
C 56.739915 107.642239 55.574319 110.456238 55.574319 113.39 
C 55.574319 116.323762 56.739915 119.137761 58.814398 121.212244 
C 60.888881 123.286727 63.70288 124.452323 66.636642 124.452323 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #a7dba0; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 129.722483 
C 157.031097 129.722483 161.185702 128.00159 164.248482 124.938809 
C 167.311262 121.876029 169.032155 117.721425 169.032155 113.39 
C 169.032155 109.058575 167.311262 104.903971 164.248482 101.841191 
C 161.185702 98.77841 157.031097 97.057517 152.699672 97.057517 
C 148.368247 97.057517 144.213643 98.77841 141.150863 101.841191 
C 138.088083 104.903971 136.367189 109.058575 136.367189 113.39 
C 136.367189 117.721425 138.088083 121.876029 141.150863 124.938809 
C 144.213643 128.00159 148.368247 129.722483 152.699672 129.722483 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #006b2b; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 123.624745 
C 241.476989 123.624745 244.08047 122.546347 245.99976 120.627057 
C 247.91905 118.707767 248.997447 116.104286 248.997447 113.39 
C 248.997447 110.675714 247.91905 108.072233 245.99976 106.152943 
C 244.08047 104.233653 241.476989 103.155255 238.762703 103.155255 
C 236.048417 103.155255 233.444935 104.233653 231.525645 106.152943 
C 229.606355 108.072233 228.527958 110.675714 228.527958 113.39 
C 228.527958 116.104286 229.606355 118.707767 231.525645 120.627057 
C 233.444935 122.546347 236.048417 123.624745 238.762703 123.624745 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #bbe4b4; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 124.486171 
C 327.768472 124.486171 330.59108 123.317008 332.67191 121.236177 
C 334.752741 119.155347 335.921903 116.332739 335.921903 113.39 
C 335.921903 110.447261 334.752741 107.624653 332.67191 105.543823 
C 330.59108 103.462992 327.768472 102.293829 324.825733 102.293829 
C 321.882994 102.293829 319.060386 103.462992 316.979556 105.543823 
C 314.898725 107.624653 313.729562 110.447261 313.729562 113.39 
C 313.729562 116.332739 314.898725 119.155347 316.979556 121.236177 
C 319.060386 123.317008 321.882994 124.486171 324.825733 124.486171 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #a7dba0; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 199.92293 
C 69.469788 199.92293 72.187278 198.797309 74.190615 196.793972 
C 76.193951 194.790636 77.319572 192.073146 77.319572 189.24 
C 77.319572 186.406854 76.193951 183.689364 74.190615 181.686028 
C 72.187278 179.682691 69.469788 178.55707 66.636642 178.55707 
C 63.803496 178.55707 61.086006 179.682691 59.08267 181.686028 
C 57.079333 183.689364 55.953712 186.406854 55.953712 189.24 
C 55.953712 192.073146 57.079333 194.790636 59.08267 196.793972 
C 61.086006 198.797309 63.803496 199.92293 66.636642 199.92293 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #b0dfaa; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 204.577862 
C 156.767321 204.577862 160.668917 202.961768 163.545178 200.085506 
C 166.42144 197.209244 168.037534 193.307648 168.037534 189.24 
C 168.037534 185.172352 166.42144 181.270756 163.545178 178.394494 
C 160.668917 175.518232 156.767321 173.902138 152.699672 173.902138 
C 148.632024 173.902138 144.730428 175.518232 141.854166 178.394494 
C 138.977905 181.270756 137.361811 185.172352 137.361811 189.24 
C 137.361811 193.307648 138.977905 197.209244 141.854166 200.085506 
C 144.730428 202.961768 148.632024 204.577862 152.699672 204.577862 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #1f8742; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 199.419637 
C 241.462374 199.419637 244.051837 198.347046 245.960793 196.43809 
C 247.869748 194.529134 248.942339 191.939671 248.942339 189.24 
C 248.942339 186.540329 247.869748 183.950866 245.960793 182.04191 
C 244.051837 180.132954 241.462374 179.060363 238.762703 179.060363 
C 236.063031 179.060363 233.473568 180.132954 231.564613 182.04191 
C 229.655657 183.950866 228.583066 186.540329 228.583066 189.24 
C 228.583066 191.939671 229.655657 194.529134 231.564613 196.43809 
C 233.473568 198.347046 236.063031 199.419637 238.762703 199.419637 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #bce4b5; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 199.957976 
C 327.668173 199.957976 330.394577 198.828662 332.404486 196.818753 
C 334.414395 194.808844 335.543709 192.08244 335.543709 189.24 
C 335.543709 186.39756 334.414395 183.671156 332.404486 181.661247 
C 330.394577 179.651338 327.668173 178.522024 324.825733 178.522024 
C 321.983293 178.522024 319.256889 179.651338 317.24698 181.661247 
C 315.237071 183.671156 314.107757 186.39756 314.107757 189.24 
C 314.107757 192.08244 315.237071 194.808844 317.24698 196.818753 
C 319.256889 198.828662 321.983293 199.957976 324.825733 199.957976 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #afdfa8; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 274.836794 
C 69.221522 274.836794 71.70088 273.809811 73.528666 271.982024 
C 75.356453 270.154238 76.383436 267.67488 76.383436 265.09 
C 76.383436 262.50512 75.356453 260.025762 73.528666 258.197976 
C 71.70088 256.370189 69.221522 255.343206 66.636642 255.343206 
C 64.051762 255.343206 61.572404 256.370189 59.744618 258.197976 
C 57.916831 260.025762 56.889848 262.50512 56.889848 265.09 
C 56.889848 267.67488 57.916831 270.154238 59.744618 271.982024 
C 61.572404 273.809811 64.051762 274.836794 66.636642 274.836794 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #c6e8bf; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 279.688801 
C 156.57132 279.688801 160.284916 278.15058 163.022584 275.412911 
C 165.760252 272.675243 167.298474 268.961647 167.298474 265.09 
C 167.298474 261.218353 165.760252 257.504757 163.022584 254.767089 
C 160.284916 252.02942 156.57132 250.491199 152.699672 250.491199 
C 148.828025 250.491199 145.114429 252.02942 142.376761 254.767089 
C 139.639093 257.504757 138.100871 261.218353 138.100871 265.09 
C 138.100871 268.961647 139.639093 272.675243 142.376761 275.412911 
C 145.114429 278.15058 148.828025 279.688801 152.699672 279.688801 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #329b51; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 273.670501 
C 241.038278 273.670501 243.220958 272.766405 244.830033 271.157331 
C 246.439108 269.548256 247.343204 267.365576 247.343204 265.09 
C 247.343204 262.814424 246.439108 260.631744 244.830033 259.022669 
C 243.220958 257.413595 241.038278 256.509499 238.762703 256.509499 
C 236.487127 256.509499 234.304447 257.413595 232.695372 259.022669 
C 231.086297 260.631744 230.182202 262.814424 230.182202 265.09 
C 230.182202 267.365576 231.086297 269.548256 232.695372 271.157331 
C 234.304447 272.766405 236.487127 273.670501 238.762703 273.670501 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #d9f0d3; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 274.894336 
C 327.425873 274.894336 329.919868 273.861289 331.758445 272.022712 
C 333.597022 270.184136 334.630069 267.69014 334.630069 265.09 
C 334.630069 262.48986 333.597022 259.995864 331.758445 258.157288 
C 329.919868 256.318711 327.425873 255.285664 324.825733 255.285664 
C 322.225593 255.285664 319.731597 256.318711 317.893021 258.157288 
C 316.054444 259.995864 315.021397 262.48986 315.021397 265.09 
C 315.021397 267.69014 316.054444 270.184136 317.893021 272.022712 
C 319.731597 273.861289 322.225593 274.894336 324.825733 274.894336 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #c4e8bd; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 352.153831 
C 69.610585 352.153831 72.463123 350.972271 74.566018 348.869376 
C 76.668913 346.766481 77.850473 343.913943 77.850473 340.94 
C 77.850473 337.966057 76.668913 335.113519 74.566018 333.010624 
C 72.463123 330.907729 69.610585 329.726169 66.636642 329.726169 
C 63.662699 329.726169 60.810161 330.907729 58.707266 333.010624 
C 56.604371 335.113519 55.422811 337.966057 55.422811 340.94 
C 55.422811 343.913943 56.604371 346.766481 58.707266 348.869376 
C 60.810161 350.972271 63.662699 352.153831 66.636642 352.153831 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #a3da9d; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 356.818445 
C 156.910685 356.818445 160.949792 355.145391 163.927428 352.167756 
C 166.905064 349.19012 168.578117 345.151013 168.578117 340.94 
C 168.578117 336.728987 166.905064 332.68988 163.927428 329.712244 
C 160.949792 326.734609 156.910685 325.061555 152.699672 325.061555 
C 148.48866 325.061555 144.449552 326.734609 141.471917 329.712244 
C 138.494281 332.68988 136.821228 336.728987 136.821228 340.94 
C 136.821228 345.151013 138.494281 349.19012 141.471917 352.167756 
C 144.449552 355.145391 148.48866 356.818445 152.699672 356.818445 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #0d7836; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 351.082731 
C 241.452586 351.082731 244.032662 350.014029 245.934697 348.111994 
C 247.836732 346.209959 248.905434 343.629884 248.905434 340.94 
C 248.905434 338.250116 247.836732 335.670041 245.934697 333.768006 
C 244.032662 331.865971 241.452586 330.797269 238.762703 330.797269 
C 236.072819 330.797269 233.492744 331.865971 231.590709 333.768006 
C 229.688673 335.670041 228.619971 338.250116 228.619971 340.94 
C 228.619971 343.629884 229.688673 346.209959 231.590709 348.111994 
C 233.492744 350.014029 236.072819 351.082731 238.762703 351.082731 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #bce4b5; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 352.137098 
C 327.795238 352.137098 330.64352 350.957301 332.743277 348.857544 
C 334.843034 346.757787 336.022831 343.909505 336.022831 340.94 
C 336.022831 337.970495 334.843034 335.122213 332.743277 333.022456 
C 330.64352 330.922699 327.795238 329.742902 324.825733 329.742902 
C 321.856228 329.742902 319.007946 330.922699 316.908189 333.022456 
C 314.808432 335.122213 313.628635 337.970495 313.628635 340.94 
C 313.628635 343.909505 314.808432 346.757787 316.908189 348.857544 
C 319.007946 350.957301 321.856228 352.137098 324.825733 352.137098 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #a4da9e; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 425.796942 
C 69.025311 425.796942 71.316468 424.847914 73.005512 423.15887 
C 74.694556 421.469826 75.643584 419.178669 75.643584 416.79 
C 75.643584 414.401331 74.694556 412.110174 73.005512 410.42113 
C 71.316468 408.732086 69.025311 407.783058 66.636642 407.783058 
C 64.247973 407.783058 61.956816 408.732086 60.267772 410.42113 
C 58.578728 412.110174 57.6297 414.401331 57.6297 416.79 
C 57.6297 419.178669 58.578728 421.469826 60.267772 423.15887 
C 61.956816 424.847914 64.247973 425.796942 66.636642 425.796942 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #d2edcc; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 430.785535 
C 156.411332 430.785535 159.971471 429.310877 162.59601 426.686338 
C 165.220549 424.061798 166.695207 420.501659 166.695207 416.79 
C 166.695207 413.078341 165.220549 409.518202 162.59601 406.893662 
C 159.971471 404.269123 156.411332 402.794465 152.699672 402.794465 
C 148.988013 402.794465 145.427874 404.269123 142.803335 406.893662 
C 140.178795 409.518202 138.704137 413.078341 138.704137 416.79 
C 138.704137 420.501659 140.178795 424.061798 142.803335 426.686338 
C 145.427874 429.310877 148.988013 430.785535 152.699672 430.785535 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #42ab5d; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 424.121439 
C 240.707023 424.121439 242.571971 423.348952 243.946813 421.97411 
C 245.321655 420.599268 246.094142 418.73432 246.094142 416.79 
C 246.094142 414.84568 245.321655 412.980732 243.946813 411.60589 
C 242.571971 410.231048 240.707023 409.458561 238.762703 409.458561 
C 236.818382 409.458561 234.953434 410.231048 233.578592 411.60589 
C 232.20375 412.980732 231.431264 414.84568 231.431264 416.79 
C 231.431264 418.73432 232.20375 420.599268 233.578592 421.97411 
C 234.953434 423.348952 236.818382 424.121439 238.762703 424.121439 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #e8f6e3; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 425.817735 
C 327.219916 425.817735 329.516362 424.866516 331.209306 423.173573 
C 332.902249 421.480629 333.853468 419.184183 333.853468 416.79 
C 333.853468 414.395817 332.902249 412.099371 331.209306 410.406427 
C 329.516362 408.713484 327.219916 407.762265 324.825733 407.762265 
C 322.43155 407.762265 320.135104 408.713484 318.44216 410.406427 
C 316.749217 412.099371 315.797998 414.395817 315.797998 416.79 
C 315.797998 419.184183 316.749217 421.480629 318.44216 423.173573 
C 320.135104 424.866516 322.43155 425.817735 324.825733 425.817735 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #d1edcb; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 502.406012 
C 69.226619 502.406012 71.710866 501.377004 73.542256 499.545614 
C 75.373646 497.714224 76.402655 495.229977 76.402655 492.64 
C 76.402655 490.050023 75.373646 487.565776 73.542256 485.734386 
C 71.710866 483.902996 69.226619 482.873988 66.636642 482.873988 
C 64.046665 482.873988 61.562419 483.902996 59.731028 485.734386 
C 57.899638 487.565776 56.87063 490.050023 56.87063 492.64 
C 56.87063 495.229977 57.899638 497.714224 59.731028 499.545614 
C 61.562419 501.377004 64.046665 502.406012 66.636642 502.406012 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #c4e8bd; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 507.174442 
C 156.554251 507.174442 160.251476 505.643001 162.977075 502.917402 
C 165.702674 500.191803 167.234114 496.494579 167.234114 492.64 
C 167.234114 488.785421 165.702674 485.088197 162.977075 482.362598 
C 160.251476 479.636999 156.554251 478.105558 152.699672 478.105558 
C 148.845093 478.105558 145.147869 479.636999 142.42227 482.362598 
C 139.696671 485.088197 138.16523 488.785421 138.16523 492.64 
C 138.16523 496.494579 139.696671 500.191803 142.42227 502.917402 
C 145.147869 505.643001 148.845093 507.174442 152.699672 507.174442 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #349d53; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 500.999127 
C 240.979569 500.999127 243.105937 500.118356 244.673498 498.550795 
C 246.241059 496.983234 247.121829 494.856866 247.121829 492.64 
C 247.121829 490.423134 246.241059 488.296766 244.673498 486.729205 
C 243.105937 485.161644 240.979569 484.280873 238.762703 484.280873 
C 236.545836 484.280873 234.419469 485.161644 232.851907 486.729205 
C 231.284346 488.296766 230.403576 490.423134 230.403576 492.64 
C 230.403576 494.856866 231.284346 496.983234 232.851907 498.550795 
C 234.419469 500.118356 236.545836 500.999127 238.762703 500.999127 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #dbf1d6; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 502.425193 
C 327.420796 502.425193 329.909922 501.394163 331.744909 499.559176 
C 333.579896 497.724189 334.610926 495.235063 334.610926 492.64 
C 334.610926 490.044937 333.579896 487.555811 331.744909 485.720824 
C 329.909922 483.885837 327.420796 482.854807 324.825733 482.854807 
C 322.230669 482.854807 319.741544 483.885837 317.906557 485.720824 
C 316.07157 487.555811 315.04054 490.044937 315.04054 492.64 
C 315.04054 495.235063 316.07157 497.724189 317.906557 499.559176 
C 319.741544 501.394163 322.230669 502.425193 324.825733 502.425193 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #c4e8bd; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 576.50561 
C 68.762407 576.50561 70.801392 575.661035 72.304534 574.157892 
C 73.807677 572.654749 74.652252 570.615765 74.652252 568.49 
C 74.652252 566.364235 73.807677 564.325251 72.304534 562.822108 
C 70.801392 561.318965 68.762407 560.47439 66.636642 560.47439 
C 64.510877 560.47439 62.471893 561.318965 60.96875 562.822108 
C 59.465607 564.325251 58.621032 566.364235 58.621032 568.49 
C 58.621032 570.615765 59.465607 572.654749 60.96875 574.157892 
C 62.471893 575.661035 64.510877 576.50561 66.636642 576.50561 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #e0f3db; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 581.784736 
C 156.225477 581.784736 159.607349 580.383919 162.10047 577.890798 
C 164.593591 575.397677 165.994408 572.015805 165.994408 568.49 
C 165.994408 564.964195 164.593591 561.582323 162.10047 559.089202 
C 159.607349 556.596081 156.225477 555.195264 152.699672 555.195264 
C 149.173867 555.195264 145.791995 556.596081 143.298875 559.089202 
C 140.805754 561.582323 139.404937 564.964195 139.404937 568.49 
C 139.404937 572.015805 140.805754 575.397677 143.298875 577.890798 
C 145.791995 580.383919 149.173867 581.784736 152.699672 581.784736 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #5eb96b; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 574.46913 
C 240.348387 574.46913 241.869338 573.839131 242.990586 572.717884 
C 244.111834 571.596636 244.741833 570.075684 244.741833 568.49 
C 244.741833 566.904316 244.111834 565.383364 242.990586 564.262116 
C 241.869338 563.140869 240.348387 562.51087 238.762703 562.51087 
C 237.177019 562.51087 235.656067 563.140869 234.534819 564.262116 
C 233.413571 565.383364 232.783572 566.904316 232.783572 568.49 
C 232.783572 570.075684 233.413571 571.596636 234.534819 572.717884 
C 235.656067 573.839131 237.177019 574.46913 238.762703 574.46913 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #f1faee; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 576.528968 
C 326.957692 576.528968 329.002619 575.681931 330.510141 574.174409 
C 332.017664 572.666886 332.864701 570.621959 332.864701 568.49 
C 332.864701 566.358041 332.017664 564.313114 330.510141 562.805591 
C 329.002619 561.298069 326.957692 560.451032 324.825733 560.451032 
C 322.693774 560.451032 320.648847 561.298069 319.141324 562.805591 
C 317.633802 564.313114 316.786765 566.358041 316.786765 568.49 
C 316.786765 570.621959 317.633802 572.666886 319.141324 574.174409 
C 320.648847 575.681931 322.693774 576.528968 324.825733 576.528968 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #e0f3db; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 66.636642 651.773034 
C 68.607906 651.773034 70.498697 650.989843 71.892591 649.595949 
C 73.286485 648.202055 74.069676 646.311264 74.069676 644.34 
C 74.069676 642.368736 73.286485 640.477945 71.892591 639.084051 
C 70.498697 637.690157 68.607906 636.906966 66.636642 636.906966 
C 64.665378 636.906966 62.774587 637.690157 61.380693 639.084051 
C 59.986799 640.477945 59.203608 642.368736 59.203608 644.34 
C 59.203608 646.311264 59.986799 648.202055 61.380693 649.595949 
C 62.774587 650.989843 64.665378 651.773034 66.636642 651.773034 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #e7f6e3; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 152.699672 657.028578 
C 156.064722 657.028578 159.292402 655.691629 161.671852 653.312179 
C 164.051301 650.932729 165.38825 647.70505 165.38825 644.34 
C 165.38825 640.97495 164.051301 637.747271 161.671852 635.367821 
C 159.292402 632.988371 156.064722 631.651422 152.699672 631.651422 
C 149.334622 631.651422 146.106943 632.988371 143.727493 635.367821 
C 141.348043 637.747271 140.011095 640.97495 140.011095 644.34 
C 140.011095 647.70505 141.348043 650.932729 143.727493 653.312179 
C 146.106943 655.691629 149.334622 657.028578 152.699672 657.028578 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #76c578; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 238.762703 649.251721 
C 240.065306 649.251721 241.314734 648.734191 242.235814 647.813111 
C 243.156893 646.892031 243.674423 645.642604 243.674423 644.34 
C 243.674423 643.037396 243.156893 641.787969 242.235814 640.866889 
C 241.314734 639.945809 240.065306 639.428279 238.762703 639.428279 
C 237.460099 639.428279 236.210671 639.945809 235.289592 640.866889 
C 234.368512 641.787969 233.850982 643.037396 233.850982 644.34 
C 233.850982 645.642604 234.368512 646.892031 235.289592 647.813111 
C 236.210671 648.734191 237.460099 649.251721 238.762703 649.251721 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #f7fcf5; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
    <path d="M 324.825733 651.773034 
C 326.796997 651.773034 328.687788 650.989843 330.081682 649.595949 
C 331.475576 648.202055 332.258767 646.311264 332.258767 644.34 
C 332.258767 642.368736 331.475576 640.477945 330.081682 639.084051 
C 328.687788 637.690157 326.796997 636.906966 324.825733 636.906966 
C 322.854469 636.906966 320.963678 637.690157 319.569784 639.084051 
C 318.17589 640.477945 317.392699 642.368736 317.392699 644.34 
C 317.392699 646.311264 318.17589 648.202055 319.569784 649.595949 
C 320.963678 650.989843 322.854469 651.773034 324.825733 651.773034 
z
" clip-path="url(#pa6d9fb2606)" style="fill: #e7f6e3; fill-opacity: 0.7; stroke: #a9a9a9; stroke-opacity: 0.7; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m4ea384bea3" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4ea384bea3" x="66.636642" y="674.68" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- AP -->
      <g transform="translate(60.245236 688.62375) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-41" d="M 2928 1419 
L 1288 1419 
L 1000 750 
Q 894 503 894 381 
Q 894 284 986 211 
Q 1078 138 1384 116 
L 1384 0 
L 50 0 
L 50 116 
Q 316 163 394 238 
Q 553 388 747 847 
L 2238 4334 
L 2347 4334 
L 3822 809 
Q 4000 384 4145 257 
Q 4291 131 4550 116 
L 4550 0 
L 2878 0 
L 2878 116 
Q 3131 128 3220 200 
Q 3309 272 3309 375 
Q 3309 513 3184 809 
L 2928 1419 
z
M 2841 1650 
L 2122 3363 
L 1384 1650 
L 2841 1650 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-50" d="M 1313 1984 
L 1313 750 
Q 1313 350 1400 253 
Q 1519 116 1759 116 
L 1922 116 
L 1922 0 
L 106 0 
L 106 116 
L 266 116 
Q 534 116 650 291 
Q 713 388 713 750 
L 713 3488 
Q 713 3888 628 3984 
Q 506 4122 266 4122 
L 106 4122 
L 106 4238 
L 1659 4238 
Q 2228 4238 2556 4120 
Q 2884 4003 3109 3725 
Q 3334 3447 3334 3066 
Q 3334 2547 2992 2222 
Q 2650 1897 2025 1897 
Q 1872 1897 1694 1919 
Q 1516 1941 1313 1984 
z
M 1313 2163 
Q 1478 2131 1606 2115 
Q 1734 2100 1825 2100 
Q 2150 2100 2386 2351 
Q 2622 2603 2622 3003 
Q 2622 3278 2509 3514 
Q 2397 3750 2190 3867 
Q 1984 3984 1722 3984 
Q 1563 3984 1313 3925 
L 1313 2163 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m4ea384bea3" x="152.699672" y="674.68" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- AP50 -->
      <g transform="translate(141.308266 688.62375) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-35" d="M 2778 4238 
L 2534 3706 
L 1259 3706 
L 981 3138 
Q 1809 3016 2294 2522 
Q 2709 2097 2709 1522 
Q 2709 1188 2573 903 
Q 2438 619 2231 419 
Q 2025 219 1772 97 
Q 1413 -75 1034 -75 
Q 653 -75 479 54 
Q 306 184 306 341 
Q 306 428 378 495 
Q 450 563 559 563 
Q 641 563 702 538 
Q 763 513 909 409 
Q 1144 247 1384 247 
Q 1750 247 2026 523 
Q 2303 800 2303 1197 
Q 2303 1581 2056 1914 
Q 1809 2247 1375 2428 
Q 1034 2569 447 2591 
L 1259 4238 
L 2778 4238 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-30" d="M 231 2094 
Q 231 2819 450 3342 
Q 669 3866 1031 4122 
Q 1313 4325 1613 4325 
Q 2100 4325 2488 3828 
Q 2972 3213 2972 2159 
Q 2972 1422 2759 906 
Q 2547 391 2217 158 
Q 1888 -75 1581 -75 
Q 975 -75 572 641 
Q 231 1244 231 2094 
z
M 844 2016 
Q 844 1141 1059 588 
Q 1238 122 1591 122 
Q 1759 122 1940 273 
Q 2122 425 2216 781 
Q 2359 1319 2359 2297 
Q 2359 3022 2209 3506 
Q 2097 3866 1919 4016 
Q 1791 4119 1609 4119 
Q 1397 4119 1231 3928 
Q 1006 3669 925 3112 
Q 844 2556 844 2016 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(127.832031 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(177.832031 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m4ea384bea3" x="238.762703" y="674.68" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- AP75 -->
      <g transform="translate(227.371296 688.62375) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-37" d="M 644 4238 
L 2916 4238 
L 2916 4119 
L 1503 -88 
L 1153 -88 
L 2419 3728 
L 1253 3728 
Q 900 3728 750 3644 
Q 488 3500 328 3200 
L 238 3234 
L 644 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(127.832031 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(177.832031 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m4ea384bea3" x="324.825733" y="674.68" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- API -->
      <g transform="translate(316.769483 688.62375) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-49" d="M 1975 116 
L 1975 0 
L 159 0 
L 159 116 
L 309 116 
Q 572 116 691 269 
Q 766 369 766 750 
L 766 3488 
Q 766 3809 725 3913 
Q 694 3991 597 4047 
Q 459 4122 309 4122 
L 159 4122 
L 159 4238 
L 1975 4238 
L 1975 4122 
L 1822 4122 
Q 1563 4122 1444 3969 
Q 1366 3869 1366 3488 
L 1366 750 
Q 1366 428 1406 325 
Q 1438 247 1538 191 
Q 1672 116 1822 116 
L 1975 116 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-49" transform="translate(127.832031 0)"/>
      </g>
     </g>
    </g>
    <g id="text_5">
     <!-- Metrics -->
     <g transform="translate(176.077437 702.709063) scale(0.12 -0.12)">
      <defs>
       <path id="TimesNewRomanPS-BoldMT-4d" d="M 3050 1444 
L 4200 4238 
L 5925 4238 
L 5925 4122 
L 5788 4122 
Q 5600 4122 5488 4056 
Q 5409 4013 5363 3909 
Q 5328 3834 5328 3519 
L 5328 725 
Q 5328 400 5362 319 
Q 5397 238 5503 177 
Q 5609 116 5788 116 
L 5925 116 
L 5925 0 
L 3713 0 
L 3713 116 
L 3850 116 
Q 4038 116 4150 181 
Q 4228 225 4275 331 
Q 4309 406 4309 725 
L 4309 3866 
L 2684 0 
L 2609 0 
L 959 3838 
L 959 853 
Q 959 541 975 469 
Q 1016 313 1152 214 
Q 1288 116 1578 116 
L 1578 0 
L 128 0 
L 128 116 
L 172 116 
Q 313 113 434 161 
Q 556 209 618 290 
Q 681 372 716 519 
Q 722 553 722 838 
L 722 3519 
Q 722 3841 687 3920 
Q 653 4000 547 4061 
Q 441 4122 263 4122 
L 128 4122 
L 128 4238 
L 1859 4238 
L 3050 1444 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-65" d="M 2691 1566 
L 1088 1566 
Q 1116 984 1397 647 
Q 1613 388 1916 388 
Q 2103 388 2256 492 
Q 2409 597 2584 869 
L 2691 800 
Q 2453 316 2165 114 
Q 1878 -88 1500 -88 
Q 850 -88 516 413 
Q 247 816 247 1413 
Q 247 2144 642 2576 
Q 1038 3009 1569 3009 
Q 2013 3009 2339 2645 
Q 2666 2281 2691 1566 
z
M 1922 1775 
Q 1922 2278 1867 2465 
Q 1813 2653 1697 2750 
Q 1631 2806 1522 2806 
Q 1359 2806 1256 2647 
Q 1072 2369 1072 1884 
L 1072 1775 
L 1922 1775 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-74" d="M 1375 3991 
L 1375 2922 
L 2069 2922 
L 2069 2613 
L 1375 2613 
L 1375 809 
Q 1375 556 1398 482 
Q 1422 409 1481 364 
Q 1541 319 1591 319 
Q 1794 319 1975 628 
L 2069 559 
Q 1816 -41 1247 -41 
Q 969 -41 776 114 
Q 584 269 531 459 
Q 500 566 500 1034 
L 500 2613 
L 119 2613 
L 119 2722 
Q 513 3000 789 3306 
Q 1066 3613 1272 3991 
L 1375 3991 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-72" d="M 1428 2922 
L 1428 2259 
Q 1719 2713 1937 2861 
Q 2156 3009 2359 3009 
Q 2534 3009 2639 2901 
Q 2744 2794 2744 2597 
Q 2744 2388 2642 2272 
Q 2541 2156 2397 2156 
Q 2231 2156 2109 2262 
Q 1988 2369 1966 2381 
Q 1934 2400 1894 2400 
Q 1803 2400 1722 2331 
Q 1594 2225 1528 2028 
Q 1428 1725 1428 1359 
L 1428 688 
L 1431 513 
Q 1431 334 1453 284 
Q 1491 200 1564 161 
Q 1638 122 1813 113 
L 1813 0 
L 234 0 
L 234 113 
Q 425 128 492 217 
Q 559 306 559 688 
L 559 2303 
Q 559 2553 534 2622 
Q 503 2709 443 2750 
Q 384 2791 234 2806 
L 234 2922 
L 1428 2922 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-69" d="M 919 4338 
Q 1122 4338 1262 4195 
Q 1403 4053 1403 3853 
Q 1403 3653 1261 3512 
Q 1119 3372 919 3372 
Q 719 3372 578 3512 
Q 438 3653 438 3853 
Q 438 4053 578 4195 
Q 719 4338 919 4338 
z
M 1356 2922 
L 1356 606 
Q 1356 297 1428 211 
Q 1500 125 1709 113 
L 1709 0 
L 131 0 
L 131 113 
Q 325 119 419 225 
Q 481 297 481 606 
L 481 2313 
Q 481 2622 409 2708 
Q 338 2794 131 2806 
L 131 2922 
L 1356 2922 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-63" d="M 2600 753 
L 2697 678 
Q 2491 291 2183 101 
Q 1875 -88 1528 -88 
Q 944 -88 594 353 
Q 244 794 244 1413 
Q 244 2009 563 2463 
Q 947 3009 1622 3009 
Q 2075 3009 2342 2781 
Q 2609 2553 2609 2272 
Q 2609 2094 2501 1987 
Q 2394 1881 2219 1881 
Q 2034 1881 1914 2003 
Q 1794 2125 1766 2438 
Q 1747 2634 1675 2713 
Q 1603 2791 1506 2791 
Q 1356 2791 1250 2631 
Q 1088 2391 1088 1894 
Q 1088 1481 1219 1104 
Q 1350 728 1578 544 
Q 1750 409 1984 409 
Q 2138 409 2275 481 
Q 2413 553 2600 753 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-73" d="M 2063 3003 
L 2109 2006 
L 2003 2006 
Q 1813 2450 1608 2612 
Q 1403 2775 1200 2775 
Q 1072 2775 981 2689 
Q 891 2603 891 2491 
Q 891 2406 953 2328 
Q 1053 2200 1512 1889 
Q 1972 1578 2117 1361 
Q 2263 1144 2263 875 
Q 2263 631 2141 397 
Q 2019 163 1797 37 
Q 1575 -88 1306 -88 
Q 1097 -88 747 44 
Q 653 78 619 78 
Q 516 78 447 -78 
L 344 -78 
L 294 972 
L 400 972 
Q 541 559 786 353 
Q 1031 147 1250 147 
Q 1400 147 1495 239 
Q 1591 331 1591 463 
Q 1591 613 1497 722 
Q 1403 831 1078 1053 
Q 600 1384 459 1559 
Q 253 1816 253 2125 
Q 253 2463 486 2736 
Q 719 3009 1159 3009 
Q 1397 3009 1619 2894 
Q 1703 2847 1756 2847 
Q 1813 2847 1847 2870 
Q 1881 2894 1956 3003 
L 2063 3003 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPS-BoldMT-4d"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(94.384766 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-74" transform="translate(138.769531 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-72" transform="translate(172.070312 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-69" transform="translate(216.455078 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-63" transform="translate(244.238281 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-73" transform="translate(288.623047 0)"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_5">
      <defs>
       <path id="m2c704e666d" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="37.54" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- CSAF -->
      <g transform="translate(21.713125 41.011875) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-43" d="M 3853 4334 
L 3950 2894 
L 3853 2894 
Q 3659 3541 3300 3825 
Q 2941 4109 2438 4109 
Q 2016 4109 1675 3895 
Q 1334 3681 1139 3212 
Q 944 2744 944 2047 
Q 944 1472 1128 1050 
Q 1313 628 1683 403 
Q 2053 178 2528 178 
Q 2941 178 3256 354 
Q 3572 531 3950 1056 
L 4047 994 
Q 3728 428 3303 165 
Q 2878 -97 2294 -97 
Q 1241 -97 663 684 
Q 231 1266 231 2053 
Q 231 2688 515 3219 
Q 800 3750 1298 4042 
Q 1797 4334 2388 4334 
Q 2847 4334 3294 4109 
Q 3425 4041 3481 4041 
Q 3566 4041 3628 4100 
Q 3709 4184 3744 4334 
L 3853 4334 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-53" d="M 2934 4334 
L 2934 2869 
L 2819 2869 
Q 2763 3291 2617 3541 
Q 2472 3791 2203 3937 
Q 1934 4084 1647 4084 
Q 1322 4084 1109 3886 
Q 897 3688 897 3434 
Q 897 3241 1031 3081 
Q 1225 2847 1953 2456 
Q 2547 2138 2764 1967 
Q 2981 1797 3098 1565 
Q 3216 1334 3216 1081 
Q 3216 600 2842 251 
Q 2469 -97 1881 -97 
Q 1697 -97 1534 -69 
Q 1438 -53 1133 45 
Q 828 144 747 144 
Q 669 144 623 97 
Q 578 50 556 -97 
L 441 -97 
L 441 1356 
L 556 1356 
Q 638 900 775 673 
Q 913 447 1195 297 
Q 1478 147 1816 147 
Q 2206 147 2432 353 
Q 2659 559 2659 841 
Q 2659 997 2573 1156 
Q 2488 1316 2306 1453 
Q 2184 1547 1640 1851 
Q 1097 2156 867 2337 
Q 638 2519 519 2737 
Q 400 2956 400 3219 
Q 400 3675 750 4004 
Q 1100 4334 1641 4334 
Q 1978 4334 2356 4169 
Q 2531 4091 2603 4091 
Q 2684 4091 2736 4139 
Q 2788 4188 2819 4334 
L 2934 4334 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-46" d="M 1309 4006 
L 1309 2341 
L 2081 2341 
Q 2347 2341 2470 2458 
Q 2594 2575 2634 2922 
L 2750 2922 
L 2750 1488 
L 2634 1488 
Q 2631 1734 2570 1850 
Q 2509 1966 2401 2023 
Q 2294 2081 2081 2081 
L 1309 2081 
L 1309 750 
Q 1309 428 1350 325 
Q 1381 247 1481 191 
Q 1619 116 1769 116 
L 1922 116 
L 1922 0 
L 103 0 
L 103 116 
L 253 116 
Q 516 116 634 269 
Q 709 369 709 750 
L 709 3488 
Q 709 3809 669 3913 
Q 638 3991 541 4047 
Q 406 4122 253 4122 
L 103 4122 
L 103 4238 
L 3256 4238 
L 3297 3306 
L 3188 3306 
Q 3106 3603 2998 3742 
Q 2891 3881 2733 3943 
Q 2575 4006 2244 4006 
L 1309 4006 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-43"/>
       <use xlink:href="#TimesNewRomanPSMT-53" transform="translate(66.699219 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-41" transform="translate(122.314453 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-46" transform="translate(194.53125 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="113.39" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- DT -->
      <g transform="translate(24.494375 116.861875) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-4d" d="M 2619 0 
L 981 3566 
L 981 734 
Q 981 344 1066 247 
Q 1181 116 1431 116 
L 1581 116 
L 1581 0 
L 106 0 
L 106 116 
L 256 116 
Q 525 116 638 278 
Q 706 378 706 734 
L 706 3503 
Q 706 3784 644 3909 
Q 600 4000 483 4061 
Q 366 4122 106 4122 
L 106 4238 
L 1306 4238 
L 2844 922 
L 4356 4238 
L 5556 4238 
L 5556 4122 
L 5409 4122 
Q 5138 4122 5025 3959 
Q 4956 3859 4956 3503 
L 4956 734 
Q 4956 344 5044 247 
Q 5159 116 5409 116 
L 5556 116 
L 5556 0 
L 3756 0 
L 3756 116 
L 3906 116 
Q 4178 116 4288 278 
Q 4356 378 4356 734 
L 4356 3566 
L 2722 0 
L 2619 0 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-52" d="M 4325 0 
L 3194 0 
L 1759 1981 
Q 1600 1975 1500 1975 
Q 1459 1975 1412 1976 
Q 1366 1978 1316 1981 
L 1316 750 
Q 1316 350 1403 253 
Q 1522 116 1759 116 
L 1925 116 
L 1925 0 
L 109 0 
L 109 116 
L 269 116 
Q 538 116 653 291 
Q 719 388 719 750 
L 719 3488 
Q 719 3888 631 3984 
Q 509 4122 269 4122 
L 109 4122 
L 109 4238 
L 1653 4238 
Q 2328 4238 2648 4139 
Q 2969 4041 3192 3777 
Q 3416 3513 3416 3147 
Q 3416 2756 3161 2468 
Q 2906 2181 2372 2063 
L 3247 847 
Q 3547 428 3762 290 
Q 3978 153 4325 116 
L 4325 0 
z
M 1316 2178 
Q 1375 2178 1419 2176 
Q 1463 2175 1491 2175 
Q 2097 2175 2405 2437 
Q 2713 2700 2713 3106 
Q 2713 3503 2464 3751 
Q 2216 4000 1806 4000 
Q 1625 4000 1316 3941 
L 1316 2178 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-43"/>
       <use xlink:href="#TimesNewRomanPSMT-4d" transform="translate(66.699219 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-52" transform="translate(155.615234 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="189.24" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- ST -->
      <g transform="translate(35.058438 192.711875) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-54" d="M 3703 4238 
L 3750 3244 
L 3631 3244 
Q 3597 3506 3538 3619 
Q 3441 3800 3280 3886 
Q 3119 3972 2856 3972 
L 2259 3972 
L 2259 734 
Q 2259 344 2344 247 
Q 2463 116 2709 116 
L 2856 116 
L 2856 0 
L 1059 0 
L 1059 116 
L 1209 116 
Q 1478 116 1591 278 
Q 1659 378 1659 734 
L 1659 3972 
L 1150 3972 
Q 853 3972 728 3928 
Q 566 3869 450 3700 
Q 334 3531 313 3244 
L 194 3244 
L 244 4238 
L 3703 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-53"/>
       <use xlink:href="#TimesNewRomanPSMT-54" transform="translate(55.615234 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="265.09" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- HTC -->
      <g transform="translate(26.727188 268.561875) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-48" d="M 1316 2272 
L 3284 2272 
L 3284 3484 
Q 3284 3809 3244 3913 
Q 3213 3991 3113 4047 
Q 2978 4122 2828 4122 
L 2678 4122 
L 2678 4238 
L 4491 4238 
L 4491 4122 
L 4341 4122 
Q 4191 4122 4056 4050 
Q 3956 4000 3920 3898 
Q 3884 3797 3884 3484 
L 3884 750 
Q 3884 428 3925 325 
Q 3956 247 4053 191 
Q 4191 116 4341 116 
L 4491 116 
L 4491 0 
L 2678 0 
L 2678 116 
L 2828 116 
Q 3088 116 3206 269 
Q 3284 369 3284 750 
L 3284 2041 
L 1316 2041 
L 1316 750 
Q 1316 428 1356 325 
Q 1388 247 1488 191 
Q 1622 116 1772 116 
L 1925 116 
L 1925 0 
L 109 0 
L 109 116 
L 259 116 
Q 522 116 641 269 
Q 716 369 716 750 
L 716 3484 
Q 716 3809 675 3913 
Q 644 3991 547 4047 
Q 409 4122 259 4122 
L 109 4122 
L 109 4238 
L 1925 4238 
L 1925 4122 
L 1772 4122 
Q 1622 4122 1488 4050 
Q 1391 4000 1353 3898 
Q 1316 3797 1316 3484 
L 1316 2272 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-48"/>
       <use xlink:href="#TimesNewRomanPSMT-54" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-43" transform="translate(133.300781 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="340.94" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- MR -->
      <g transform="translate(31.164688 344.411875) scale(0.1 -0.1)">
       <use xlink:href="#TimesNewRomanPSMT-4d"/>
       <use xlink:href="#TimesNewRomanPSMT-52" transform="translate(88.916016 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="416.79" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- YV -->
      <g transform="translate(32.283438 420.261875) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-59" d="M 3050 4238 
L 4528 4238 
L 4528 4122 
L 4447 4122 
Q 4366 4122 4209 4050 
Q 4053 3978 3925 3843 
Q 3797 3709 3609 3406 
L 2588 1797 
L 2588 734 
Q 2588 344 2675 247 
Q 2794 116 3050 116 
L 3188 116 
L 3188 0 
L 1388 0 
L 1388 116 
L 1538 116 
Q 1806 116 1919 278 
Q 1988 378 1988 734 
L 1988 1738 
L 825 3513 
Q 619 3825 545 3903 
Q 472 3981 241 4091 
Q 178 4122 59 4122 
L 59 4238 
L 1872 4238 
L 1872 4122 
L 1778 4122 
Q 1631 4122 1507 4053 
Q 1384 3984 1384 3847 
Q 1384 3734 1575 3441 
L 2459 2075 
L 3291 3381 
Q 3478 3675 3478 3819 
Q 3478 3906 3433 3975 
Q 3388 4044 3303 4083 
Q 3219 4122 3050 4122 
L 3050 4238 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-56" d="M 4544 4238 
L 4544 4122 
Q 4319 4081 4203 3978 
Q 4038 3825 3909 3509 
L 2431 -97 
L 2316 -97 
L 728 3556 
Q 606 3838 556 3900 
Q 478 3997 364 4051 
Q 250 4106 56 4122 
L 56 4238 
L 1788 4238 
L 1788 4122 
Q 1494 4094 1406 4022 
Q 1319 3950 1319 3838 
Q 1319 3681 1463 3350 
L 2541 866 
L 3541 3319 
Q 3688 3681 3688 3822 
Q 3688 3913 3597 3995 
Q 3506 4078 3291 4113 
Q 3275 4116 3238 4122 
L 3238 4238 
L 4544 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-59"/>
       <use xlink:href="#TimesNewRomanPSMT-56" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="492.64" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- YL -->
      <g transform="translate(33.3975 496.111875) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-4c" d="M 3669 1172 
L 3772 1150 
L 3409 0 
L 128 0 
L 128 116 
L 288 116 
Q 556 116 672 291 
Q 738 391 738 753 
L 738 3488 
Q 738 3884 650 3984 
Q 528 4122 288 4122 
L 128 4122 
L 128 4238 
L 2047 4238 
L 2047 4122 
Q 1709 4125 1573 4059 
Q 1438 3994 1388 3894 
Q 1338 3794 1338 3416 
L 1338 753 
Q 1338 494 1388 397 
Q 1425 331 1503 300 
Q 1581 269 1991 269 
L 2300 269 
Q 2788 269 2984 341 
Q 3181 413 3343 595 
Q 3506 778 3669 1172 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-59"/>
       <use xlink:href="#TimesNewRomanPSMT-4c" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="568.49" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- QI -->
      <g transform="translate(36.175625 571.961875) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-51" d="M 2819 -47 
Q 3138 -597 3508 -856 
Q 3878 -1116 4350 -1153 
L 4350 -1253 
Q 3919 -1238 3428 -1080 
Q 2938 -922 2498 -642 
Q 2059 -363 1741 -47 
Q 1291 134 1028 338 
Q 647 641 436 1083 
Q 225 1525 225 2128 
Q 225 3078 840 3706 
Q 1456 4334 2331 4334 
Q 3163 4334 3770 3704 
Q 4378 3075 4378 2116 
Q 4378 1338 3945 750 
Q 3513 163 2819 -47 
z
M 2294 4094 
Q 1725 4094 1378 3688 
Q 941 3178 941 2128 
Q 941 1100 1384 544 
Q 1728 116 2294 116 
Q 2881 116 3241 544 
Q 3663 1050 3663 2038 
Q 3663 2797 3431 3316 
Q 3253 3716 2958 3905 
Q 2663 4094 2294 4094 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-51"/>
       <use xlink:href="#TimesNewRomanPSMT-49" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_13">
      <g>
       <use xlink:href="#m2c704e666d" x="53.727188" y="644.34" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- SL -->
      <g transform="translate(35.058438 647.811875) scale(0.1 -0.1)">
       <use xlink:href="#TimesNewRomanPSMT-53"/>
       <use xlink:href="#TimesNewRomanPSMT-4c" transform="translate(55.615234 0)"/>
      </g>
     </g>
    </g>
    <g id="text_15">
     <!-- Models -->
     <g transform="translate(15.14625 359.605625) rotate(-90) scale(0.12 -0.12)">
      <defs>
       <path id="TimesNewRomanPS-BoldMT-6f" d="M 1594 3009 
Q 1975 3009 2303 2812 
Q 2631 2616 2801 2253 
Q 2972 1891 2972 1459 
Q 2972 838 2656 419 
Q 2275 -88 1603 -88 
Q 944 -88 587 375 
Q 231 838 231 1447 
Q 231 2075 595 2542 
Q 959 3009 1594 3009 
z
M 1606 2788 
Q 1447 2788 1333 2667 
Q 1219 2547 1183 2192 
Q 1147 1838 1147 1206 
Q 1147 872 1191 581 
Q 1225 359 1337 243 
Q 1450 128 1594 128 
Q 1734 128 1828 206 
Q 1950 313 1991 503 
Q 2053 800 2053 1703 
Q 2053 2234 1993 2432 
Q 1934 2631 1819 2722 
Q 1738 2788 1606 2788 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-64" d="M 3056 4238 
L 3056 875 
Q 3056 534 3075 472 
Q 3100 369 3170 319 
Q 3241 269 3416 256 
L 3416 153 
L 2181 -88 
L 2181 375 
Q 1959 106 1786 9 
Q 1613 -88 1394 -88 
Q 834 -88 509 413 
Q 247 819 247 1409 
Q 247 1881 409 2254 
Q 572 2628 855 2818 
Q 1138 3009 1463 3009 
Q 1672 3009 1834 2928 
Q 1997 2847 2181 2644 
L 2181 3525 
Q 2181 3859 2153 3928 
Q 2116 4019 2041 4062 
Q 1966 4106 1759 4106 
L 1759 4238 
L 3056 4238 
z
M 2181 2256 
Q 1950 2700 1616 2700 
Q 1500 2700 1425 2638 
Q 1309 2541 1236 2297 
Q 1163 2053 1163 1550 
Q 1163 997 1244 731 
Q 1325 466 1466 347 
Q 1538 288 1663 288 
Q 1938 288 2181 719 
L 2181 2256 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPS-BoldMT-6c" d="M 1359 4238 
L 1359 606 
Q 1359 297 1431 211 
Q 1503 125 1713 113 
L 1713 0 
L 134 0 
L 134 113 
Q 328 119 422 225 
Q 484 297 484 606 
L 484 3631 
Q 484 3938 412 4023 
Q 341 4109 134 4122 
L 134 4238 
L 1359 4238 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPS-BoldMT-4d"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-6f" transform="translate(94.384766 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-64" transform="translate(144.384766 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(200 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-6c" transform="translate(244.384766 0)"/>
      <use xlink:href="#TimesNewRomanPS-BoldMT-73" transform="translate(272.167969 0)"/>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 53.727188 674.68 
L 53.727188 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 337.735187 674.68 
L 337.735187 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 53.727188 674.68 
L 337.735187 674.68 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 53.727188 7.2 
L 337.735187 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 355.485687 674.68 
L 388.859687 674.68 
L 388.859687 7.2 
L 355.485687 7.2 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image3456bf2974" transform="scale(1 -1) translate(0 -667.68)" x="355.44" y="-6.96" width="33.36" height="667.68"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_14">
      <defs>
       <path id="m0fb5bd7dcb" d="M 0 0 
L 3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0fb5bd7dcb" x="388.859687" y="612.10375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 10 -->
      <g transform="translate(395.859687 615.575625) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-31" d="M 750 3822 
L 1781 4325 
L 1884 4325 
L 1884 747 
Q 1884 391 1914 303 
Q 1944 216 2037 169 
Q 2131 122 2419 116 
L 2419 0 
L 825 0 
L 825 116 
Q 1125 122 1212 167 
Q 1300 213 1334 289 
Q 1369 366 1369 747 
L 1369 3034 
Q 1369 3497 1338 3628 
Q 1316 3728 1258 3775 
Q 1200 3822 1119 3822 
Q 1003 3822 797 3725 
L 750 3822 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-31"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_15">
      <g>
       <use xlink:href="#m0fb5bd7dcb" x="388.859687" y="521.413533" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 20 -->
      <g transform="translate(395.859687 524.885408) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-32" d="M 2934 816 
L 2638 0 
L 138 0 
L 138 116 
Q 1241 1122 1691 1759 
Q 2141 2397 2141 2925 
Q 2141 3328 1894 3587 
Q 1647 3847 1303 3847 
Q 991 3847 742 3664 
Q 494 3481 375 3128 
L 259 3128 
Q 338 3706 661 4015 
Q 984 4325 1469 4325 
Q 1984 4325 2329 3994 
Q 2675 3663 2675 3213 
Q 2675 2891 2525 2569 
Q 2294 2063 1775 1497 
Q 997 647 803 472 
L 1909 472 
Q 2247 472 2383 497 
Q 2519 522 2628 598 
Q 2738 675 2819 816 
L 2934 816 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-32"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_16">
      <g>
       <use xlink:href="#m0fb5bd7dcb" x="388.859687" y="430.723315" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 30 -->
      <g transform="translate(395.859687 434.19519) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-33" d="M 325 3431 
Q 506 3859 782 4092 
Q 1059 4325 1472 4325 
Q 1981 4325 2253 3994 
Q 2459 3747 2459 3466 
Q 2459 3003 1878 2509 
Q 2269 2356 2469 2072 
Q 2669 1788 2669 1403 
Q 2669 853 2319 450 
Q 1863 -75 997 -75 
Q 569 -75 414 31 
Q 259 138 259 259 
Q 259 350 332 419 
Q 406 488 509 488 
Q 588 488 669 463 
Q 722 447 909 348 
Q 1097 250 1169 231 
Q 1284 197 1416 197 
Q 1734 197 1970 444 
Q 2206 691 2206 1028 
Q 2206 1275 2097 1509 
Q 2016 1684 1919 1775 
Q 1784 1900 1550 2001 
Q 1316 2103 1072 2103 
L 972 2103 
L 972 2197 
Q 1219 2228 1467 2375 
Q 1716 2522 1828 2728 
Q 1941 2934 1941 3181 
Q 1941 3503 1739 3701 
Q 1538 3900 1238 3900 
Q 753 3900 428 3381 
L 325 3431 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-33"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_17">
      <g>
       <use xlink:href="#m0fb5bd7dcb" x="388.859687" y="340.033098" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 40 -->
      <g transform="translate(395.859687 343.504973) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-34" d="M 2978 1563 
L 2978 1119 
L 2409 1119 
L 2409 0 
L 1894 0 
L 1894 1119 
L 100 1119 
L 100 1519 
L 2066 4325 
L 2409 4325 
L 2409 1563 
L 2978 1563 
z
M 1894 1563 
L 1894 3666 
L 406 1563 
L 1894 1563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-34"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_18">
      <g>
       <use xlink:href="#m0fb5bd7dcb" x="388.859687" y="249.34288" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 50 -->
      <g transform="translate(395.859687 252.814755) scale(0.1 -0.1)">
       <use xlink:href="#TimesNewRomanPSMT-35"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_19">
      <g>
       <use xlink:href="#m0fb5bd7dcb" x="388.859687" y="158.652663" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 60 -->
      <g transform="translate(395.859687 162.124538) scale(0.1 -0.1)">
       <defs>
        <path id="TimesNewRomanPSMT-36" d="M 2869 4325 
L 2869 4209 
Q 2456 4169 2195 4045 
Q 1934 3922 1679 3669 
Q 1425 3416 1258 3105 
Q 1091 2794 978 2366 
Q 1428 2675 1881 2675 
Q 2316 2675 2634 2325 
Q 2953 1975 2953 1425 
Q 2953 894 2631 456 
Q 2244 -75 1606 -75 
Q 1172 -75 869 213 
Q 275 772 275 1663 
Q 275 2231 503 2743 
Q 731 3256 1154 3653 
Q 1578 4050 1965 4187 
Q 2353 4325 2688 4325 
L 2869 4325 
z
M 925 2138 
Q 869 1716 869 1456 
Q 869 1156 980 804 
Q 1091 453 1309 247 
Q 1469 100 1697 100 
Q 1969 100 2183 356 
Q 2397 613 2397 1088 
Q 2397 1622 2184 2012 
Q 1972 2403 1581 2403 
Q 1463 2403 1327 2353 
Q 1191 2303 925 2138 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-36"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_20">
      <g>
       <use xlink:href="#m0fb5bd7dcb" x="388.859687" y="67.962446" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- 70 -->
      <g transform="translate(395.859687 71.434321) scale(0.1 -0.1)">
       <use xlink:href="#TimesNewRomanPSMT-37"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="text_23">
     <!-- Score -->
     <g transform="translate(417.497812 353.462813) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="TimesNewRomanPSMT-63" d="M 2631 1088 
Q 2516 522 2178 217 
Q 1841 -88 1431 -88 
Q 944 -88 581 321 
Q 219 731 219 1428 
Q 219 2103 620 2525 
Q 1022 2947 1584 2947 
Q 2006 2947 2278 2723 
Q 2550 2500 2550 2259 
Q 2550 2141 2473 2067 
Q 2397 1994 2259 1994 
Q 2075 1994 1981 2113 
Q 1928 2178 1911 2362 
Q 1894 2547 1784 2644 
Q 1675 2738 1481 2738 
Q 1169 2738 978 2506 
Q 725 2200 725 1697 
Q 725 1184 976 792 
Q 1228 400 1656 400 
Q 1963 400 2206 609 
Q 2378 753 2541 1131 
L 2631 1088 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-6f" d="M 1600 2947 
Q 2250 2947 2644 2453 
Q 2978 2031 2978 1484 
Q 2978 1100 2793 706 
Q 2609 313 2286 112 
Q 1963 -88 1566 -88 
Q 919 -88 538 428 
Q 216 863 216 1403 
Q 216 1797 411 2186 
Q 606 2575 925 2761 
Q 1244 2947 1600 2947 
z
M 1503 2744 
Q 1338 2744 1170 2645 
Q 1003 2547 900 2300 
Q 797 2053 797 1666 
Q 797 1041 1045 587 
Q 1294 134 1700 134 
Q 2003 134 2200 384 
Q 2397 634 2397 1244 
Q 2397 2006 2069 2444 
Q 1847 2744 1503 2744 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-72" d="M 1038 2947 
L 1038 2303 
Q 1397 2947 1775 2947 
Q 1947 2947 2059 2842 
Q 2172 2738 2172 2600 
Q 2172 2478 2090 2393 
Q 2009 2309 1897 2309 
Q 1788 2309 1652 2417 
Q 1516 2525 1450 2525 
Q 1394 2525 1328 2463 
Q 1188 2334 1038 2041 
L 1038 669 
Q 1038 431 1097 309 
Q 1138 225 1241 169 
Q 1344 113 1538 113 
L 1538 0 
L 72 0 
L 72 113 
Q 291 113 397 181 
Q 475 231 506 341 
Q 522 394 522 644 
L 522 1753 
Q 522 2253 501 2348 
Q 481 2444 426 2487 
Q 372 2531 291 2531 
Q 194 2531 72 2484 
L 41 2597 
L 906 2947 
L 1038 2947 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-65" d="M 681 1784 
Q 678 1147 991 784 
Q 1303 422 1725 422 
Q 2006 422 2214 576 
Q 2422 731 2563 1106 
L 2659 1044 
Q 2594 616 2278 264 
Q 1963 -88 1488 -88 
Q 972 -88 605 314 
Q 238 716 238 1394 
Q 238 2128 614 2539 
Q 991 2950 1559 2950 
Q 2041 2950 2350 2633 
Q 2659 2316 2659 1784 
L 681 1784 
z
M 681 1966 
L 2006 1966 
Q 1991 2241 1941 2353 
Q 1863 2528 1708 2628 
Q 1553 2728 1384 2728 
Q 1125 2728 920 2526 
Q 716 2325 681 1966 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-53"/>
      <use xlink:href="#TimesNewRomanPSMT-63" transform="translate(55.615234 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(100 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-72" transform="translate(150 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-65" transform="translate(183.300781 0)"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_8">
    <path d="M 355.485687 674.68 
L 372.172687 674.68 
L 388.859687 674.68 
L 388.859687 7.2 
L 372.172687 7.2 
L 355.485687 7.2 
L 355.485687 674.68 
z
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pa6d9fb2606">
   <rect x="53.727188" y="7.2" width="284.008" height="667.48"/>
  </clipPath>
 </defs>
</svg>
