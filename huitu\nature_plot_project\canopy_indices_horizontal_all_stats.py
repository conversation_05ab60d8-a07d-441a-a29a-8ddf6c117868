import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
import matplotlib.gridspec as gridspec

# 设置更简单的字体配置，确保字体兼容性
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 10
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 时间日期（x轴）
dates = ['1024', '1031', '1108', '1115', '1128', '1218']
x_numeric = np.arange(len(dates))

# 树冠1的数据
canopy1 = {
    'ExG': [0.4685979, 0.3948133, 0.3849822, 0.4232759, 0.4514346, 0.524074],
    'CVI': [0.3778748, 0.3609359, 0.3445884, 0.3775304, 0.417605, 0.4675018],
    'MGRVI': [0.80422, 0.7466185, 0.6947784, 0.8350153, 0.6765864, 0.8539257],
    'EXB': [0.7354636, 0.6994844, 0.8099242, 0.6946393, 0.6963692, 0.7525901]
}

# 树冠2的数据
canopy2 = {
    'ExG': [0.4639508, 0.3962725, 0.3961348, 0.3734632, 0.4585701, 0.480593],
    'CVI': [0.4172846, 0.1896627, 0.3470715, 0.3212644, 0.4146229, 0.4156693],
    'MGRVI': [0.7592297, 0.8725845, 0.8228972, 0.8509057, 0.7099463, 0.6306188],
    'EXB': [0.7590165, 0.6946992, 0.7686097, 0.72676501, 0.7563242, 0.7625658]
}

# 树冠3的数据
canopy3 = {
    'ExG': [0.4228484, 0.4100583, 0.4249226, 0.4216866, 0.4630271, 0.4681649],
    'CVI': [0.346686, 0.1988318, 0.3346633, 0.3618345, 0.4292103, 0.4019993],
    'MGRVI': [0.8356118, 0.7940347, 0.79819, 0.7767971, 0.6697405, 0.7593806],
    'EXB': [0.78208, 0.7343576, 0.7024199, 0.7156579, 0.7510814, 0.6928242]
}

# 植被指数的颜色和标记
index_colors = {
    'ExG': '#2E5E4B',     # 深绿色
    'CVI': '#4A7B8C',     # 蓝色
    'MGRVI': '#8C4A59',   # 红色
    'EXB': '#6B929E'      # 灰蓝色
}

index_markers = {
    'ExG': 'o',      # 圆形
    'CVI': 's',      # 方形
    'MGRVI': '^',    # 三角形
    'EXB': 'd'       # 菱形
}

# 定义统计信息的位置，避免重叠
text_positions = {
    'ExG': (0.02, 0.95),  # 左上
    'CVI': (0.02, 0.85),  # 左上偏下
    'MGRVI': (0.60, 0.95), # 右上
    'EXB': (0.60, 0.85)   # 右上偏下
}

def create_horizontal_canopy_plots():
    """创建三个树冠的横向排列图表，每个图表包含该树冠的所有植被指数"""
    
    # 创建大图和子图布局
    fig = plt.figure(figsize=(16, 5), facecolor='white')
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1], wspace=0.2)
    
    # 所有树冠数据和标签
    all_canopies = [canopy1, canopy2, canopy3]
    canopy_labels = ['Canopy 1', 'Canopy 2', 'Canopy 3']
    
    # 确定每个树冠的y轴范围
    y_ranges = []
    for canopy_data in all_canopies:
        all_values = []
        for values in canopy_data.values():
            all_values.extend(values)
        y_min, y_max = min(all_values), max(all_values)
        y_range = y_max - y_min
        y_ranges.append((y_min - 0.1*y_range, y_max + 0.1*y_range))
    
    # 为每个树冠创建子图
    for i, (canopy_data, label, y_range) in enumerate(zip(all_canopies, canopy_labels, y_ranges)):
        ax = plt.subplot(gs[i])
        
        # 为每个植被指数绘制散点和趋势线
        for index_name, values in canopy_data.items():
            color = index_colors[index_name]
            marker = index_markers[index_name]
            
            # 绘制散点
            ax.scatter(x_numeric, values, color=color, s=40, marker=marker, 
                       label=index_name, alpha=0.9)
            
            # 计算并绘制趋势线
            slope, intercept, r_value, p_value, std_err = stats.linregress(x_numeric, values)
            line = slope * x_numeric + intercept
            ax.plot(x_numeric, line, color=color, linestyle='-', linewidth=1.5)
            
            # 计算并绘制置信区间
            n = len(x_numeric)
            mean_x = np.mean(x_numeric)
            t_val = stats.t.ppf(0.975, n-2)
            s_err = np.sqrt(np.sum((values - (intercept + slope * x_numeric))**2) / (n - 2))
            confs = t_val * s_err * np.sqrt(1/n + (x_numeric - mean_x)**2 / np.sum((x_numeric - mean_x)**2))
            
            upper = line + confs
            lower = line - confs
            ax.fill_between(x_numeric, lower, upper, color=color, alpha=0.1)
            
            # 为每个植被指数添加统计信息
            x_pos, y_pos = text_positions[index_name]
            stat_text = f"{index_name}: Slope={slope:.2f}, R²={r_value**2:.2f}, p={p_value:.3f}"
            
            # 添加较小的背景框以增强可读性
            ax.text(x_pos, y_pos, stat_text, transform=ax.transAxes, 
                    fontsize=8, color=color, verticalalignment='top',
                    bbox=dict(facecolor='white', alpha=0.7, pad=2, edgecolor='none'))
        
        # 设置轴标签和标题
        ax.set_title(label, fontsize=12)
        ax.set_xlabel('Date', fontsize=10)
        
        # 只为第一个子图添加y轴标签
        if i == 0:
            ax.set_ylabel('Vegetation Index', fontsize=10)
        
        # 设置x轴刻度
        ax.set_xticks(x_numeric)
        ax.set_xticklabels(dates)
        
        # 设置y轴范围
        ax.set_ylim(y_range)
        
        # 添加图例
        ax.legend(loc='lower right', frameon=True, framealpha=0.9)
        
        # 关闭网格线
        ax.grid(False)
    
    # 优化布局
    plt.tight_layout(pad=1.5)
    
    # 保存图表
    plt.savefig('horizontal_canopy_indices_all_stats.png', dpi=600, bbox_inches='tight', pad_inches=0.1)
    plt.savefig('horizontal_canopy_indices_all_stats.pdf', bbox_inches='tight', pad_inches=0.1)
    plt.savefig('horizontal_canopy_indices_all_stats.svg', bbox_inches='tight', pad_inches=0.1)
    
    print("Created horizontal canopy indices plot with all statistics")
    return fig

if __name__ == "__main__":
    fig = create_horizontal_canopy_plots()
    plt.show() 