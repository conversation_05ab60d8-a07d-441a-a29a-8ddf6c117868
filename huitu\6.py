import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
# import matplotlib.gridspec as gridspec # Remove gridspec
from mpl_toolkits.axes_grid1 import make_axes_locatable # Import AxisDivider helper

# --- 设置图表参数 ---
try:
    plt.rcParams['font.family'] = 'Times New Roman'
except RuntimeError:
    print("Times New Roman font not found, using default.")

plt.rcParams.update({
    'font.size': 24,  # 进一步增大基础字体大小
    'axes.labelsize': 28,
    'xtick.labelsize': 24,
    'ytick.labelsize': 24,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# --- 数据准备 ---
# Abbreviated model names for better fit on X-axis (matching example)
models_short = [
    'CSAF', 'DT', 'ST', 'HTC', 'MR', 'YV', 'YL', 'QI', 'SL' # Corrected Solov2 abbreviation
]
models_full = [
    'CSAF', 'Cascade Mask R-CNN', 'Swin Transformer', 'Hybrid Task Cascade',
    'Mask R-CNN', 'YOLOv5', 'Yolact', 'QueryInst', 'Solov2'
]
metrics = ['AP', 'AP50', 'AP75', 'API']
N_models = len(models_short)
N_metrics = len(metrics)

data = np.array([
    [35.4, 76.7, 27.5, 35.4], # CSAF
    [29.3, 67.8, 24.6, 29.5], # Cascade Mask R-CNN
    [27.1, 59.4, 24.3, 27.3], # Swin Transformer
    [22.0, 53.5, 16.3, 22.3], # Hybrid Task Cascade
    [30.2, 63.9, 24.1, 30.1], # Mask R-CNN
    [18.3, 48.9, 11.0, 18.4], # YOLOv5
    [22.1, 53.0, 15.3, 22.2], # Yolact
    [13.8, 43.8, 6.2, 13.9],  # QueryInst
    [11.4, 39.6, 3.1, 11.4]   # Solov2
])

# Create DataFrame with short model names as index
df = pd.DataFrame(data, index=models_short, columns=metrics)

# --- 计算总和 ---
model_sums = df.sum(axis=1) # Sum across columns (metrics) for each model -> Top bar
metric_sums = df.sum(axis=0) # Sum across rows (models) for each metric -> Left lollipop

# --- 创建复杂布局 (使用 AxisDivider) ---
fig = plt.figure(figsize=(20, 12))  # 大幅增加图表尺寸

# Create the main axes for the heatmap
ax_heatmap = fig.add_subplot(111)

# Create a divider for the heatmap axes
divider = make_axes_locatable(ax_heatmap)

# Append axes for the other plots relative to the heatmap axes
ax_bar = divider.append_axes("top", size="20%", pad=0.3, sharex=ax_heatmap)
ax_lolli = divider.append_axes("left", size="30%", pad=0.8, sharey=ax_heatmap)  # 增加左侧空间
ax_cbar = divider.append_axes("right", size="5%", pad=0.3)

# --- Set All Axes Backgrounds Transparent ---
# fig.patch.set_alpha(0.0) # Set on figure only is usually sufficient now
ax_heatmap.patch.set_alpha(0.0)
ax_bar.patch.set_alpha(0.0)
ax_lolli.patch.set_alpha(0.0)
ax_cbar.patch.set_alpha(0.0)

# --- 绘制热力图 ---
df_transposed = df.T
cmap = "viridis"  # 更改为viridis配色，更美观清晰
heatmap_plot = sns.heatmap( # Assign to variable to use for colorbar
    df_transposed,
    ax=ax_heatmap,
    annot=False,
    cmap=cmap,
    linewidths=0.5,
    linecolor='white',
    cbar=False, # Disable automatic colorbar
    # cbar_ax=ax_cbar, # We will draw it manually
    # cbar_kws={'label': 'Score'}
)
# Configure heatmap ticks and labels
ax_heatmap.tick_params(axis='x', bottom=True, top=False, labelbottom=True, rotation=90)
ax_heatmap.set_xticks(np.arange(N_models) + 0.5) # Center ticks
ax_heatmap.set_xticklabels(df.index, ha='center', fontsize=26) # 更大的模型名称字体
ax_heatmap.tick_params(axis='y', left=True, right=False, labelleft=True, labelright=False, rotation=0)
ax_heatmap.set_yticks(np.arange(N_metrics) + 0.5) # Center ticks
ax_heatmap.set_yticklabels(df_transposed.index, fontsize=26) # 更大的指标名称字体
ax_heatmap.set_xlabel('')
ax_heatmap.set_ylabel('')

# --- 绘制顶部条形图 ---
# 获取热力图的精确x轴限制，以确保对齐
heatmap_xlim = ax_heatmap.get_xlim()
bar_positions = np.arange(N_models) + 0.5  # 将条形图位置调整到热力图单元格中心
bar_color = sns.color_palette('viridis', 3)[1]  # 匹配热力图的配色方案
ax_bar.bar(bar_positions, model_sums, width=1.0, color=bar_color, alpha=0.8, edgecolor='grey')
ax_bar.set_xlim(heatmap_xlim)  # 精确匹配热力图的x轴范围
ax_bar.tick_params(axis='x', bottom=False, labelbottom=False) # Hide shared x-axis labels
ax_bar.spines[:].set_visible(False) # Hide all spines
ax_bar.tick_params(axis='y', left=False, labelleft=False, right=True, labelright=True)
ax_bar.yaxis.set_label_position("right")
ax_bar.yaxis.set_label_coords(1.15, 0.5)  # x=1.15表示向右移动，y=0.5表示中间位置
ax_bar.set_ylabel("Total Score (Model)", fontsize=28)
ax_bar.grid(axis='y', linestyle='--', alpha=0.6)
ax_bar.tick_params(axis='y', labelsize=24)  # 确保y轴刻度标签字体足够大

# --- 绘制左侧棒棒糖图 ---
lollipop_color = sns.color_palette('viridis', 3)[1]  # 匹配热力图的配色方案
y_pos = np.arange(N_metrics)
# 反转棒棒糖图方向，从右往左
ax_lolli.hlines(y=y_pos, xmin=metric_sums, xmax=0, color=lollipop_color, alpha=0.8, linewidth=2)  # 交换xmin和xmax
ax_lolli.scatter(metric_sums, y_pos, color=lollipop_color, s=80, alpha=1, zorder=3)  # 增大点的尺寸
ax_lolli.tick_params(axis='y', left=False, labelleft=False) # Hide shared y-axis labels
ax_lolli.spines[:].set_visible(False) # Hide all spines
ax_lolli.xaxis.tick_top()
ax_lolli.xaxis.set_label_position("top")
ax_lolli.xaxis.set_label_coords(0.5, 1.15)  # x=0.5表示中间位置，y=1.15表示向上移动
ax_lolli.set_xlabel("Total Score (Metric)", fontsize=28)
plt.setp(ax_lolli.get_xticklabels(), rotation=0, ha='center', fontsize=24)
ax_lolli.grid(axis='x', linestyle='--', alpha=0.6)
ax_lolli.set_ylim(ax_heatmap.get_ylim()) # Ensure ylim matches heatmap
# 反转x轴，使刻度从大到小
ax_lolli.invert_xaxis()  # 反转x轴方向
ax_lolli.tick_params(axis='x', labelsize=24)  # 确保x轴刻度标签字体足够大

# --- Draw Colorbar ---
cbar = fig.colorbar(heatmap_plot.collections[0], cax=ax_cbar)
cbar.set_label('Score', fontsize=28)
cbar.ax.tick_params(labelsize=24)

# --- 设置背景 (透明) ---
fig.patch.set_alpha(0.0)

# --- 布局和保存 ---
plt.tight_layout(pad=3.0)  # 增加整体边距
plt.subplots_adjust(left=0.28, right=0.92, top=0.92, bottom=0.08)  # 调整边距，确保图表完全显示

plt.savefig('Model_Dashboard_Heatmap_Divider.png', format='png', transparent=True, bbox_inches='tight', pad_inches=1.0)
plt.savefig('Model_Dashboard_Heatmap_Divider.svg', format='svg', transparent=True, bbox_inches='tight', pad_inches=1.0)
plt.savefig('Model_Dashboard_Heatmap_Divider.pdf', format='pdf', transparent=True, bbox_inches='tight', pad_inches=1.0)

plt.show()

# Note: Top bar chart and left lollipop chart are not included due to lack of corresponding data.
# We can discuss adding them if you provide the data or want to use aggregated values. 