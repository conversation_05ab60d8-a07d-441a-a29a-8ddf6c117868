import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon, Wedge
from matplotlib.collections import PatchCollection
import matplotlib.patheffects as path_effects
import matplotlib.cm as cm
from matplotlib.colors import LinearSegmentedColormap

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['savefig.dpi'] = 600

# 数据
months = list(range(1, 13))
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'MRCNN': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [551, 548, 520, 547, 547, 547, 547, 547, 547, 547, 547, 547]
}

# 计算平均值和共享数据
avg_csaf = np.mean(data['CSAF'])
avg_mrcnn = np.mean(data['MRCNN'])
avg_real = np.mean(data['real'])
total_csaf = sum(data['CSAF'])
total_mrcnn = sum(data['MRCNN'])
total_real = sum(data['real'])

# 创建两个图表
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7), facecolor='white')

# 自定义颜色映射
colors_csaf = LinearSegmentedColormap.from_list('csaf_colors', ['#5B9BD5', '#A5C8E1'])
colors_mrcnn = LinearSegmentedColormap.from_list('mrcnn_colors', ['#ED7D31', '#F4B183'])
colors_real = LinearSegmentedColormap.from_list('real_colors', ['#70AD47', '#A9D18E'])

# 函数创建花瓣图
def create_flower_diagram(ax, data1, data2, name1, name2, colors1, colors2):
    # 中心多边形
    center_radius = 2
    center_x, center_y = 0, 0
    
    # 创建中心多边形
    hex = Polygon([(center_radius*np.cos(2*np.pi*i/6), center_radius*np.sin(2*np.pi*i/6)) for i in range(6)], 
                 closed=True, facecolor='#DCE6F1', edgecolor='navy', linewidth=1.5, alpha=0.9, zorder=10)
    ax.add_patch(hex)
    
    # 计算共享数据和百分比
    total1 = sum(data1)
    total2 = sum(data2)
    avg1 = np.mean(data1)
    avg2 = np.mean(data2)
    overlap_pct = min(avg1, avg2)/max(avg1, avg2) * 100
    
    # 添加中心文本
    ax.text(0, 0.8, f"{name1}-{name2}\nCore", ha='center', va='center', fontsize=10, 
            fontweight='bold', color='navy', zorder=11)
    ax.text(0, 0, f"{int(avg1)}\n{int(total1)}", ha='center', va='center', fontsize=9, 
            color='navy', zorder=11)
    ax.text(0, -0.8, f"[{overlap_pct:.1f}%]", ha='center', va='center', fontsize=9, 
            fontweight='bold', color='navy', zorder=11)
    
    # 花瓣位置计算
    n_months = len(data1)
    radius = 6  # 花瓣中心到整体中心的距离
    petal_radius = 3  # 花瓣大小
    
    # 创建花瓣
    for i in range(n_months):
        angle = 2 * np.pi * i / n_months
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # 计算花瓣大小（根据数值调整）
        value1 = data1[i]
        value2 = data2[i]
        size1 = petal_radius * (value1 / max(data1)) * 0.8 + petal_radius * 0.2
        size2 = petal_radius * (value2 / max(data2)) * 0.8 + petal_radius * 0.2
        
        # 创建两个圆，稍微错开位置
        offset = 0.5
        circle1 = Circle((x-offset, y), size1, facecolor=colors1(i/n_months), 
                        alpha=0.7, edgecolor='none', zorder=i+1)
        circle2 = Circle((x+offset, y), size2, facecolor=colors2(i/n_months), 
                        alpha=0.7, edgecolor='none', zorder=i+1)
        
        ax.add_patch(circle1)
        ax.add_patch(circle2)
        
        # 添加数值标签
        ax.text(x-offset, y, f"{value1}", ha='center', va='center', fontsize=9, 
                fontweight='bold', color='white',
                path_effects=[path_effects.withStroke(linewidth=2, foreground=colors1(i/n_months))])
        
        ax.text(x+offset, y, f"{value2}", ha='center', va='center', fontsize=9, 
                fontweight='bold', color='white',
                path_effects=[path_effects.withStroke(linewidth=2, foreground=colors2(i/n_months))])
        
        # 添加连接线
        line_x = [x-offset-size1*0.8, x+offset+size2*0.8]
        line_y = [y, y]
        ax.plot(line_x, line_y, color='lightgray', linestyle='-', alpha=0.5, zorder=0)
    
    # 设置图表范围和关闭坐标轴
    ax.set_xlim(-radius-petal_radius-2, radius+petal_radius+2)
    ax.set_ylim(-radius-petal_radius-2, radius+petal_radius+2)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 添加月份标签
    for i in range(n_months):
        angle = 2 * np.pi * i / n_months
        x = (radius + petal_radius + 1) * np.cos(angle)
        y = (radius + petal_radius + 1) * np.sin(angle)
        ax.text(x, y, f"Month {i+1}", ha='center', va='center', fontsize=8, 
                fontweight='bold', color='black')

# 创建两个花瓣图
create_flower_diagram(ax1, data['CSAF'], data['real'], 'CSAF', 'Real', colors_csaf, colors_real)
create_flower_diagram(ax2, data['MRCNN'], data['real'], 'MRCNN', 'Real', colors_mrcnn, colors_real)

# 添加标题
fig.suptitle('Model Performance Comparison - Flower Petal Visualization', fontsize=14, fontweight='bold', y=0.98)

# 添加图例
ax1.text(-9, -9, 'A', fontsize=16, fontweight='bold')
ax2.text(-9, -9, 'B', fontsize=16, fontweight='bold')

# 保存图表
plt.tight_layout()
plt.savefig('flower_petal_comparison.png', dpi=600, bbox_inches='tight')
plt.savefig('flower_petal_comparison.pdf', bbox_inches='tight')

print("花瓣式比较图创建完成")
plt.show() 