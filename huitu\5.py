import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import matplotlib.colors as mcolors

# --- 设置图表参数 ---
try:
    plt.rcParams['font.family'] = 'Times New Roman'
except RuntimeError:
    print("Times New Roman font not found, using default.")

plt.rcParams.update({
    'font.size': 10,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# --- 数据准备 ---
models = [
    'CSAF', 'DT', 'ST', 'HTC',
    'MR', 'YV', 'YL', 'QI', 'SL'
]
metrics = ['AP', 'AP50', 'AP75', 'API']
N_models = len(models)
N_metrics = len(metrics)

data = np.array([
    [35.4, 76.7, 27.5, 35.4], # CSAF
    [29.3, 67.8, 24.6, 29.5], # Cascade Mask R-CNN
    [27.1, 59.4, 24.3, 27.3], # Swin Transformer
    [22.0, 53.5, 16.3, 22.3], # Hybrid Task Cascade
    [30.2, 63.9, 24.1, 30.1], # Mask R-CNN
    [18.3, 48.9, 11.0, 18.4], # YOLOv5
    [22.1, 53.0, 15.3, 22.2], # Yolact
    [13.8, 43.8, 6.2, 13.9],  # QueryInst
    [11.4, 39.6, 3.1, 11.4]   # Solov2
])

# Create DataFrame
df = pd.DataFrame(data, index=models, columns=metrics)

# Prepare data for scatter plot
x_coords = []
y_coords = []
scores = []
metric_map = {metric: i for i, metric in enumerate(metrics)}
model_map = {model: i for i, model in enumerate(models)}

for model in models:
    for metric in metrics:
        x_coords.append(metric_map[metric])
        y_coords.append(model_map[model])
        scores.append(df.loc[model, metric])

# --- 创建散点图 (气泡图风格) ---
fig, ax = plt.subplots(figsize=(6, 10)) # Changed figsize for taller/narrower aspect ratio

cmap = "Greens" # Colormap for score
# dot_size = 200   # Fixed size for dots (can be changed or mapped later)

# --- Calculate dot sizes based on score ---
# Normalize scores (optional, but can help control size range)
# norm_scores = (np.array(scores) - min(scores)) / (max(scores) - min(scores))
base_size = 50 # Minimum dot size
scaling_factor = 15 # Controls how much size increases with score (adjust as needed)
# Size can be scaled linearly, or use power scale for more emphasis
sizes = base_size + scaling_factor * np.array(scores) # Linear scaling
# sizes = base_size + scaling_factor * (np.array(scores)**1.5) # Example power scaling

scatter = ax.scatter(
    x=x_coords,
    y=y_coords,
    s=sizes, # Use calculated sizes
    c=scores,
    cmap=cmap,
    alpha=0.7, # Adjusted alpha slightly
    edgecolors='darkgrey', # Optional: make edge darker
    linewidths=0.5
)

# --- 定制外观 ---

# Set ticks and labels
ax.set_xticks(list(metric_map.values()))
ax.set_xticklabels(list(metric_map.keys()))
ax.set_yticks(list(model_map.values()))
ax.set_yticklabels(list(model_map.keys()))

ax.set_xlabel('Metrics', fontsize=12, fontweight='bold')
ax.set_ylabel('Models', fontsize=12, fontweight='bold')

# --- 移除网格线 ---
ax.grid(False)

# Invert Y axis so CSAF (index 0) is at the top
ax.invert_yaxis()

# Add color bar
cbar = fig.colorbar(scatter)
cbar.set_label('Score')

# --- 布局和保存 ---
plt.tight_layout()
plt.savefig('Model_Performance_DotPlot.png', format='png')
plt.savefig('Model_Performance_DotPlot.svg', format='svg')
plt.savefig('Model_Performance_DotPlot.pdf', format='pdf')

plt.show() 