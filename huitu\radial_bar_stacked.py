import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.path import Path
import matplotlib.patches as mpatches

# 设置字体和样式
plt.rcParams.update({
    'font.family': 'Arial',
    'font.size': 14,  # 增大基础字体
    'figure.dpi': 300
})

# 使用用户提供的数据
data = {
    'Zone': ['Zone1', 'Zone2', 'Zone3'],
    'CSAF': [10248, 11027, 3294],  # 更新 CSAF 数据
    'Detectree2': [10094, 10190, 2779],  # 更新 Detectree2 数据
    'Real': [10982, 11563, 3589]  # 更新 Real 数据
}

# 计算准确率


# 计算准确率
csaf_accuracy = [round(1 - abs(c - r) / r, 2) for c, r in zip(data['CSAF'], data['Real'])]
detectree2_accuracy = [round(1 - abs(c - r) / r, 2) for c, r in zip(data['Detectree2'], data['Real'])]

# 创建图形
fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(polar=True))

# 最大值用于归一化
max_value = max([max(data['CSAF']), max(data['Detectree2']), max(data['Real'])])

# 设置角度和扇区数量
n_zones = len(data['Zone'])
theta = np.linspace(0, 2 * np.pi, n_zones, endpoint=False)

# 每个区域内的角度分配 - 增大间隔
width = (2 * np.pi / n_zones) * 0.7  # 减小扇区宽度以增加间隔
gap = (2 * np.pi / n_zones) * 0.3 / 4  # 增大间隔

# 颜色设置 - 使用与图片一致的颜色
colors = {
    'CSAF': '#FF9AA2',  # 粉红色
    'Detectree2': '#B5EAD7',   # 浅绿色
    'Real': '#C7CEEA'   # 浅蓝色
}

# 每个区域内条形的宽度
bar_width = width / 3  # 三种数据平分扇区宽度

# 数据类型绘制顺序
data_types = ['CSAF', 'Detectree2', 'Real']

# 绘制条形
for i, zone in enumerate(data['Zone']):
    # 区域中心角度
    zone_angle = theta[i]
    
    # 创建区域标签映射
    zone_map = {
        'Zone1': 'Zone1',
        'Zone2': 'Zone2',
        'Zone3': 'Zone3'
    }
    
    # 显示区域名称
    display_zone = zone_map[zone]
    
    # 在外圈添加区域标签 - 增大字体
    if zone_angle < np.pi:
        ax.text(zone_angle, max_value * 1.2, display_zone, 
                ha='center', va='center', fontsize=18, fontweight='bold')
    else:
        ax.text(zone_angle, max_value * 1.2, display_zone, 
                ha='center', va='center', fontsize=18, fontweight='bold',
                rotation=180)
    
    # 画出三种数据的条形 - 更好地排列顺序
    for j, key in enumerate(data_types):
        # 计算条形的角度位置
        bar_angle = zone_angle + (j - 1) * bar_width
        
        # 获取数值
        value = data[key][i]
        
        # 计算准确率文本
        if key == 'CSAF':
            accuracy = csaf_accuracy[i]
            acc_text = f"({accuracy*100:.0f}%)"
        elif key == 'Detectree2':
            accuracy = detectree2_accuracy[i]
            acc_text = f"({accuracy*100:.0f}%)"
        else:
            acc_text = ""
        
        # 绘制条形
        bar = ax.bar(bar_angle, value, width=bar_width*0.85, alpha=0.8, 
                     bottom=0.0, color=colors[key], edgecolor='white', linewidth=0.5)
        

        # 添加数值标签 - 调整旋转角度
        label_angle = bar_angle
        rotation_angle = np.degrees(label_angle)
        if np.pi/2 < label_angle < 3*np.pi/2:
            rotation_angle += 180
        
        # 在条形中间添加类型名

        # 在条形外部添加数值和准确率 - 增加偏移以避免遮挡
        text_radius = value * 1.08  # 增加文本偏移以避免被误差线遮挡
        
        # 创建带有白色背景的文本框
        text_str = f"{value}"
        if acc_text:
            text_str += f"\n{acc_text}"
            
        text_obj = ax.text(label_angle, text_radius, text_str, 
                ha='center', va='bottom', fontsize=11, fontweight='bold', 
                rotation=rotation_angle, rotation_mode='anchor',
                bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1.5))

# 设置刻度和网格
ax.set_ylim(0, max_value * 1.30)  # 增加上边界以放置标签
ax.set_xticks([])  # 移除角度刻度

# 设置径向网格线
radii = np.linspace(0, max_value, 5)
ax.set_yticks(radii)
ax.set_yticklabels([f'{int(r):,}' for r in radii], fontsize=11)
ax.yaxis.grid(True, linestyle='--', alpha=0.5, linewidth=0.8)
ax.xaxis.grid(False)

# 添加中心圆和标题
circle = plt.Circle((0, 0), 0.12 * max_value, transform=ax.transData._b, 
                   facecolor='white', edgecolor='black', linewidth=1.2, zorder=10)
ax.add_artist(circle)

# 添加中心文本
ax.text(0, 0, "Comparison", ha='center', va='center', 
        fontsize=12, fontweight='bold', color='black', zorder=11)

# 添加图例 - 调整位置和大小
legend_elements = [
    Rectangle((0, 0), 1, 1, facecolor=colors['CSAF'], edgecolor='white', alpha=0.8, label='CSAF'),
    Rectangle((0, 0), 1, 1, facecolor=colors['Detectree2'], edgecolor='white', alpha=0.8, label='Detectree2'),
    Rectangle((0, 0), 1, 1, facecolor=colors['Real'], edgecolor='white', alpha=0.8, label='Real')
]

# 将图例放在图表底部，更加明显
legend = ax.legend(handles=legend_elements, loc='lower center', bbox_to_anchor=(0.5, -0.08),
          frameon=True, ncol=3, fontsize=12, handlelength=2, handleheight=1)
legend.get_frame().set_linewidth(0.8)

# 保存图像
plt.tight_layout()
plt.savefig('E:\\deeplearn\\detectron2\\tgrs\\fig\\epr5.pdf', dpi=300, bbox_inches='tight')

plt.show() 