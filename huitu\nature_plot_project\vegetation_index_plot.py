import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib as mpl
import matplotlib.dates as mdates
import sys

print("Python版本:", sys.version)
print("matplotlib版本:", mpl.__version__)
print("numpy版本:", np.__version__)
print("pandas版本:", pd.__version__)


# 设置Nature风格字体配置
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 设置全局字体配置
plt.rcParams['font.family'] = 'sans-serif'  # 使用无衬线字体
plt.rcParams['font.sans-serif'] = ['Arial']  # 指定Arial为默认字体
plt.rcParams['font.size'] = 12  # 设置全局字体大小
plt.rcParams['axes.titlesize'] = 12  # 设置标题字体大小
plt.rcParams['axes.labelsize'] = 12  # 设置坐标轴标签字体大小
plt.rcParams['xtick.labelsize'] = 6  # 设置x轴刻度标签字体大小
plt.rcParams['ytick.labelsize'] = 6  # 设置y轴刻度标签字体大小
plt.rcParams['legend.fontsize'] = 12  # 设置图例字体大小
plt.rcParams['figure.titlesize'] = 12  # 设置图表标题字体大小


# 设置绘图风格以匹配示例图
plt.style.use('default')
plt.rcParams['axes.grid'] = False  # 关闭网格线
plt.rcParams['figure.figsize'] = (14, 10)  # 设置默认图表大小

# 配色方案
COLORS = {
    'green_points': '#00A651',  # 绿色数据点
    'orange_points': '#F7931E', # 橙色特殊点
    'red_line': '#FF0000',      # 红色拟合线
    'scvi_points': '#00A651',   # SCVI方法的点（绿色）
    'scvi_line': '#FF0000',     # SCVI方法的趋势线（红色）
    'amm_points': '#1E90FF',    # AMM方法的点（蓝色）
    'amm_line': '#FFA500',      # AMM方法的趋势线（橙色）
}

# 示例数据 - 完整植被指数数据
data = """
	20240809	20240905	20240909	20240926	20241009	20241024	20241031	20241108	20241115	20241128	20241218	20250124	20250206
ExG	0.54136616	0.4965929	0.3758612	0.4444074	0.4013867	0.3703497	0.3750776	0.3975182	0.4304174	0.4105941	0.46242326	0.4445651	0.4067239
VARI	0.39017594	0.1769925	0.3284648	0.3648841	0.386271	0.2709987	0.2579096	0.2704263	0.2886658	0.2291694	0.2719293	0.6027397	0.4111943
GRVI	0.3714161	0.20077	0.2875195	0.3203812	0.3675687	0.2459731	0.2538624	0.2536665	0.2341466	0.2404094	0.2555577	0.6222959	0.3881785
GLI	0.322717	0.20576	0.1991961	0.2247392	0.2958801	0.1951502	0.1892051	0.2016868	0.1820819	0.1905902	0.2217071	0.3729685	0.2198496
CVI	0.4649783	0.405318	0.3578 	0.4192345	0.3316211	0.3150681	0.3194154	0.3291861	0.3660258	0.3709717	0.4060744	0.4217714	0.3790541
CIVE	0.3784033	0.2764329	0.2378373	0.2686966	0.3326277	0.254953	0.2416903	0.2464778	0.2274313	0.2445461	0.2626014	0.4151497	0.2533702
TGI	0.5726 	0.5558024	0.4099 	0.4727128	0.4663239	0.4219477	0.4299912	0.4542072	0.4539849	0.442943	0.5060658	0.5216841	0.4587298
VGI	0.4238338	0.4388217	0.2805 	0.3194561	0.3757503	0.386614	0.3260931	0.2993507	0.2743471	0.365836	0.3122476	0.3957969	0.2653264
EXR	0.4061409	0.3820039	0.408819	0.3984713	0.3709036	0.40553	0.4208397	0.430647	0.4242315	0.4075686	0.4114606	0.3745641	0.4422951
EXGR	0.5538799	0.5465627	0.4935447	0.5171995	0.5311142	0.5028483	0.500833	0.5195729	0.5137571	0.5123477	0.5697962	0.5942216	0.5024052
NDI	0.3714161	0.20077	0.2875195	0.3203182	0.3675687	0.2459731	0.2538264	0.2536665	0.2341466	0.2404094	0.2556577	0.6222959	0.3881785
MGRVI	0.8640497	0.7774477	0.8779328	0.8832163	0.8001987	0.8344273	0.8213048	0.8067742	0.8181769	0.8265388	0.798691	0.887974	0.8575249
EGRBDI	0.1744125	0.0954908	0.1151012	0.1152843	0.2002728	0.1115886	0.1351925	0.1727179	0.151206	0.1267272	0.1981418	0.1094035	0.1444365
RGBRI	0.4589064	0.5180187	0.3044347	0.3493446	0.3792501	0.4366705	0.3734442	0.3432368	0.3411695	0.3930982	0.3422959	0.3790369	0.2716029
E-NGBDI	0.8230029	0.8486174	0.7774236	0.8459029	0.7416984	0.8092224	0.7826473	0.7116277	0.687097	0.8010469	0.6871299	0.8001632	0.713449
ExB	0.6759173	0.7118702	0.7777959	0.7738261	0.7192533	0.7450202	0.7676269	0.7672238	0.8023041	0.7595955	0.7460911	0.6476213	0.7748689
IKA W	0.4831361	0.5834863	0.2362851	0.3595943	0.3921518	0.4420896	0.4276556	0.3601815	0.3042266	0.4616215	0.3405077	0.354731	0.2190956

"""

# SCVI方法数据
data_scvi = """
	20240809	20240905	20240909	20240926	20241009	20241024	20241031	20241108	20241115	20241128	20241218	20250124	20250206
ExG	0.54136616	0.4965929	0.3758612	0.4444074	0.4013867	0.3703497	0.3750776	0.3975182	0.4304174	0.4105941	0.46242326	0.4445651	0.4067239
VARI	0.39017594	0.1769925	0.3284648	0.3648841	0.386271	0.2709987	0.2579096	0.2704263	0.2886658	0.2291694	0.2719293	0.6027397	0.4111943
GRVI	0.3714161	0.20077	0.2875195	0.3203812	0.3675687	0.2459731	0.2538624	0.2536665	0.2341466	0.2404094	0.2555577	0.6222959	0.3881785
GLI	0.322717	0.20576	0.1991961	0.2247392	0.2958801	0.1951502	0.1892051	0.2016868	0.1820819	0.1905902	0.2217071	0.3729685	0.2198496
CVI	0.4649783	0.405318	0.3578 	0.4192345	0.3316211	0.3150681	0.3194154	0.3291861	0.3660258	0.3709717	0.4060744	0.4217714	0.3790541
CIVE	0.3784033	0.2764329	0.2378373	0.2686966	0.3326277	0.254953	0.2416903	0.2464778	0.2274313	0.2445461	0.2626014	0.4151497	0.2533702
TGI	0.5726 	0.5558024	0.4099 	0.4727128	0.4663239	0.4219477	0.4299912	0.4542072	0.4539849	0.442943	0.5060658	0.5216841	0.4587298
VGI	0.4238338	0.4388217	0.2805 	0.3194561	0.3757503	0.386614	0.3260931	0.2993507	0.2743471	0.365836	0.3122476	0.3957969	0.2653264
EXR	0.4061409	0.3820039	0.408819	0.3984713	0.3709036	0.40553	0.4208397	0.430647	0.4242315	0.4075686	0.4114606	0.3745641	0.4422951
EXGR	0.5538799	0.5465627	0.4935447	0.5171995	0.5311142	0.5028483	0.500833	0.5195729	0.5137571	0.5123477	0.5697962	0.5942216	0.5024052
NDI	0.3714161	0.20077	0.2875195	0.3203182	0.3675687	0.2459731	0.2538264	0.2536665	0.2341466	0.2404094	0.2556577	0.6222959	0.3881785
MGRVI	0.8640497	0.7774477	0.8779328	0.8832163	0.8001987	0.8344273	0.8213048	0.8067742	0.8181769	0.8265388	0.798691	0.887974	0.8575249
EGRBDI	0.1744125	0.0954908	0.1151012	0.1152843	0.2002728	0.1115886	0.1351925	0.1727179	0.151206	0.1267272	0.1981418	0.1094035	0.1444365
RGBRI	0.4589064	0.5180187	0.3044347	0.3493446	0.3792501	0.4366705	0.3734442	0.3432368	0.3411695	0.3930982	0.3422959	0.3790369	0.2716029
E-NGBDI	0.8230029	0.8486174	0.7774236	0.8459029	0.7416984	0.8092224	0.7826473	0.7116277	0.687097	0.8010469	0.6871299	0.8001632	0.713449
ExB	0.6759173	0.7118702	0.7777959	0.7738261	0.7192533	0.7450202	0.7676269	0.7672238	0.8023041	0.7595955	0.7460911	0.6476213	0.7748689
IKA W	0.4831361	0.5834863	0.2362851	0.3595943	0.3921518	0.4420896	0.4276556	0.3601815	0.3042266	0.4616215	0.3405077	0.354731	0.2190956
"""

# AMM方法数据
data_amm = """
	20240809	20240905	20240909	20240926	20241009	20241024	20241031	20241108	20241115	20241128	20241218	20250124	20250206
ExG	0.4587	0.4704	0.4681	0.3717	0.3529	0.3485	0.3643	0.3603	0.4191	0.3625	0.4144	0.3916	0.4061
VARI	0.1879	0.4667	0.4937	0.4839	0.4709	0.4133	0.619	0.4921	0.4627	0.493	0.501	0.4958	0.4526
GRVI	0.3514	0.7017	0.3155	0.3197	0.249	0.6324	0.4335	0.3587	0.3605	0.4813	0.3631	0.6677	0.2861
GLI	0.2678	0.4893	0.2881	0.2441	0.1549	0.4147	0.3418	0.1925	0.2021	0.224	0.1707	0.3875	0.2042
CVI	0.5385	0.6202	0.5489	0.4980	0.5276	0.4493	0.4477	0.3744	0.5239	0.5202	0.4858	0.6363	0.4879
CIVE	0.3509	0.6541	0.3477	0.3482	0.2173	0.5831	0.4824	0.2724	0.2822	0.3114	0.2409	0.5576	0.2954
TGI	0.4065	0.4292	0.3133	0.3877	0.3286	0.3232	0.3450	0.3122	0.3554	0.3308	0.3638	0.3621	0.3512
VGI	0.3928	0.6165	0.4199	0.4245	0.5504	0.5434	0.5517	0.3315	0.3487	0.5578	0.3369	0.4816	0.3357
EXR	0.4397	0.4785	0.5209	0.4892	0.5286	0.5190	0.5180	0.5140	0.4825	0.4902	0.4666	0.4723	0.4475
EXGR	0.4652	0.4559	0.3297	0.4077	0.3855	0.3741	0.3849	0.3759	0.3969	0.3841	0.4280	0.4210	0.4119
NDI	0.3514	0.7017	0.3155	0.3197	0.2490	0.6324	0.4335	0.3587	0.3605	0.4813	0.3631	0.6677	0.2861
MGRVI	0.8638	0.9955	0.9664	0.9666	0.8128	0.9938	0.9865	0.9378	0.9644	0.9862	0.8786	0.9877	0.8903
EGRBDI	0.0946	0.0099	0.0148	0.0169	0.0927	0.0182	0.0104	0.0072	0.0203	0.0115	0.0485	0.0117	0.0404
RGBRI	0.5554	0.7093	0.4451	0.4896	0.3542	0.6638	0.6058	0.4293	0.4793	0.5215	0.4177	0.6287	0.4244
E-NGBDI	0.8418	0.9889	0.9736	0.9622	0.8385	0.9924	0.9412	0.9656	0.8997	0.9844	0.7855	0.9909	0.8914
ExB	0.6603	0.602	0.5754	0.5722	0.7410	0.6309	0.5192	0.6522	0.6417	0.6273	0.6639	0.4985	0.6497
"""

try:
    print("处理数据...")
    # 处理SCVI数据
    lines_scvi = [line.strip() for line in data_scvi.strip().split('\n')]
    headers = ['index'] + lines_scvi[0].split()
    rows_scvi = []
    for line in lines_scvi[1:]:
        if not line or line.isspace():
            continue
        parts = line.split()
        if len(parts) <= 1:
            continue
        
        index_name = parts[0]
        if len(parts) == len(headers):
            rows_scvi.append(parts)
        elif 'E-NGRDI' in line:
            rows_scvi.append(['E-NGRDI'] + parts[1:])
        elif 'IKA W' in line:
            rows_scvi.append(['IKA_W'] + parts[2:])
        else:
            data_values = parts[1:] if len(parts) > len(headers) else parts[1:]
            while len(data_values) < len(headers) - 1:
                data_values.append(None)
            if len(data_values) > len(headers) - 1:
                data_values = data_values[:len(headers) - 1]
            rows_scvi.append([parts[0]] + data_values)
    
    # 处理AMM数据
    lines_amm = [line.strip() for line in data_amm.strip().split('\n')]
    rows_amm = []
    for line in lines_amm[1:]:
        if not line or line.isspace():
            continue
        parts = line.split()
        if len(parts) <= 1:
            continue
        
        index_name = parts[0]
        if len(parts) == len(headers):
            rows_amm.append(parts)
        elif 'E-NGRDI' in line:
            rows_amm.append(['E-NGRDI'] + parts[1:])
        else:
            data_values = parts[1:] if len(parts) > len(headers) else parts[1:]
            while len(data_values) < len(headers) - 1:
                data_values.append(None)
            if len(data_values) > len(headers) - 1:
                data_values = data_values[:len(headers) - 1]
            rows_amm.append([parts[0]] + data_values)
    
    # 创建数据框
    df_scvi = pd.DataFrame(rows_scvi, columns=headers)
    df_amm = pd.DataFrame(rows_amm, columns=headers)
    print("数据框创建成功，SCVI形状:", df_scvi.shape, "AMM形状:", df_amm.shape)
    
    # 转换列类型
    for col in headers[1:]:
        df_scvi[col] = pd.to_numeric(df_scvi[col], errors='coerce')
        df_amm[col] = pd.to_numeric(df_amm[col], errors='coerce')
    
    print("数据类型转换完成")
    print("可用的植被指数:", ", ".join(df_scvi['index'].values))

    # 将日期列转换为datetime对象
    dates = [datetime.strptime(str(date), '%Y%m%d') for date in df_scvi.columns[1:]]
    date_strings = [date.strftime('%Y-%m') for date in dates]  # 只保留年份和月份


    def plot_fixed_layout_matrix(indices=None, output_filename='vegetation_indices_fixed_layout', n_rows=4, n_cols=4):
        """
        生成固定布局的植被指数时间序列图矩阵，同时显示SCVI和AMM两种方法的数据
        """
        try:
            print("开始生成固定布局时间序列图矩阵...")
            
            available_indices = list(df_scvi['index'].values)
            if indices is not None:
                indices_to_plot = indices
            else:
                indices_to_plot = [idx for idx in available_indices if idx != 'IKA_W']
            
            total_plots = n_rows * n_cols
            if len(indices_to_plot) > total_plots:
                indices_to_plot = indices_to_plot[:total_plots]
            elif len(indices_to_plot) < total_plots:
                while len(indices_to_plot) < total_plots:
                    next_idx = indices_to_plot[len(indices_to_plot) % len(indices_to_plot)]
                    indices_to_plot.append(next_idx)
            
            print(f"将为以下指数生成图表: {', '.join(indices_to_plot)}")
            print(f"使用固定布局: {n_rows}行 x {n_cols}列")
            
            fig_width = min(18, n_cols * 3.2)
            fig_height = min(14, n_rows * 3)
            
            # 创建具有透明背景的图表
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))
            fig.patch.set_alpha(0.0)  # 设置图表背景透明
            
            plt.subplots_adjust(wspace=0.25, hspace=0.4)
            
            if n_rows == 1 and n_cols == 1:
                axes = np.array([[axes]])
            elif n_rows == 1:
                axes = axes.reshape(1, -1)
            elif n_cols == 1:
                axes = axes.reshape(-1, 1)
            
            date_nums = mdates.date2num(dates)
            x_nums = np.arange(len(dates))
            
            for idx, index in enumerate(indices_to_plot):
                row = idx // n_cols
                col = idx % n_cols
                
                print(f"处理行{row+1}列{col+1}: {index}")
                ax = axes[row, col]
                
                # 获取SCVI方法的数据
                scvi_row = df_scvi[df_scvi['index'] == index]
                if len(scvi_row) == 0:
                    print(f"警告: 找不到SCVI指数 {index}")
                    continue
                scvi_values = scvi_row.iloc[0, 1:].values.astype(float)
                
                # 获取AMM方法的数据
                amm_row = df_amm[df_amm['index'] == index]
                if len(amm_row) == 0:
                    print(f"警告: 找不到AMM指数 {index}")
                    continue
                amm_values = amm_row.iloc[0, 1:].values.astype(float)
                
                # 计算拟合曲线
                # SCVI拟合
                z_scvi = np.polyfit(x_nums, scvi_values, 2)
                p_scvi = np.poly1d(z_scvi)
                r_squared_scvi = np.corrcoef(scvi_values, p_scvi(x_nums))[0, 1]**2
                
                # AMM拟合
                z_amm = np.polyfit(x_nums, amm_values, 2)
                p_amm = np.poly1d(z_amm)
                r_squared_amm = np.corrcoef(amm_values, p_amm(x_nums))[0, 1]**2
                
                # 绘制拟合曲线
                x_smooth = np.linspace(0, len(dates)-1, 100)
                y_smooth_scvi = p_scvi(x_smooth)
                y_smooth_amm = p_amm(x_smooth)
                x_dates_smooth = [dates[0] + (dates[-1] - dates[0]) * (x / (len(dates)-1)) for x in x_smooth]
                
                # 先绘制拟合曲线
                ax.plot(x_dates_smooth, y_smooth_scvi, '-', color=COLORS['scvi_line'], linewidth=1.0, zorder=1)
                ax.plot(x_dates_smooth, y_smooth_amm, '-', color=COLORS['amm_line'], linewidth=1.0, zorder=2)
                
                # 绘制数据点，调整大小和顺序
                # SCVI点稍大，在底层
                scvi_scatter = ax.scatter(dates, scvi_values, s=35, color=COLORS['scvi_points'], alpha=0.9, 
                                        label='SCVI', edgecolors='none', zorder=3)
                # AMM点稍小，在上层
                amm_scatter = ax.scatter(dates, amm_values, s=25, color=COLORS['amm_points'], alpha=0.9,
                                       label='SIC', edgecolors='none', zorder=4)
                
                # 添加图例
                ax.legend(loc='upper right', fontsize=6)
                
                # 格式化系数
                def format_coef(a):
                    return f"{a:.2e}" if np.abs(a) < 0.001 else f"{a:+.3f}"
                
                # SCVI拟合方程
                a_scvi, b_scvi, c_scvi = z_scvi
                eq_scvi = f"SCVI: y = {format_coef(a_scvi)}x² {format_coef(b_scvi)}x {format_coef(c_scvi)}"
                r2_scvi = f"R² = {r_squared_scvi:.4f}"
                
                # AMM拟合方程
                a_amm, b_amm, c_amm = z_amm
                eq_amm = f"SIC: y = {format_coef(a_amm)}x² {format_coef(b_amm)}x {format_coef(c_amm)}"
                r2_amm = f"R² = {r_squared_amm:.4f}"
                
                # 添加拟合方程文本
                font_size = 6 if n_cols > 3 else 7
                ax.text(0.05, 0.95, eq_scvi, transform=ax.transAxes, 
                       fontsize=font_size, ha='left', va='top', color=COLORS['scvi_line'])
                ax.text(0.05, 0.90, r2_scvi, transform=ax.transAxes,
                       fontsize=font_size, ha='left', va='top', color=COLORS['scvi_line'])
                ax.text(0.05, 0.85, eq_amm, transform=ax.transAxes,
                       fontsize=font_size, ha='left', va='top', color=COLORS['amm_line'])
                ax.text(0.05, 0.80, r2_amm, transform=ax.transAxes,
                       fontsize=font_size, ha='left', va='top', color=COLORS['amm_line'])
                
                # 设置日期格式
                # 设置日期格式
                date_formatter = mdates.DateFormatter('%Y-%m')  # 修改为只显示年份和月份
                ax.xaxis.set_major_formatter(date_formatter)
                ax.xaxis.set_major_locator(mdates.MonthLocator())  # 每月一个主刻度
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

                # 设置y轴范围
                y_min = min(min(scvi_values), min(amm_values))
                y_max = max(max(scvi_values), max(amm_values))
                y_range = y_max - y_min
                ax.set_ylim(y_min - 0.05*y_range, y_max + 0.05*y_range)
                
                # 添加标题和标签
                ax.text(0.5, 1.05, index, transform=ax.transAxes,
                       fontsize=font_size+1, ha='center', va='bottom', fontweight='bold')
                
                ax.tick_params(axis='both', which='major', labelsize=10)
                ax.set_ylabel("Vegetation Index", fontsize=10)
                ax.set_xlabel("Date", fontsize=10)
                for spine in ax.spines.values():
                    spine.set_linewidth(0.5)
                    spine.set_color('black')
            
            plt.tight_layout(pad=1.0)
            
            print("正在保存图表...")
            # 保存为PDF格式
            plt.savefig(f'{output_filename}.pdf', format='pdf', bbox_inches='tight', transparent=True)
            # 保存为PNG格式（高分辨率）
            plt.savefig(f'{output_filename}.png', format='png', bbox_inches='tight', dpi=100, pad_inches=0.1, transparent=True)
            # 保存为SVG格式（矢量图）
            plt.savefig(f'{output_filename}.svg', format='svg', bbox_inches='tight', dpi=1200, pad_inches=0.1, transparent=True)
            print(f'已成功保存图表为以下格式：')
            print(f'- PDF: {output_filename}.pdf')
            print(f'- PNG: {output_filename}.png')
            print(f'- SVG: {output_filename}.svg')
            
            plt.close()
            
        except Exception as e:
            print(f"保存图表时出错: {e}")
            import traceback
            traceback.print_exc()

except Exception as e:
    print(f"程序运行出错: {e}")
    import traceback
    traceback.print_exc()

# 在文件最后添加以下代码来调用函数
if __name__ == "__main__":
    try:
        # 创建4x4的固定布局图表
        plot_fixed_layout_matrix(
            indices=None,  # 使用所有可用的指数
            output_filename='vegetation_indices_matrix',  # 输出文件名
            n_rows=4,  # 4行
            n_cols=4   # 4列
        )
        print("程序执行完成！")
        
    except Exception as e:
        print(f"主程序执行出错: {e}")
        import traceback
        traceback.print_exc() 