import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
from matplotlib.patches import Wedge
from datetime import datetime, timedelta

# 设置字体和样式
plt.rcParams.update({
    'font.family': 'Arial',
    'font.size': 11,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.dpi': 300
})

# 创建图表
fig, ax = plt.subplots(figsize=(16, 6), facecolor='white')

# 设置背景色为白色
ax.set_facecolor('white')

# 定义数据
x_labels = ['09/30', '10/05', '10/09', '10/26', '11/09', '11/24', '12/31', 
            '01/08', '01/15', '01/28', '02/18', '03/24', '04/08']

real_values = [538, 538, 180, 142, 121, 121, 121, 121, 121, 121, 121, 119, 117]
pred_values = [519, 514, 169, 131, 110, 109, 112, 111, 112, 110, 111, 108, 107]
accuracy = [0.96, 0.96, 0.94, 0.92, 0.91, 0.90, 0.93, 0.92, 0.93, 0.91, 0.92, 0.91, 0.91]

# 定义更漂亮的颜色
real_color = '#3a7359'  # 更自然的绿色
pred_color = '#2a6c8e'  # 更柔和的蓝色

# 动态计算圆圈大小
max_value = max(max(real_values), max(pred_values))
circle_sizes = []
for real, pred in zip(real_values, pred_values):
    size = max(real, pred) / max_value * 0.5
    # 确保前两个点更大
    if real > 500:
        size = 0.7
    circle_sizes.append(size)

# 绘制分割的饼图
for i in range(len(real_values)):
    center = (i, 0)
    
    # 创建左半圆（实际值）
    left_wedge = Wedge(center, circle_sizes[i], 90, 270, 
                      fc=real_color, ec='black', lw=0.8, alpha=0.9)
    
    # 创建右半圆（预测值）
    right_wedge = Wedge(center, circle_sizes[i], 270, 90, 
                       fc=pred_color, ec='black', lw=0.8, alpha=0.9)
    
    ax.add_patch(left_wedge)
    ax.add_patch(right_wedge)
    
    # 格式化文本
    real_text = f"Real: {real_values[i]}"
    pred_text = f"Pred: {pred_values[i]}"
    acc_text = f"Acc: {accuracy[i]:.2f}"
    
    # 创建更整洁的标签布局
    spacing = 0.2
    text_y = -1.0
    plt.text(i, text_y, real_text, ha='center', fontsize=10, color='black')
    plt.text(i, text_y - spacing, pred_text, ha='center', fontsize=10, color='black')
    plt.text(i, text_y - 2*spacing, acc_text, ha='center', fontsize=10, color='black')

# 设置x轴标签
plt.xticks(range(len(real_values)), x_labels, rotation=45)
plt.xlabel('Date', fontsize=12, labelpad=15)

# 去除y轴刻度和标签
ax.set_yticks([])
ax.yaxis.set_visible(False)

# 设置适当的坐标范围
ax.set_xlim(-0.5, len(real_values) - 0.5)
ax.set_ylim(-1.7, 0.9)  # 调整以适应标签

# 添加图例
real_patch = plt.Rectangle((0, 0), 1, 1, fc=real_color, ec='black', alpha=0.9, label='Real values')
pred_patch = plt.Rectangle((0, 0), 1, 1, fc=pred_color, ec='black', alpha=0.9, label='Predicted values')
legend = plt.legend(handles=[real_patch, pred_patch], loc='upper right', 
                   frameon=True, fancybox=True, framealpha=0.9, 
                   edgecolor='lightgray')

# 移除边框
for spine in ax.spines.values():
    spine.set_visible(False)

# 美化x轴刻度的外观
for tick in ax.get_xticklabels():
    tick.set_rotation(45)
    tick.set_ha('right')

# 轻微的网格线仅用于参考
ax.grid(False)

# 美化布局
plt.tight_layout()
plt.subplots_adjust(bottom=0.25)

# 保存图像
plt.savefig('time_series_pie_comparison.png', dpi=300, bbox_inches='tight')
plt.savefig('time_series_pie_comparison.pdf', format='pdf', bbox_inches='tight')

# 显示图表
plt.show() 