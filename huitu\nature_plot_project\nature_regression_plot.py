import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
import matplotlib as mpl

# 设置Nature期刊风格的绘图样式
plt.style.use('default')
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['xtick.labelsize'] = 9
plt.rcParams['ytick.labelsize'] = 9
plt.rcParams['legend.fontsize'] = 9
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 时间日期（x轴）
# 时间日期（x轴）
dates = ['0809', '0905', '0909', '0926', '1009', '1024', '1031', '1108', '1115', '1128', '1218', '0124', '0206']

x_numeric = np.arange(len(dates))

# 为每个指数创建带置信区间的回归图
def create_nature_regression_plot(index_name, canopy_data, color='#E41A1C'):
    """为指定的植被指数创建带置信区间的回归图，符合Nature期刊风格"""
    
    # 提取这个植被指数在三个树冠上的数据
    canopy1_values = canopy1[index_name]
    canopy2_values = canopy2[index_name]
    canopy3_values = canopy3[index_name]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(6, 3.5), facecolor='white')
    
    # 绘制所有树冠的数据点
    ax.scatter(x_numeric, canopy1_values, color='#4A7B8C', s=35, marker='o', label='Canopy 1')
    ax.scatter(x_numeric, canopy2_values, color='#8C4A59', s=35, marker='s', label='Canopy 2')
    ax.scatter(x_numeric, canopy3_values, color='#2E5E4B', s=35, marker='^', label='Canopy 3')
    
    # 合并所有树冠数据用于回归分析
    all_x = np.concatenate([x_numeric, x_numeric, x_numeric])
    all_y = np.concatenate([canopy1_values, canopy2_values, canopy3_values])
    
    # 计算线性回归
    slope, intercept, r_value, p_value, std_err = stats.linregress(all_x, all_y)
    line = slope * x_numeric + intercept
    
    # 绘制回归线
    ax.plot(x_numeric, line, color='#E41A1C', linewidth=1.5)
    
    # 计算并绘制95%置信区间
    n = len(all_x)
    mean_x = np.mean(all_x)
    
    # 计算预测的置信区间
    t_val = stats.t.ppf(0.975, n-2)  # 95%置信度的t值
    s_err = np.sqrt(np.sum((all_y - (intercept + slope * all_x))**2) / (n - 2))  # 标准误差
    
    # 计算置信区间
    confs = t_val * s_err * np.sqrt(1/n + (x_numeric - mean_x)**2 / np.sum((all_x - mean_x)**2))
    upper = line + confs
    lower = line - confs
    
    # 填充置信区间
    ax.fill_between(x_numeric, lower, upper, color='grey', alpha=0.2)
    
    # 添加统计信息
    stat_text = f"Slope={slope:.2f}, R²={r_value**2:.2f}, p={p_value:.3f}"
    ax.text(0.02, 0.95, stat_text, transform=ax.transAxes, fontsize=8, 
            color='#E41A1C', verticalalignment='top')
    
    # 设置轴标签
    ax.set_xlabel('Date', fontsize=10, labelpad=8)
    ax.set_ylabel(f'{index_name} Index', fontsize=10, labelpad=8)
    
    # 设置x轴刻度
    ax.set_xticks(x_numeric)
    ax.set_xticklabels(dates)
    
    # 添加图例
    ax.legend(loc='best', frameon=True, framealpha=0.7)
    
    # 关闭网格线
    ax.grid(False)
    
    # 优化布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(f'{index_name}_nature_regression.png', dpi=600, bbox_inches='tight', pad_inches=0.1)
    plt.savefig(f'{index_name}_nature_regression.pdf', bbox_inches='tight', pad_inches=0.1)
    plt.savefig(f'{index_name}_nature_regression.svg', bbox_inches='tight', pad_inches=0.1)
    
    print(f"Nature style regression plot created for {index_name} index")
    return fig

# 比较所有植被指数的趋势
def create_combined_indices_plot():
    """创建一个包含所有植被指数趋势的组合图"""
    
    # 创建图表和子图布局
    fig, axes = plt.subplots(2, 2, figsize=(10, 8), facecolor='white')
    axes = axes.flatten()
    
    # 植被指数和颜色
    indices = ['ExG', 'CVI', 'MGRVI', 'EXB']
    colors = ['#2E5E4B', '#4A7B8C', '#8C4A59', '#6B929E']
    
    # 处理每个植被指数
    for i, (index_name, color) in enumerate(zip(indices, colors)):
        ax = axes[i]
        
        # 获取所有树冠的数据
        canopy1_values = canopy1[index_name]
        canopy2_values = canopy2[index_name]
        canopy3_values = canopy3[index_name]
        
        # 绘制散点
        ax.scatter(x_numeric, canopy1_values, color='#4A7B8C', s=35, alpha=0.7, marker='o', label='Canopy 1')
        ax.scatter(x_numeric, canopy2_values, color='#8C4A59', s=35, alpha=0.7, marker='s', label='Canopy 2')
        ax.scatter(x_numeric, canopy3_values, color='#2E5E4B', s=35, alpha=0.7, marker='^', label='Canopy 3')
        
        # 合并所有树冠数据用于回归分析
        all_x = np.concatenate([x_numeric, x_numeric, x_numeric])
        all_y = np.concatenate([canopy1_values, canopy2_values, canopy3_values])
        
        # 计算线性回归
        slope, intercept, r_value, p_value, std_err = stats.linregress(all_x, all_y)
        line = slope * x_numeric + intercept
        
        # 绘制回归线
        ax.plot(x_numeric, line, color=color, linewidth=1.5)
        
        # 计算并绘制95%置信区间
        n = len(all_x)
        mean_x = np.mean(all_x)
        t_val = stats.t.ppf(0.975, n-2)
        s_err = np.sqrt(np.sum((all_y - (intercept + slope * all_x))**2) / (n - 2))
        confs = t_val * s_err * np.sqrt(1/n + (x_numeric - mean_x)**2 / np.sum((all_x - mean_x)**2))
        upper = line + confs
        lower = line - confs
        
        # 填充置信区间
        ax.fill_between(x_numeric, lower, upper, color='grey', alpha=0.2)
        
        # 添加统计信息
        stat_text = f"Slope={slope:.2f}, R²={r_value**2:.2f}, p={p_value:.3f}"
        ax.text(0.02, 0.95, stat_text, transform=ax.transAxes, fontsize=8, color=color, verticalalignment='top')
        
        # 设置标题和标签
        ax.set_title(f'{index_name}', fontsize=11)
        ax.set_xlabel('Date', fontsize=10)
        ax.set_ylabel(f'{index_name} Index', fontsize=10)
        
        # 设置x轴刻度
        ax.set_xticks(x_numeric)
        ax.set_xticklabels(dates)
        
        # 关闭网格线
        ax.grid(False)
        
        # 只在第一个子图中添加图例
        if i == 0:
            ax.legend(loc='best', frameon=True, framealpha=0.7)
    
    # 优化布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('all_indices_nature_regression.png', dpi=600, bbox_inches='tight', pad_inches=0.1)
    plt.savefig('all_indices_nature_regression.pdf', bbox_inches='tight', pad_inches=0.1)
    plt.savefig('all_indices_nature_regression.svg', bbox_inches='tight', pad_inches=0.1)
    plt.show()
    print("Combined indices regression plot created in Nature style")
    return fig

# 主函数
def main():
    # 创建数据
    global canopy1, canopy2, canopy3


    # 树冠1的数据
    canopy1 = {
        'ExG': [0.5764534, 0.5035132, 0.3874947, 0.42891, 0.401387, 0.4685979, 0.3948133, 0.3849822, 0.4232759,
                0.4514346, 0.524074, 0.454456, 0.414858],
        'CVI': [0.4723432, 0.41436, 0.35095483, 0.34607, 0.331621, 0.3778748, 0.3609359, 0.3445884, 0.3775304, 0.417605,
                0.4675018, 0.430207, 0.386635],
        'MGRVI': [0.873251, 0.768565, 0.83448511, 0.86215, 0.800199, 0.80422, 0.7466185, 0.6947784, 0.8350153,
                  0.6765864, 0.8539257, 0.906734, 0.874675],
        'EXB': [0.695142341, 0.715696357, 0.81389591, 0.76324, 0.719253, 0.7354636, 0.6994844, 0.8099242, 0.6946393,
                0.6963692, 0.7525901, 0.660574, 0.790366]
    }

    # 树冠2的数据
    canopy2 = {
        'ExG': [0.541312, 0.4836543, 0.36926894, 0.44096, 0.388343, 0.4639508, 0.3962725, 0.3961348, 0.3734632,
                0.4585701, 0.480593, 0.435674, 0.398589],
        'CVI': [0.4415142, 0.393474812, 0.34305723, 0.36144, 0.322027, 0.4172846, 0.1896627, 0.3470715, 0.3212644,
                0.4146229, 0.4156693, 0.413336, 0.371472],
        'MGRVI': [0.861342135, 0.745434542, 0.85613901, 0.89123, 0.840209, 0.7592297, 0.8725845, 0.8228972, 0.8509057,
                  0.7099463, 0.6306188, 0.870216, 0.840374],
        'EXB': [0.667654, 0.69412545, 0.74805617, 0.79192, 0.683291, 0.7590165, 0.6946992, 0.7686097, 0.72676501,
                0.7563242, 0.7625658, 0.63467, 0.759373]
    }

    # 树冠3的数据
    canopy3 = {
        'ExG': [0.5231425, 0.4436543, 0.36214464, 0.45918, 0.421456, 0.4228484, 0.4100583, 0.4249226, 0.4216866,
                0.4630271, 0.4681649, 0.451033, 0.412826],
        'CVI': [0.4551235, 0.383474812, 0.34688855, 0.35107, 0.340988, 0.346686, 0.1988318, 0.3346633, 0.3618345,
                0.4292103, 0.4019993, 0.432416, 0.385426],
        'MGRVI': [0.8451325, 0.734575464, 0.84921457, 0.83404, 0.760189, 0.8356118, 0.7940347, 0.79819, 0.7767971,
                  0.6697405, 0.7593806, 0.913612, 0.871673],
        'EXB': [0.6883985, 0.665766555, 0.80576143, 0.73891, 0.755216, 0.78208, 0.7343576, 0.7024199, 0.7156579,
                0.7510814, 0.6928242, 0.663046, 0.788854]
    }

    # 为每个植被指数创建单独的回归分析图
    for index_name in ['ExG', 'CVI', 'MGRVI', 'EXB']:
        create_nature_regression_plot(index_name, [canopy1, canopy2, canopy3])
    
    # 创建组合图
    create_combined_indices_plot()

if __name__ == "__main__":
    main() 