import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon, PathPatch
from matplotlib.path import Path
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['savefig.dpi'] = 600

# 将12个月数据分组成5个部分
grouped_data = {
    'CSAF': [611, 592, 509, 611, 546],
    'MRCNN': [743, 846, 690, 774, 779],
    'real': [556, 548, 520, 551, 551]
}

# 计算准确率
accuracy_csaf = [1 - (abs(csaf - real) / real) for csaf, real in zip(grouped_data['CSAF'], grouped_data['real'])]
accuracy_mrcnn = [1 - (abs(mrcnn - real) / real) for mrcnn, real in zip(grouped_data['MRCNN'], grouped_data['real'])]

# 计算总和和平均准确率
total_real = sum(grouped_data['real'])
total_csaf = sum(grouped_data['CSAF'])
total_mrcnn = sum(grouped_data['MRCNN'])
avg_acc_csaf = np.mean(accuracy_csaf) * 100
avg_acc_mrcnn = np.mean(accuracy_mrcnn) * 100

# 创建两个图表
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7), facecolor='white')

# 完全匹配图片的花瓣颜色
petal_colors = ['#45D6D3', '#5E9BDF', '#A8D48E', '#F290CD', '#FF9060']
curve_colors = ['#EF7F94', '#3CD9E3', '#7BD55F', '#B08AFF', '#FF7E3D']

def create_pentapetalae(ax, model_values, real_values, accuracies, model_name, total_model, total_real, avg_accuracy):
    # 中心五边形
    n_sides = 5
    center_radius = 2.5
    
    # 创建蓝色填充的中心多边形
    pentagon_verts = [(center_radius*np.cos(2*np.pi*i/n_sides - np.pi/2), 
                      center_radius*np.sin(2*np.pi*i/n_sides - np.pi/2)) 
                      for i in range(n_sides)]
    pentagon = Polygon(pentagon_verts, closed=True, 
                      facecolor='#94ADDa', edgecolor='#2D5597', 
                      linewidth=1.5, alpha=0.6, zorder=10)
    ax.add_patch(pentagon)
    
    # 花瓣位置计算
    n_petals = len(model_values)
    radius = 7  # 花瓣中心到整体中心的距离
    petal_radius = 3.5  # 花瓣大小
    
    # 花瓣之间的连接曲线
    for i in range(n_petals):
        angle1 = 2 * np.pi * i / n_petals - np.pi/2  # 从顶部开始
        x1 = radius * np.cos(angle1)
        y1 = radius * np.sin(angle1)
        
        for j in range(i+1, n_petals):
            angle2 = 2 * np.pi * j / n_petals - np.pi/2
            x2 = radius * np.cos(angle2)
            y2 = radius * np.sin(angle2)
            
            # 计算控制点 (让曲线更自然)
            control_x = (x1 + x2) * 0.5
            control_y = (y1 + y2) * 0.5
            dist = np.sqrt(control_x**2 + control_y**2)
            control_x = control_x / dist * (radius * 0.5)
            control_y = control_y / dist * (radius * 0.5)
            
            # 贝塞尔曲线
            verts = [
                (x1, y1),
                ((x1+control_x)*0.5, (y1+control_y)*0.5),
                (control_x, control_y),
                ((x2+control_x)*0.5, (y2+control_y)*0.5),
                (x2, y2)
            ]
            codes = [Path.MOVETO, Path.CURVE4, Path.CURVE4, Path.CURVE4, Path.LINETO]
            path = Path(verts, codes)
            
            curve_color = curve_colors[(i+j) % len(curve_colors)]
            patch = PathPatch(path, facecolor='none', edgecolor=curve_color, 
                             linestyle='-', alpha=0.6, linewidth=1.2, zorder=1)
            ax.add_patch(patch)
    
    # 创建花瓣 (从顶部开始, 顺时针)
    for i in range(n_petals):
        angle = 2 * np.pi * i / n_petals - np.pi/2  # 从顶部开始
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # 根据值的大小稍微调整花瓣大小
        size_factor = 0.85 + 0.3 * (model_values[i] / max(model_values))
        size = petal_radius * size_factor
        
        # 创建填充花瓣
        circle = Circle((x, y), size, 
                        facecolor=petal_colors[i], alpha=0.5, 
                        edgecolor='none', zorder=5)
        ax.add_patch(circle)
        
        # 在花瓣中显示预测值、真实值和准确率
        # 预测值 (居中略偏上)
        ax.text(x, y+0.3, f"{model_values[i]}", 
                ha='center', va='center', fontsize=11, fontweight='bold', 
                color='black', zorder=6)
        
        # 真实值 (左下)
        ax.text(x-0.8, y-0.8, f"R:{real_values[i]}", 
                ha='center', va='center', fontsize=8, 
                color='#333333', zorder=6)
        
        # 准确率 (右下)
        ax.text(x+0.8, y-0.8, f"Acc:{accuracies[i]:.2f}", 
                ha='center', va='center', fontsize=8, 
                color='#333333', zorder=6)
    
    # 添加中心信息
    if model_name == "CSAF":
        name = model_name
    else:
        name = "Cascade\nMask R-CNN"
        
    ax.text(0, 0.8, name, ha='center', va='center', 
            fontsize=11, fontweight='bold', color='navy', zorder=11)
    ax.text(0, 0, "Core", ha='center', va='center', 
            fontsize=10, color='navy', zorder=11)
    ax.text(0, -0.5, f"{int(total_model)}\n{int(total_real)}", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='navy', zorder=11)
    ax.text(0, -1.5, f"[{avg_accuracy:.1f}%]", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='navy', zorder=11)
    
    # 设置图表范围和关闭坐标轴
    margin = 1.5
    ax.set_xlim(-radius*margin, radius*margin)
    ax.set_ylim(-radius*margin, radius*margin)
    ax.set_aspect('equal')
    ax.axis('off')

# 创建两个花瓣图
create_pentapetalae(ax1, grouped_data['CSAF'], grouped_data['real'], accuracy_csaf,
                   "CSAF", total_csaf, total_real, avg_acc_csaf)
create_pentapetalae(ax2, grouped_data['MRCNN'], grouped_data['real'], accuracy_mrcnn,
                   "Mask R-CNN", total_mrcnn, total_real, avg_acc_mrcnn)

# 微调图表间距
plt.subplots_adjust(wspace=0.05)

# 保存图表
plt.savefig('pentapetalae_final.png', dpi=600, bbox_inches='tight')
plt.savefig('pentapetalae_final.pdf', bbox_inches='tight')

print("最终版五瓣花图表已创建")
plt.show() 