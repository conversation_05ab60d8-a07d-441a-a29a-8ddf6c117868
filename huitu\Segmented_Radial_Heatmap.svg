<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="536.0736pt" height="462.929375pt" viewBox="0 0 536.0736 462.929375" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-25T18:12:56.696014</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 462.929375 
L 536.0736 462.929375 
L 536.0736 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 426.24 246.209375 
C 426.24 218.695461 420.820359 191.449085 410.29124 166.029542 
C 399.76212 140.61 384.328288 117.511638 364.873013 98.056362 
C 345.417737 78.601087 322.319375 63.167255 296.899833 52.638135 
C 271.48029 42.109016 244.233914 36.689375 216.72 36.689375 
C 189.206086 36.689375 161.95971 42.109016 136.540167 52.638135 
C 111.120625 63.167255 88.022263 78.601087 68.566987 98.056362 
C 49.111712 117.511638 33.67788 140.61 23.14876 166.029542 
C 12.619641 191.449085 7.2 218.695461 7.2 246.209375 
C 7.2 273.723289 12.619641 300.969665 23.14876 326.389208 
C 33.67788 351.80875 49.111712 374.907112 68.566987 394.362388 
C 88.022263 413.817663 111.120625 429.251495 136.540167 439.780615 
C 161.95971 450.309734 189.206086 455.729375 216.72 455.729375 
C 244.233914 455.729375 271.48029 450.309734 296.899833 439.780615 
C 322.319375 429.251495 345.417737 413.817663 364.873013 394.362388 
C 384.328288 374.907112 399.76212 351.80875 410.29124 326.389208 
C 420.820359 300.969665 426.24 273.723289 426.24 246.209375 
M 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="QuadMesh_1">
    <path d="M 216.72 246.209375 
L 216.72 246.209375 
L 240 246.209375 
L 216.72 222.929375 
L 216.72 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #e7f6e3; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 246.209375 
L 216.72 246.209375 
L 216.72 269.489375 
L 240 246.209375 
L 216.72 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #76c578; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 246.209375 
L 216.72 246.209375 
L 193.44 246.209375 
L 216.72 269.489375 
L 216.72 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #f7fcf5; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 246.209375 
L 216.72 246.209375 
L 216.72 222.929375 
L 193.44 246.209375 
L 216.72 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #e7f6e3; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 222.929375 
L 240 246.209375 
L 263.28 246.209375 
L 216.72 199.649375 
L 216.72 222.929375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #e0f3db; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 240 246.209375 
L 216.72 269.489375 
L 216.72 292.769375 
L 263.28 246.209375 
L 240 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #5eb96b; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 269.489375 
L 193.44 246.209375 
L 170.16 246.209375 
L 216.72 292.769375 
L 216.72 269.489375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #f1faee; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 193.44 246.209375 
L 216.72 222.929375 
L 216.72 199.649375 
L 170.16 246.209375 
L 193.44 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #e0f3db; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 199.649375 
L 263.28 246.209375 
L 286.56 246.209375 
L 216.72 176.369375 
L 216.72 199.649375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #c4e8bd; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 263.28 246.209375 
L 216.72 292.769375 
L 216.72 316.049375 
L 286.56 246.209375 
L 263.28 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #349d53; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 292.769375 
L 170.16 246.209375 
L 146.88 246.209375 
L 216.72 316.049375 
L 216.72 292.769375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #dbf1d6; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 170.16 246.209375 
L 216.72 199.649375 
L 216.72 176.369375 
L 146.88 246.209375 
L 170.16 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #c4e8bd; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 176.369375 
L 286.56 246.209375 
L 309.84 246.209375 
L 216.72 153.089375 
L 216.72 176.369375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #d2edcc; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 286.56 246.209375 
L 216.72 316.049375 
L 216.72 339.329375 
L 309.84 246.209375 
L 286.56 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #42ab5d; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 316.049375 
L 146.88 246.209375 
L 123.6 246.209375 
L 216.72 339.329375 
L 216.72 316.049375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #e8f6e3; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 146.88 246.209375 
L 216.72 176.369375 
L 216.72 153.089375 
L 123.6 246.209375 
L 146.88 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #d1edcb; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 153.089375 
L 309.84 246.209375 
L 333.12 246.209375 
L 216.72 129.809375 
L 216.72 153.089375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #a3da9d; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 309.84 246.209375 
L 216.72 339.329375 
L 216.72 362.609375 
L 333.12 246.209375 
L 309.84 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #0d7836; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 339.329375 
L 123.6 246.209375 
L 100.32 246.209375 
L 216.72 362.609375 
L 216.72 339.329375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #bce4b5; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 123.6 246.209375 
L 216.72 153.089375 
L 216.72 129.809375 
L 100.32 246.209375 
L 123.6 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #a4da9e; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 129.809375 
L 333.12 246.209375 
L 356.4 246.209375 
L 216.72 106.529375 
L 216.72 129.809375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #c6e8bf; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 333.12 246.209375 
L 216.72 362.609375 
L 216.72 385.889375 
L 356.4 246.209375 
L 333.12 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #329b51; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 362.609375 
L 100.32 246.209375 
L 77.04 246.209375 
L 216.72 385.889375 
L 216.72 362.609375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #d9f0d3; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 100.32 246.209375 
L 216.72 129.809375 
L 216.72 106.529375 
L 77.04 246.209375 
L 100.32 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #c4e8bd; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 106.529375 
L 356.4 246.209375 
L 379.68 246.209375 
L 216.72 83.249375 
L 216.72 106.529375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #b0dfaa; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 356.4 246.209375 
L 216.72 385.889375 
L 216.72 409.169375 
L 379.68 246.209375 
L 356.4 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #1f8742; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 385.889375 
L 77.04 246.209375 
L 53.76 246.209375 
L 216.72 409.169375 
L 216.72 385.889375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #bce4b5; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 77.04 246.209375 
L 216.72 106.529375 
L 216.72 83.249375 
L 53.76 246.209375 
L 77.04 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #afdfa8; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 83.249375 
L 379.68 246.209375 
L 402.96 246.209375 
L 216.72 59.969375 
L 216.72 83.249375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #a7dba0; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 379.68 246.209375 
L 216.72 409.169375 
L 216.72 432.449375 
L 402.96 246.209375 
L 379.68 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #006b2b; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 409.169375 
L 53.76 246.209375 
L 30.48 246.209375 
L 216.72 432.449375 
L 216.72 409.169375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #bbe4b4; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 53.76 246.209375 
L 216.72 83.249375 
L 216.72 59.969375 
L 30.48 246.209375 
L 53.76 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #a7dba0; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 59.969375 
L 402.96 246.209375 
L 426.24 246.209375 
L 216.72 36.689375 
L 216.72 59.969375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #8ace88; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 402.96 246.209375 
L 216.72 432.449375 
L 216.72 455.729375 
L 426.24 246.209375 
L 402.96 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #00441b; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 216.72 432.449375 
L 30.48 246.209375 
L 7.2 246.209375 
L 216.72 455.729375 
L 216.72 432.449375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #afdfa8; stroke: #d3d3d3; stroke-width: 0.5"/>
    <path d="M 30.48 246.209375 
L 216.72 59.969375 
L 216.72 36.689375 
L 7.2 246.209375 
L 30.48 246.209375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: #8ace88; stroke: #d3d3d3; stroke-width: 0.5"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 216.72 246.209375 
L 364.873013 98.056362 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="text_1">
      <!-- AP -->
      <g transform="translate(374.255577 85.482548) scale(0.08 -0.08)">
       <defs>
        <path id="TimesNewRomanPSMT-41" d="M 2928 1419 
L 1288 1419 
L 1000 750 
Q 894 503 894 381 
Q 894 284 986 211 
Q 1078 138 1384 116 
L 1384 0 
L 50 0 
L 50 116 
Q 316 163 394 238 
Q 553 388 747 847 
L 2238 4334 
L 2347 4334 
L 3822 809 
Q 4000 384 4145 257 
Q 4291 131 4550 116 
L 4550 0 
L 2878 0 
L 2878 116 
Q 3131 128 3220 200 
Q 3309 272 3309 375 
Q 3309 513 3184 809 
L 2928 1419 
z
M 2841 1650 
L 2122 3363 
L 1384 1650 
L 2841 1650 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-50" d="M 1313 1984 
L 1313 750 
Q 1313 350 1400 253 
Q 1519 116 1759 116 
L 1922 116 
L 1922 0 
L 106 0 
L 106 116 
L 266 116 
Q 534 116 650 291 
Q 713 388 713 750 
L 713 3488 
Q 713 3888 628 3984 
Q 506 4122 266 4122 
L 106 4122 
L 106 4238 
L 1659 4238 
Q 2228 4238 2556 4120 
Q 2884 4003 3109 3725 
Q 3334 3447 3334 3066 
Q 3334 2547 2992 2222 
Q 2650 1897 2025 1897 
Q 1872 1897 1694 1919 
Q 1516 1941 1313 1984 
z
M 1313 2163 
Q 1478 2131 1606 2115 
Q 1734 2100 1825 2100 
Q 2150 2100 2386 2351 
Q 2622 2603 2622 3003 
Q 2622 3278 2509 3514 
Q 2397 3750 2190 3867 
Q 1984 3984 1722 3984 
Q 1563 3984 1313 3925 
L 1313 2163 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <path d="M 216.72 246.209375 
L 364.873013 394.362388 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="text_2">
      <!-- AP50 -->
      <g transform="translate(370.255577 410.779952) scale(0.08 -0.08)">
       <defs>
        <path id="TimesNewRomanPSMT-35" d="M 2778 4238 
L 2534 3706 
L 1259 3706 
L 981 3138 
Q 1809 3016 2294 2522 
Q 2709 2097 2709 1522 
Q 2709 1188 2573 903 
Q 2438 619 2231 419 
Q 2025 219 1772 97 
Q 1413 -75 1034 -75 
Q 653 -75 479 54 
Q 306 184 306 341 
Q 306 428 378 495 
Q 450 563 559 563 
Q 641 563 702 538 
Q 763 513 909 409 
Q 1144 247 1384 247 
Q 1750 247 2026 523 
Q 2303 800 2303 1197 
Q 2303 1581 2056 1914 
Q 1809 2247 1375 2428 
Q 1034 2569 447 2591 
L 1259 4238 
L 2778 4238 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-30" d="M 231 2094 
Q 231 2819 450 3342 
Q 669 3866 1031 4122 
Q 1313 4325 1613 4325 
Q 2100 4325 2488 3828 
Q 2972 3213 2972 2159 
Q 2972 1422 2759 906 
Q 2547 391 2217 158 
Q 1888 -75 1581 -75 
Q 975 -75 572 641 
Q 231 1244 231 2094 
z
M 844 2016 
Q 844 1141 1059 588 
Q 1238 122 1591 122 
Q 1759 122 1940 273 
Q 2122 425 2216 781 
Q 2359 1319 2359 2297 
Q 2359 3022 2209 3506 
Q 2097 3866 1919 4016 
Q 1791 4119 1609 4119 
Q 1397 4119 1231 3928 
Q 1006 3669 925 3112 
Q 844 2556 844 2016 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(127.832031 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(177.832031 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <path d="M 216.72 246.209375 
L 68.566987 394.362388 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="text_3">
      <!-- AP75 -->
      <g transform="translate(44.958173 410.779952) scale(0.08 -0.08)">
       <defs>
        <path id="TimesNewRomanPSMT-37" d="M 644 4238 
L 2916 4238 
L 2916 4119 
L 1503 -88 
L 1153 -88 
L 2419 3728 
L 1253 3728 
Q 900 3728 750 3644 
Q 488 3500 328 3200 
L 238 3234 
L 644 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(127.832031 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(177.832031 0)"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <path d="M 216.72 246.209375 
L 68.566987 98.056362 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="text_4">
      <!-- API -->
      <g transform="translate(47.626298 85.482548) scale(0.08 -0.08)">
       <defs>
        <path id="TimesNewRomanPSMT-49" d="M 1975 116 
L 1975 0 
L 159 0 
L 159 116 
L 309 116 
Q 572 116 691 269 
Q 766 369 766 750 
L 766 3488 
Q 766 3809 725 3913 
Q 694 3991 597 4047 
Q 459 4122 309 4122 
L 159 4122 
L 159 4238 
L 1975 4238 
L 1975 4122 
L 1822 4122 
Q 1563 4122 1444 3969 
Q 1366 3869 1366 3488 
L 1366 750 
Q 1366 428 1406 325 
Q 1438 247 1538 191 
Q 1672 116 1822 116 
L 1975 116 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-41"/>
       <use xlink:href="#TimesNewRomanPSMT-50" transform="translate(72.216797 0)"/>
       <use xlink:href="#TimesNewRomanPSMT-49" transform="translate(127.832031 0)"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_5">
      <path d="M 216.72 234.569375 
C 218.248551 234.569375 219.762238 234.870466 221.174435 235.455417 
C 222.586632 236.040368 223.869874 236.897803 224.950723 237.978652 
C 226.031572 239.059501 226.889007 240.342743 227.473958 241.75494 
C 228.058909 243.167137 228.36 244.680824 228.36 246.209375 
C 228.36 247.737926 228.058909 249.251613 227.473958 250.66381 
C 226.889007 252.076007 226.031572 253.359249 224.950723 254.440098 
C 223.869874 255.520947 222.586632 256.378382 221.174435 256.963333 
C 219.762238 257.548284 218.248551 257.849375 216.72 257.849375 
C 215.191449 257.849375 213.677762 257.548284 212.265565 256.963333 
C 210.853368 256.378382 209.570126 255.520947 208.489277 254.440098 
C 207.408428 253.359249 206.550993 252.076007 205.966042 250.66381 
C 205.381091 249.251613 205.08 247.737926 205.08 246.209375 
C 205.08 244.680824 205.381091 243.167137 205.966042 241.75494 
C 206.550993 240.342743 207.408428 239.059501 208.489277 237.978652 
C 209.570126 236.897803 210.853368 236.040368 212.265565 235.455417 
C 213.677762 234.870466 215.191449 234.569375 216.72 234.569375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_6">
      <path d="M 216.72 211.289375 
C 221.305652 211.289375 225.846715 212.192649 230.083305 213.947502 
C 234.319896 215.702355 238.169623 218.27466 241.412169 221.517206 
C 244.654715 224.759752 247.22702 228.609479 248.981873 232.84607 
C 250.736726 237.08266 251.64 241.623723 251.64 246.209375 
C 251.64 250.795027 250.736726 255.33609 248.981873 259.57268 
C 247.22702 263.809271 244.654715 267.658998 241.412169 270.901544 
C 238.169623 274.14409 234.319896 276.716395 230.083305 278.471248 
C 225.846715 280.226101 221.305652 281.129375 216.72 281.129375 
C 212.134348 281.129375 207.593285 280.226101 203.356695 278.471248 
C 199.120104 276.716395 195.270377 274.14409 192.027831 270.901544 
C 188.785285 267.658998 186.21298 263.809271 184.458127 259.57268 
C 182.703274 255.33609 181.8 250.795027 181.8 246.209375 
C 181.8 241.623723 182.703274 237.08266 184.458127 232.84607 
C 186.21298 228.609479 188.785285 224.759752 192.027831 221.517206 
C 195.270377 218.27466 199.120104 215.702355 203.356695 213.947502 
C 207.593285 212.192649 212.134348 211.289375 216.72 211.289375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_7">
      <path d="M 216.72 188.009375 
C 224.362754 188.009375 231.931192 189.514831 238.992176 192.439586 
C 246.05316 195.364342 252.469372 199.651517 257.873615 205.05576 
C 263.277858 210.460003 267.565033 216.876215 270.489789 223.937199 
C 273.414544 230.998183 274.92 238.566621 274.92 246.209375 
C 274.92 253.852129 273.414544 261.420567 270.489789 268.481551 
C 267.565033 275.542535 263.277858 281.958747 257.873615 287.36299 
C 252.469372 292.767233 246.05316 297.054408 238.992176 299.979164 
C 231.931192 302.903919 224.362754 304.409375 216.72 304.409375 
C 209.077246 304.409375 201.508808 302.903919 194.447824 299.979164 
C 187.38684 297.054408 180.970628 292.767233 175.566385 287.36299 
C 170.162142 281.958747 165.874967 275.542535 162.950211 268.481551 
C 160.025456 261.420567 158.52 253.852129 158.52 246.209375 
C 158.52 238.566621 160.025456 230.998183 162.950211 223.937199 
C 165.874967 216.876215 170.162142 210.460003 175.566385 205.05576 
C 180.970628 199.651517 187.38684 195.364342 194.447824 192.439586 
C 201.508808 189.514831 209.077246 188.009375 216.72 188.009375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_8">
      <path d="M 216.72 164.729375 
C 227.419856 164.729375 238.015669 166.837013 247.901046 170.931671 
C 257.786424 175.026328 266.76912 181.028374 274.335061 188.594314 
C 281.901001 196.160255 287.903047 205.142951 291.997704 215.028329 
C 296.092362 224.913706 298.2 235.509519 298.2 246.209375 
C 298.2 256.909231 296.092362 267.505044 291.997704 277.390421 
C 287.903047 287.275799 281.901001 296.258495 274.335061 303.824436 
C 266.76912 311.390376 257.786424 317.392422 247.901046 321.487079 
C 238.015669 325.581737 227.419856 327.689375 216.72 327.689375 
C 206.020144 327.689375 195.424331 325.581737 185.538954 321.487079 
C 175.653576 317.392422 166.67088 311.390376 159.104939 303.824436 
C 151.538999 296.258495 145.536953 287.275799 141.442296 277.390421 
C 137.347638 267.505044 135.24 256.909231 135.24 246.209375 
C 135.24 235.509519 137.347638 224.913706 141.442296 215.028329 
C 145.536953 205.142951 151.538999 196.160255 159.104939 188.594314 
C 166.67088 181.028374 175.653576 175.026328 185.538954 170.931671 
C 195.424331 166.837013 206.020144 164.729375 216.72 164.729375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_9">
      <path d="M 216.72 141.449375 
C 230.476957 141.449375 244.100145 144.159196 256.809916 149.423755 
C 269.519688 154.688315 281.068869 162.405231 290.796506 172.132869 
C 300.524144 181.860506 308.24106 193.409687 313.50562 206.119459 
C 318.770179 218.82923 321.48 232.452418 321.48 246.209375 
C 321.48 259.966332 318.770179 273.58952 313.50562 286.299291 
C 308.24106 299.009063 300.524144 310.558244 290.796506 320.285881 
C 281.068869 330.013519 269.519688 337.730435 256.809916 342.994995 
C 244.100145 348.259554 230.476957 350.969375 216.72 350.969375 
C 202.963043 350.969375 189.339855 348.259554 176.630084 342.994995 
C 163.920312 337.730435 152.371131 330.013519 142.643494 320.285881 
C 132.915856 310.558244 125.19894 299.009063 119.93438 286.299291 
C 114.669821 273.58952 111.96 259.966332 111.96 246.209375 
C 111.96 232.452418 114.669821 218.82923 119.93438 206.119459 
C 125.19894 193.409687 132.915856 181.860506 142.643494 172.132869 
C 152.371131 162.405231 163.920312 154.688315 176.630084 149.423755 
C 189.339855 144.159196 202.963043 141.449375 216.72 141.449375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_10">
      <path d="M 216.72 118.169375 
C 233.534059 118.169375 250.184622 121.481378 265.718787 127.91584 
C 281.252951 134.350301 295.368617 143.782088 307.257952 155.671423 
C 319.147287 167.560758 328.579074 181.676424 335.013535 197.210588 
C 341.447997 212.744753 344.76 229.395316 344.76 246.209375 
C 344.76 263.023434 341.447997 279.673997 335.013535 295.208162 
C 328.579074 310.742326 319.147287 324.857992 307.257952 336.747327 
C 295.368617 348.636662 281.252951 358.068449 265.718787 364.50291 
C 250.184622 370.937372 233.534059 374.249375 216.72 374.249375 
C 199.905941 374.249375 183.255378 370.937372 167.721213 364.50291 
C 152.187049 358.068449 138.071383 348.636662 126.182048 336.747327 
C 114.292713 324.857992 104.860926 310.742326 98.426465 295.208162 
C 91.992003 279.673997 88.68 263.023434 88.68 246.209375 
C 88.68 229.395316 91.992003 212.744753 98.426465 197.210588 
C 104.860926 181.676424 114.292713 167.560758 126.182048 155.671423 
C 138.071383 143.782088 152.187049 134.350301 167.721213 127.91584 
C 183.255378 121.481378 199.905941 118.169375 216.72 118.169375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_11">
      <path d="M 216.72 94.889375 
C 236.59116 94.889375 256.269099 98.80356 274.627657 106.407924 
C 292.986215 114.012288 309.668366 125.158945 323.719398 139.209977 
C 337.77043 153.261009 348.917087 169.94316 356.521451 188.301718 
C 364.125815 206.660276 368.04 226.338215 368.04 246.209375 
C 368.04 266.080535 364.125815 285.758474 356.521451 304.117032 
C 348.917087 322.47559 337.77043 339.157741 323.719398 353.208773 
C 309.668366 367.259805 292.986215 378.406462 274.627657 386.010826 
C 256.269099 393.61519 236.59116 397.529375 216.72 397.529375 
C 196.84884 397.529375 177.170901 393.61519 158.812343 386.010826 
C 140.453785 378.406462 123.771634 367.259805 109.720602 353.208773 
C 95.66957 339.157741 84.522913 322.47559 76.918549 304.117032 
C 69.314185 285.758474 65.4 266.080535 65.4 246.209375 
C 65.4 226.338215 69.314185 206.660276 76.918549 188.301718 
C 84.522913 169.94316 95.66957 153.261009 109.720602 139.209977 
C 123.771634 125.158945 140.453785 114.012288 158.812343 106.407924 
C 177.170901 98.80356 196.84884 94.889375 216.72 94.889375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_12">
      <path d="M 216.72 71.609375 
C 239.648262 71.609375 262.353575 76.125743 283.536527 84.900009 
C 304.719479 93.674275 323.968115 106.535802 340.180844 122.748531 
C 356.393573 138.96126 369.2551 158.209896 378.029366 179.392848 
C 386.803632 200.5758 391.32 223.281113 391.32 246.209375 
C 391.32 269.137637 386.803632 291.84295 378.029366 313.025902 
C 369.2551 334.208854 356.393573 353.45749 340.180844 369.670219 
C 323.968115 385.882948 304.719479 398.744475 283.536527 407.518741 
C 262.353575 416.293007 239.648262 420.809375 216.72 420.809375 
C 193.791738 420.809375 171.086425 416.293007 149.903473 407.518741 
C 128.720521 398.744475 109.471885 385.882948 93.259156 369.670219 
C 77.046427 353.45749 64.1849 334.208854 55.410634 313.025902 
C 46.636368 291.84295 42.12 269.137637 42.12 246.209375 
C 42.12 223.281113 46.636368 200.5758 55.410634 179.392848 
C 64.1849 158.209896 77.046427 138.96126 93.259156 122.748531 
C 109.471885 106.535802 128.720521 93.674275 149.903473 84.900009 
C 171.086425 76.125743 193.791738 71.609375 216.72 71.609375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_13">
      <path d="M 216.72 48.329375 
C 242.705364 48.329375 268.438052 53.447925 292.445398 63.392093 
C 316.452743 73.336261 338.267863 87.912658 356.64229 106.287085 
C 375.016717 124.661512 389.593114 146.476632 399.537282 170.483977 
C 409.48145 194.491323 414.6 220.224011 414.6 246.209375 
C 414.6 272.194739 409.48145 297.927427 399.537282 321.934773 
C 389.593114 345.942118 375.016717 367.757238 356.64229 386.131665 
C 338.267863 404.506092 316.452743 419.082489 292.445398 429.026657 
C 268.438052 438.970825 242.705364 444.089375 216.72 444.089375 
C 190.734636 444.089375 165.001948 438.970825 140.994602 429.026657 
C 116.987257 419.082489 95.172137 404.506092 76.79771 386.131665 
C 58.423283 367.757238 43.846886 345.942118 33.902718 321.934773 
C 23.95855 297.927427 18.84 272.194739 18.84 246.209375 
C 18.84 220.224011 23.95855 194.491323 33.902718 170.483977 
C 43.846886 146.476632 58.423283 124.661512 76.79771 106.287085 
C 95.172137 87.912658 116.987257 73.336261 140.994602 63.392093 
C 165.001948 53.447925 190.734636 48.329375 216.72 48.329375 
" clip-path="url(#pcc2cf0ad3a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.7; stroke-width: 0.8"/>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 216.72 36.689375 
C 189.206086 36.689375 161.95971 42.109016 136.540167 52.638135 
C 111.120625 63.167255 88.022263 78.601087 68.566987 98.056362 
C 49.111712 117.511638 33.67788 140.61 23.14876 166.029542 
C 12.619641 191.449085 7.2 218.695461 7.2 246.209375 
C 7.2 273.723289 12.619641 300.969665 23.14876 326.389208 
C 33.67788 351.80875 49.111712 374.907112 68.566987 394.362388 
C 88.022263 413.817663 111.120625 429.251495 136.540167 439.780615 
C 161.95971 450.309734 189.206086 455.729375 216.72 455.729375 
C 244.233914 455.729375 271.48029 450.309734 296.899833 439.780615 
C 322.319375 429.251495 345.417737 413.817663 364.873013 394.362388 
C 384.328288 374.907112 399.76212 351.80875 410.29124 326.389208 
C 420.820359 300.969665 426.24 273.723289 426.24 246.209375 
C 426.24 218.695461 420.820359 191.449085 410.29124 166.029542 
C 399.76212 140.61 384.328288 117.511638 364.873013 98.056362 
C 345.417737 78.601087 322.319375 63.167255 296.899833 52.638135 
C 271.48029 42.109016 244.233914 36.689375 216.72 36.689375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_5">
    <!-- 11.4 -->
    <g transform="translate(219.813223 239.420058) scale(0.06 -0.06)">
     <defs>
      <path id="TimesNewRomanPSMT-31" d="M 750 3822 
L 1781 4325 
L 1884 4325 
L 1884 747 
Q 1884 391 1914 303 
Q 1944 216 2037 169 
Q 2131 122 2419 116 
L 2419 0 
L 825 0 
L 825 116 
Q 1125 122 1212 167 
Q 1300 213 1334 289 
Q 1369 366 1369 747 
L 1369 3034 
Q 1369 3497 1338 3628 
Q 1316 3728 1258 3775 
Q 1200 3822 1119 3822 
Q 1003 3822 797 3725 
L 750 3822 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-2e" d="M 800 606 
Q 947 606 1047 504 
Q 1147 403 1147 259 
Q 1147 116 1045 14 
Q 944 -88 800 -88 
Q 656 -88 554 14 
Q 453 116 453 259 
Q 453 406 554 506 
Q 656 606 800 606 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-34" d="M 2978 1563 
L 2978 1119 
L 2409 1119 
L 2409 0 
L 1894 0 
L 1894 1119 
L 100 1119 
L 100 1519 
L 2066 4325 
L 2409 4325 
L 2409 1563 
L 2978 1563 
z
M 1894 1563 
L 1894 3666 
L 406 1563 
L 1894 1563 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(46.25 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(96.25 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(121.25 0)"/>
    </g>
   </g>
   <g id="text_6">
    <!-- 13.8 -->
    <g transform="translate(236.162169 222.958612) scale(0.06 -0.06)">
     <defs>
      <path id="TimesNewRomanPSMT-33" d="M 325 3431 
Q 506 3859 782 4092 
Q 1059 4325 1472 4325 
Q 1981 4325 2253 3994 
Q 2459 3747 2459 3466 
Q 2459 3003 1878 2509 
Q 2269 2356 2469 2072 
Q 2669 1788 2669 1403 
Q 2669 853 2319 450 
Q 1863 -75 997 -75 
Q 569 -75 414 31 
Q 259 138 259 259 
Q 259 350 332 419 
Q 406 488 509 488 
Q 588 488 669 463 
Q 722 447 909 348 
Q 1097 250 1169 231 
Q 1284 197 1416 197 
Q 1734 197 1970 444 
Q 2206 691 2206 1028 
Q 2206 1275 2097 1509 
Q 2016 1684 1919 1775 
Q 1784 1900 1550 2001 
Q 1316 2103 1072 2103 
L 972 2103 
L 972 2197 
Q 1219 2228 1467 2375 
Q 1716 2522 1828 2728 
Q 1941 2934 1941 3181 
Q 1941 3503 1739 3701 
Q 1538 3900 1238 3900 
Q 753 3900 428 3381 
L 325 3431 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-38" d="M 1228 2134 
Q 725 2547 579 2797 
Q 434 3047 434 3316 
Q 434 3728 753 4026 
Q 1072 4325 1600 4325 
Q 2113 4325 2425 4047 
Q 2738 3769 2738 3413 
Q 2738 3175 2569 2928 
Q 2400 2681 1866 2347 
Q 2416 1922 2594 1678 
Q 2831 1359 2831 1006 
Q 2831 559 2490 242 
Q 2150 -75 1597 -75 
Q 994 -75 656 303 
Q 388 606 388 966 
Q 388 1247 577 1523 
Q 766 1800 1228 2134 
z
M 1719 2469 
Q 2094 2806 2194 3001 
Q 2294 3197 2294 3444 
Q 2294 3772 2109 3958 
Q 1925 4144 1606 4144 
Q 1288 4144 1088 3959 
Q 888 3775 888 3528 
Q 888 3366 970 3203 
Q 1053 3041 1206 2894 
L 1719 2469 
z
M 1375 2016 
Q 1116 1797 991 1539 
Q 866 1281 866 981 
Q 866 578 1086 336 
Q 1306 94 1647 94 
Q 1984 94 2187 284 
Q 2391 475 2391 747 
Q 2391 972 2272 1150 
Q 2050 1481 1375 2016 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-38" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_7">
    <!-- 22.1 -->
    <g transform="translate(252.623615 206.497167) scale(0.06 -0.06)">
     <defs>
      <path id="TimesNewRomanPSMT-32" d="M 2934 816 
L 2638 0 
L 138 0 
L 138 116 
Q 1241 1122 1691 1759 
Q 2141 2397 2141 2925 
Q 2141 3328 1894 3587 
Q 1647 3847 1303 3847 
Q 991 3847 742 3664 
Q 494 3481 375 3128 
L 259 3128 
Q 338 3706 661 4015 
Q 984 4325 1469 4325 
Q 1984 4325 2329 3994 
Q 2675 3663 2675 3213 
Q 2675 2891 2525 2569 
Q 2294 2063 1775 1497 
Q 997 647 803 472 
L 1909 472 
Q 2247 472 2383 497 
Q 2519 522 2628 598 
Q 2738 675 2819 816 
L 2934 816 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-32" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_8">
    <!-- 18.3 -->
    <g transform="translate(269.085061 190.035721) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-38" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_9">
    <!-- 30.2 -->
    <g transform="translate(285.546506 173.574275) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-33"/>
     <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-32" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_10">
    <!-- 22.0 -->
    <g transform="translate(302.007952 157.112829) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-32" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_11">
    <!-- 27.1 -->
    <g transform="translate(318.469398 140.651383) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_12">
    <!-- 29.3 -->
    <g transform="translate(334.930844 124.189937) scale(0.06 -0.06)">
     <defs>
      <path id="TimesNewRomanPSMT-39" d="M 338 -88 
L 338 28 
Q 744 34 1094 217 
Q 1444 400 1770 856 
Q 2097 1313 2225 1859 
Q 1734 1544 1338 1544 
Q 891 1544 572 1889 
Q 253 2234 253 2806 
Q 253 3363 572 3797 
Q 956 4325 1575 4325 
Q 2097 4325 2469 3894 
Q 2925 3359 2925 2575 
Q 2925 1869 2578 1258 
Q 2231 647 1613 244 
Q 1109 -88 516 -88 
L 338 -88 
z
M 2275 2091 
Q 2331 2497 2331 2741 
Q 2331 3044 2228 3395 
Q 2125 3747 1936 3934 
Q 1747 4122 1506 4122 
Q 1228 4122 1018 3872 
Q 809 3622 809 3128 
Q 809 2469 1088 2097 
Q 1291 1828 1588 1828 
Q 1731 1828 1928 1897 
Q 2125 1966 2275 2091 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-39" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_13">
    <!-- 35.4 -->
    <g transform="translate(351.39229 107.728491) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-33"/>
     <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_14">
    <!-- 39.6 -->
    <g transform="translate(219.700723 255.881504) scale(0.06 -0.06)">
     <defs>
      <path id="TimesNewRomanPSMT-36" d="M 2869 4325 
L 2869 4209 
Q 2456 4169 2195 4045 
Q 1934 3922 1679 3669 
Q 1425 3416 1258 3105 
Q 1091 2794 978 2366 
Q 1428 2675 1881 2675 
Q 2316 2675 2634 2325 
Q 2953 1975 2953 1425 
Q 2953 894 2631 456 
Q 2244 -75 1606 -75 
Q 1172 -75 869 213 
Q 275 772 275 1663 
Q 275 2231 503 2743 
Q 731 3256 1154 3653 
Q 1578 4050 1965 4187 
Q 2353 4325 2688 4325 
L 2869 4325 
z
M 925 2138 
Q 869 1716 869 1456 
Q 869 1156 980 804 
Q 1091 453 1309 247 
Q 1469 100 1697 100 
Q 1969 100 2183 356 
Q 2397 613 2397 1088 
Q 2397 1622 2184 2012 
Q 1972 2403 1581 2403 
Q 1463 2403 1327 2353 
Q 1191 2303 925 2138 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-33"/>
     <use xlink:href="#TimesNewRomanPSMT-39" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-36" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_15">
    <!-- 43.8 -->
    <g transform="translate(236.162169 272.34295) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-34"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-38" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_16">
    <!-- 53.0 -->
    <g transform="translate(252.623615 288.804396) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-35"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_17">
    <!-- 48.9 -->
    <g transform="translate(269.085061 305.265842) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-34"/>
     <use xlink:href="#TimesNewRomanPSMT-38" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-39" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_18">
    <!-- 63.9 -->
    <g transform="translate(285.546506 321.727288) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-36"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-39" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_19">
    <!-- 53.5 -->
    <g transform="translate(302.007952 338.188734) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-35"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_20">
    <!-- 59.4 -->
    <g transform="translate(318.469398 354.650179) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-35"/>
     <use xlink:href="#TimesNewRomanPSMT-39" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_21">
    <!-- 67.8 -->
    <g transform="translate(334.930844 371.111625) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-36"/>
     <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-38" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_22">
    <!-- 76.7 -->
    <g transform="translate(351.39229 387.573071) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-37"/>
     <use xlink:href="#TimesNewRomanPSMT-36" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_23">
    <!-- 3.1 -->
    <g transform="translate(204.739277 255.881504) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-33"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(75 0)"/>
    </g>
   </g>
   <g id="text_24">
    <!-- 6.2 -->
    <g transform="translate(188.277831 272.34295) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-36"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-32" transform="translate(75 0)"/>
    </g>
   </g>
   <g id="text_25">
    <!-- 15.3 -->
    <g transform="translate(170.316385 288.804396) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_26">
    <!-- 11.0 -->
    <g transform="translate(153.967439 305.265842) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(46.25 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(96.25 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(121.25 0)"/>
    </g>
   </g>
   <g id="text_27">
    <!-- 24.1 -->
    <g transform="translate(137.393494 321.727288) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_28">
    <!-- 16.3 -->
    <g transform="translate(120.932048 338.188734) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-36" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_29">
    <!-- 24.3 -->
    <g transform="translate(104.470602 354.650179) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_30">
    <!-- 24.6 -->
    <g transform="translate(88.009156 371.111625) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-36" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_31">
    <!-- 27.5 -->
    <g transform="translate(71.54771 387.573071) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_32">
    <!-- 11.4 -->
    <g transform="translate(203.351777 239.420058) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(46.25 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(96.25 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(121.25 0)"/>
    </g>
   </g>
   <g id="text_33">
    <!-- 13.9 -->
    <g transform="translate(186.777831 222.958612) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-39" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_34">
    <!-- 22.2 -->
    <g transform="translate(170.316385 206.497167) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-32" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-32" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_35">
    <!-- 18.4 -->
    <g transform="translate(153.854939 190.035721) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-31"/>
     <use xlink:href="#TimesNewRomanPSMT-38" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_36">
    <!-- 30.1 -->
    <g transform="translate(137.393494 173.574275) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-33"/>
     <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-31" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_37">
    <!-- 22.3 -->
    <g transform="translate(120.932048 157.112829) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-32" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_38">
    <!-- 27.3 -->
    <g transform="translate(104.470602 140.651383) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-37" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-33" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_39">
    <!-- 29.5 -->
    <g transform="translate(88.009156 124.189937) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-32"/>
     <use xlink:href="#TimesNewRomanPSMT-39" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_40">
    <!-- 35.4 -->
    <g transform="translate(71.54771 107.728491) scale(0.06 -0.06)">
     <use xlink:href="#TimesNewRomanPSMT-33"/>
     <use xlink:href="#TimesNewRomanPSMT-35" transform="translate(50 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-2e" transform="translate(100 0)"/>
     <use xlink:href="#TimesNewRomanPSMT-34" transform="translate(125 0)"/>
    </g>
   </g>
   <g id="text_41">
    <!-- Model Performance (Segmented Radial Heatmap) -->
    <g transform="translate(67.034844 16.689375) scale(0.14 -0.14)">
     <defs>
      <path id="TimesNewRomanPS-BoldMT-4d" d="M 3050 1444 
L 4200 4238 
L 5925 4238 
L 5925 4122 
L 5788 4122 
Q 5600 4122 5488 4056 
Q 5409 4013 5363 3909 
Q 5328 3834 5328 3519 
L 5328 725 
Q 5328 400 5362 319 
Q 5397 238 5503 177 
Q 5609 116 5788 116 
L 5925 116 
L 5925 0 
L 3713 0 
L 3713 116 
L 3850 116 
Q 4038 116 4150 181 
Q 4228 225 4275 331 
Q 4309 406 4309 725 
L 4309 3866 
L 2684 0 
L 2609 0 
L 959 3838 
L 959 853 
Q 959 541 975 469 
Q 1016 313 1152 214 
Q 1288 116 1578 116 
L 1578 0 
L 128 0 
L 128 116 
L 172 116 
Q 313 113 434 161 
Q 556 209 618 290 
Q 681 372 716 519 
Q 722 553 722 838 
L 722 3519 
Q 722 3841 687 3920 
Q 653 4000 547 4061 
Q 441 4122 263 4122 
L 128 4122 
L 128 4238 
L 1859 4238 
L 3050 1444 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-6f" d="M 1594 3009 
Q 1975 3009 2303 2812 
Q 2631 2616 2801 2253 
Q 2972 1891 2972 1459 
Q 2972 838 2656 419 
Q 2275 -88 1603 -88 
Q 944 -88 587 375 
Q 231 838 231 1447 
Q 231 2075 595 2542 
Q 959 3009 1594 3009 
z
M 1606 2788 
Q 1447 2788 1333 2667 
Q 1219 2547 1183 2192 
Q 1147 1838 1147 1206 
Q 1147 872 1191 581 
Q 1225 359 1337 243 
Q 1450 128 1594 128 
Q 1734 128 1828 206 
Q 1950 313 1991 503 
Q 2053 800 2053 1703 
Q 2053 2234 1993 2432 
Q 1934 2631 1819 2722 
Q 1738 2788 1606 2788 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-64" d="M 3056 4238 
L 3056 875 
Q 3056 534 3075 472 
Q 3100 369 3170 319 
Q 3241 269 3416 256 
L 3416 153 
L 2181 -88 
L 2181 375 
Q 1959 106 1786 9 
Q 1613 -88 1394 -88 
Q 834 -88 509 413 
Q 247 819 247 1409 
Q 247 1881 409 2254 
Q 572 2628 855 2818 
Q 1138 3009 1463 3009 
Q 1672 3009 1834 2928 
Q 1997 2847 2181 2644 
L 2181 3525 
Q 2181 3859 2153 3928 
Q 2116 4019 2041 4062 
Q 1966 4106 1759 4106 
L 1759 4238 
L 3056 4238 
z
M 2181 2256 
Q 1950 2700 1616 2700 
Q 1500 2700 1425 2638 
Q 1309 2541 1236 2297 
Q 1163 2053 1163 1550 
Q 1163 997 1244 731 
Q 1325 466 1466 347 
Q 1538 288 1663 288 
Q 1938 288 2181 719 
L 2181 2256 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-65" d="M 2691 1566 
L 1088 1566 
Q 1116 984 1397 647 
Q 1613 388 1916 388 
Q 2103 388 2256 492 
Q 2409 597 2584 869 
L 2691 800 
Q 2453 316 2165 114 
Q 1878 -88 1500 -88 
Q 850 -88 516 413 
Q 247 816 247 1413 
Q 247 2144 642 2576 
Q 1038 3009 1569 3009 
Q 2013 3009 2339 2645 
Q 2666 2281 2691 1566 
z
M 1922 1775 
Q 1922 2278 1867 2465 
Q 1813 2653 1697 2750 
Q 1631 2806 1522 2806 
Q 1359 2806 1256 2647 
Q 1072 2369 1072 1884 
L 1072 1775 
L 1922 1775 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-6c" d="M 1359 4238 
L 1359 606 
Q 1359 297 1431 211 
Q 1503 125 1713 113 
L 1713 0 
L 134 0 
L 134 113 
Q 328 119 422 225 
Q 484 297 484 606 
L 484 3631 
Q 484 3938 412 4023 
Q 341 4109 134 4122 
L 134 4238 
L 1359 4238 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-20" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-50" d="M 1728 1919 
L 1728 744 
Q 1728 400 1770 311 
Q 1813 222 1920 169 
Q 2028 116 2316 116 
L 2316 0 
L 163 0 
L 163 116 
Q 456 116 561 170 
Q 666 225 708 312 
Q 750 400 750 744 
L 750 3494 
Q 750 3838 708 3927 
Q 666 4016 559 4069 
Q 453 4122 163 4122 
L 163 4238 
L 2009 4238 
Q 2922 4238 3319 3913 
Q 3716 3588 3716 3100 
Q 3716 2688 3459 2394 
Q 3203 2100 2753 1994 
Q 2450 1919 1728 1919 
z
M 1728 3994 
L 1728 2163 
Q 1831 2156 1884 2156 
Q 2266 2156 2472 2382 
Q 2678 2609 2678 3084 
Q 2678 3556 2472 3775 
Q 2266 3994 1856 3994 
L 1728 3994 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-72" d="M 1428 2922 
L 1428 2259 
Q 1719 2713 1937 2861 
Q 2156 3009 2359 3009 
Q 2534 3009 2639 2901 
Q 2744 2794 2744 2597 
Q 2744 2388 2642 2272 
Q 2541 2156 2397 2156 
Q 2231 2156 2109 2262 
Q 1988 2369 1966 2381 
Q 1934 2400 1894 2400 
Q 1803 2400 1722 2331 
Q 1594 2225 1528 2028 
Q 1428 1725 1428 1359 
L 1428 688 
L 1431 513 
Q 1431 334 1453 284 
Q 1491 200 1564 161 
Q 1638 122 1813 113 
L 1813 0 
L 234 0 
L 234 113 
Q 425 128 492 217 
Q 559 306 559 688 
L 559 2303 
Q 559 2553 534 2622 
Q 503 2709 443 2750 
Q 384 2791 234 2806 
L 234 2922 
L 1428 2922 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-66" d="M 1497 2616 
L 1497 588 
Q 1497 294 1559 219 
Q 1659 103 1928 113 
L 1928 0 
L 206 0 
L 206 113 
Q 403 116 486 158 
Q 569 200 600 275 
Q 631 350 631 588 
L 631 2616 
L 206 2616 
L 206 2922 
L 631 2922 
L 631 3138 
L 628 3284 
Q 628 3734 958 4034 
Q 1288 4334 1850 4334 
Q 2234 4334 2420 4190 
Q 2606 4047 2606 3872 
Q 2606 3731 2490 3628 
Q 2375 3525 2184 3525 
Q 2022 3525 1926 3609 
Q 1831 3694 1831 3797 
Q 1831 3825 1850 3913 
Q 1863 3966 1863 4013 
Q 1863 4078 1825 4109 
Q 1775 4156 1703 4156 
Q 1613 4156 1552 4081 
Q 1491 4006 1491 3841 
L 1497 3294 
L 1497 2922 
L 1928 2922 
L 1928 2616 
L 1497 2616 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-6d" d="M 1428 2922 
L 1428 2538 
Q 1669 2800 1873 2904 
Q 2078 3009 2322 3009 
Q 2603 3009 2793 2878 
Q 2984 2747 3088 2478 
Q 3338 2759 3567 2884 
Q 3797 3009 4047 3009 
Q 4350 3009 4529 2870 
Q 4709 2731 4779 2517 
Q 4850 2303 4850 1834 
L 4850 638 
Q 4850 300 4911 219 
Q 4972 138 5166 113 
L 5166 0 
L 3656 0 
L 3656 113 
Q 3834 128 3919 250 
Q 3975 334 3975 638 
L 3975 1894 
Q 3975 2284 3944 2390 
Q 3913 2497 3845 2548 
Q 3778 2600 3688 2600 
Q 3553 2600 3415 2501 
Q 3278 2403 3138 2206 
L 3138 638 
Q 3138 322 3191 241 
Q 3263 125 3466 113 
L 3466 0 
L 1953 0 
L 1953 113 
Q 2075 119 2145 173 
Q 2216 228 2239 304 
Q 2263 381 2263 638 
L 2263 1894 
Q 2263 2291 2231 2391 
Q 2200 2491 2126 2547 
Q 2053 2603 1969 2603 
Q 1844 2603 1741 2538 
Q 1594 2441 1428 2206 
L 1428 638 
Q 1428 328 1489 229 
Q 1550 131 1744 113 
L 1744 0 
L 238 0 
L 238 113 
Q 422 131 500 231 
Q 553 300 553 638 
L 553 2288 
Q 553 2619 492 2700 
Q 431 2781 238 2806 
L 238 2922 
L 1428 2922 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-61" d="M 1828 431 
Q 1294 -41 869 -41 
Q 619 -41 453 123 
Q 288 288 288 534 
Q 288 869 575 1136 
Q 863 1403 1828 1847 
L 1828 2141 
Q 1828 2472 1792 2558 
Q 1756 2644 1656 2708 
Q 1556 2772 1431 2772 
Q 1228 2772 1097 2681 
Q 1016 2625 1016 2550 
Q 1016 2484 1103 2388 
Q 1222 2253 1222 2128 
Q 1222 1975 1108 1867 
Q 994 1759 809 1759 
Q 613 1759 480 1878 
Q 347 1997 347 2156 
Q 347 2381 525 2586 
Q 703 2791 1022 2900 
Q 1341 3009 1684 3009 
Q 2100 3009 2342 2832 
Q 2584 2656 2656 2450 
Q 2700 2319 2700 1847 
L 2700 713 
Q 2700 513 2715 461 
Q 2731 409 2762 384 
Q 2794 359 2834 359 
Q 2916 359 3000 475 
L 3094 400 
Q 2938 169 2770 64 
Q 2603 -41 2391 -41 
Q 2141 -41 2000 76 
Q 1859 194 1828 431 
z
M 1828 659 
L 1828 1638 
Q 1450 1416 1266 1163 
Q 1144 994 1144 822 
Q 1144 678 1247 569 
Q 1325 484 1466 484 
Q 1622 484 1828 659 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-6e" d="M 1409 2922 
L 1409 2544 
Q 1634 2788 1840 2898 
Q 2047 3009 2284 3009 
Q 2569 3009 2756 2851 
Q 2944 2694 3006 2459 
Q 3056 2281 3056 1775 
L 3056 638 
Q 3056 300 3117 217 
Q 3178 134 3372 113 
L 3372 0 
L 1894 0 
L 1894 113 
Q 2059 134 2131 250 
Q 2181 328 2181 638 
L 2181 1938 
Q 2181 2297 2153 2390 
Q 2125 2484 2058 2536 
Q 1991 2588 1909 2588 
Q 1641 2588 1409 2203 
L 1409 638 
Q 1409 309 1470 221 
Q 1531 134 1697 113 
L 1697 0 
L 219 0 
L 219 113 
Q 403 131 481 231 
Q 534 300 534 638 
L 534 2288 
Q 534 2619 473 2700 
Q 413 2781 219 2806 
L 219 2922 
L 1409 2922 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-63" d="M 2600 753 
L 2697 678 
Q 2491 291 2183 101 
Q 1875 -88 1528 -88 
Q 944 -88 594 353 
Q 244 794 244 1413 
Q 244 2009 563 2463 
Q 947 3009 1622 3009 
Q 2075 3009 2342 2781 
Q 2609 2553 2609 2272 
Q 2609 2094 2501 1987 
Q 2394 1881 2219 1881 
Q 2034 1881 1914 2003 
Q 1794 2125 1766 2438 
Q 1747 2634 1675 2713 
Q 1603 2791 1506 2791 
Q 1356 2791 1250 2631 
Q 1088 2391 1088 1894 
Q 1088 1481 1219 1104 
Q 1350 728 1578 544 
Q 1750 409 1984 409 
Q 2138 409 2275 481 
Q 2413 553 2600 753 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-28" d="M 2056 4334 
L 2056 4191 
Q 1731 3978 1578 3775 
Q 1366 3494 1247 3053 
Q 1097 2506 1097 1525 
Q 1097 600 1233 87 
Q 1369 -425 1603 -741 
Q 1766 -959 2056 -1125 
L 2056 -1281 
Q 1303 -1028 779 -248 
Q 256 531 256 1544 
Q 256 2544 778 3314 
Q 1300 4084 2056 4334 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-53" d="M 3006 4334 
L 3041 2922 
L 2913 2922 
Q 2822 3453 2467 3776 
Q 2113 4100 1700 4100 
Q 1381 4100 1195 3929 
Q 1009 3759 1009 3538 
Q 1009 3397 1075 3288 
Q 1166 3141 1366 2997 
Q 1513 2894 2044 2631 
Q 2788 2266 3047 1941 
Q 3303 1616 3303 1197 
Q 3303 666 2889 283 
Q 2475 -100 1838 -100 
Q 1638 -100 1459 -59 
Q 1281 -19 1013 94 
Q 863 156 766 156 
Q 684 156 593 93 
Q 503 31 447 -97 
L 331 -97 
L 331 1503 
L 447 1503 
Q 584 828 976 473 
Q 1369 119 1822 119 
Q 2172 119 2380 309 
Q 2588 500 2588 753 
Q 2588 903 2508 1043 
Q 2428 1184 2265 1311 
Q 2103 1438 1691 1641 
Q 1113 1925 859 2125 
Q 606 2325 470 2572 
Q 334 2819 334 3116 
Q 334 3622 706 3978 
Q 1078 4334 1644 4334 
Q 1850 4334 2044 4284 
Q 2191 4247 2402 4145 
Q 2613 4044 2697 4044 
Q 2778 4044 2825 4094 
Q 2872 4144 2913 4334 
L 3006 4334 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-67" d="M 2091 2888 
L 3075 2888 
L 3075 2544 
L 2516 2544 
Q 2669 2391 2728 2266 
Q 2803 2097 2803 1913 
Q 2803 1600 2629 1370 
Q 2456 1141 2157 1011 
Q 1859 881 1628 881 
Q 1609 881 1253 897 
Q 1113 897 1017 805 
Q 922 713 922 584 
Q 922 472 1008 400 
Q 1094 328 1288 328 
L 1828 334 
Q 2484 334 2731 191 
Q 3084 -9 3084 -425 
Q 3084 -691 2921 -908 
Q 2759 -1125 2494 -1228 
Q 2091 -1381 1566 -1381 
Q 1172 -1381 847 -1304 
Q 522 -1228 381 -1092 
Q 241 -956 241 -806 
Q 241 -663 348 -548 
Q 456 -434 750 -356 
Q 344 -156 344 200 
Q 344 416 509 619 
Q 675 822 1034 972 
Q 616 1125 428 1375 
Q 241 1625 241 1953 
Q 241 2381 591 2695 
Q 941 3009 1491 3009 
Q 1781 3009 2091 2888 
z
M 1550 2816 
Q 1369 2816 1244 2633 
Q 1119 2450 1119 1875 
Q 1119 1409 1242 1236 
Q 1366 1063 1534 1063 
Q 1722 1063 1847 1234 
Q 1972 1406 1972 1903 
Q 1972 2463 1831 2666 
Q 1731 2816 1550 2816 
z
M 1366 -431 
Q 1047 -431 944 -484 
Q 766 -581 766 -747 
Q 766 -906 950 -1036 
Q 1134 -1166 1644 -1166 
Q 2078 -1166 2326 -1050 
Q 2575 -934 2575 -725 
Q 2575 -647 2528 -594 
Q 2444 -500 2273 -465 
Q 2103 -431 1366 -431 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-74" d="M 1375 3991 
L 1375 2922 
L 2069 2922 
L 2069 2613 
L 1375 2613 
L 1375 809 
Q 1375 556 1398 482 
Q 1422 409 1481 364 
Q 1541 319 1591 319 
Q 1794 319 1975 628 
L 2069 559 
Q 1816 -41 1247 -41 
Q 969 -41 776 114 
Q 584 269 531 459 
Q 500 566 500 1034 
L 500 2613 
L 119 2613 
L 119 2722 
Q 513 3000 789 3306 
Q 1066 3613 1272 3991 
L 1375 3991 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-52" d="M 1700 1931 
L 1700 744 
Q 1700 400 1742 311 
Q 1784 222 1890 169 
Q 1997 116 2288 116 
L 2288 0 
L 119 0 
L 119 116 
Q 413 116 517 170 
Q 622 225 664 312 
Q 706 400 706 744 
L 706 3494 
Q 706 3838 664 3927 
Q 622 4016 515 4069 
Q 409 4122 119 4122 
L 119 4238 
L 2088 4238 
Q 2856 4238 3212 4131 
Q 3569 4025 3794 3739 
Q 4019 3453 4019 3069 
Q 4019 2600 3681 2294 
Q 3466 2100 3078 2003 
L 4097 569 
Q 4297 291 4381 222 
Q 4509 125 4678 116 
L 4678 0 
L 3344 0 
L 1978 1931 
L 1700 1931 
z
M 1700 4009 
L 1700 2153 
L 1878 2153 
Q 2313 2153 2528 2233 
Q 2744 2313 2867 2520 
Q 2991 2728 2991 3063 
Q 2991 3547 2764 3778 
Q 2538 4009 2034 4009 
L 1700 4009 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-69" d="M 919 4338 
Q 1122 4338 1262 4195 
Q 1403 4053 1403 3853 
Q 1403 3653 1261 3512 
Q 1119 3372 919 3372 
Q 719 3372 578 3512 
Q 438 3653 438 3853 
Q 438 4053 578 4195 
Q 719 4338 919 4338 
z
M 1356 2922 
L 1356 606 
Q 1356 297 1428 211 
Q 1500 125 1709 113 
L 1709 0 
L 131 0 
L 131 113 
Q 325 119 419 225 
Q 481 297 481 606 
L 481 2313 
Q 481 2622 409 2708 
Q 338 2794 131 2806 
L 131 2922 
L 1356 2922 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-48" d="M 1750 2038 
L 1750 725 
Q 1750 400 1786 319 
Q 1822 238 1926 177 
Q 2031 116 2209 116 
L 2350 116 
L 2350 0 
L 134 0 
L 134 116 
L 275 116 
Q 459 116 572 181 
Q 653 225 697 331 
Q 731 406 731 725 
L 731 3513 
Q 731 3838 697 3919 
Q 663 4000 558 4061 
Q 453 4122 275 4122 
L 134 4122 
L 134 4238 
L 2350 4238 
L 2350 4122 
L 2209 4122 
Q 2025 4122 1913 4056 
Q 1831 4013 1784 3906 
Q 1750 3831 1750 3513 
L 1750 2313 
L 3247 2313 
L 3247 3513 
Q 3247 3838 3212 3919 
Q 3178 4000 3072 4061 
Q 2966 4122 2788 4122 
L 2650 4122 
L 2650 4238 
L 4863 4238 
L 4863 4122 
L 4725 4122 
Q 4538 4122 4428 4056 
Q 4347 4013 4300 3906 
Q 4266 3831 4266 3513 
L 4266 725 
Q 4266 400 4300 319 
Q 4334 238 4440 177 
Q 4547 116 4725 116 
L 4863 116 
L 4863 0 
L 2650 0 
L 2650 116 
L 2788 116 
Q 2975 116 3084 181 
Q 3166 225 3213 331 
Q 3247 406 3247 725 
L 3247 2038 
L 1750 2038 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-70" d="M 1347 297 
L 1347 -791 
Q 1347 -1016 1383 -1097 
Q 1419 -1178 1494 -1215 
Q 1569 -1253 1788 -1253 
L 1788 -1369 
L 122 -1369 
L 122 -1253 
Q 316 -1247 409 -1144 
Q 472 -1072 472 -772 
L 472 2313 
Q 472 2622 400 2708 
Q 328 2794 122 2806 
L 122 2922 
L 1347 2922 
L 1347 2538 
Q 1500 2763 1659 2863 
Q 1888 3009 2156 3009 
Q 2478 3009 2742 2806 
Q 3006 2603 3143 2245 
Q 3281 1888 3281 1475 
Q 3281 1031 3139 664 
Q 2997 297 2726 104 
Q 2456 -88 2125 -88 
Q 1884 -88 1675 19 
Q 1519 100 1347 297 
z
M 1347 613 
Q 1616 231 1922 231 
Q 2091 231 2200 409 
Q 2363 672 2363 1409 
Q 2363 2166 2184 2444 
Q 2066 2628 1866 2628 
Q 1550 2628 1347 2172 
L 1347 613 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPS-BoldMT-29" d="M 78 -1281 
L 78 -1138 
Q 403 -922 556 -719 
Q 766 -438 888 0 
Q 1038 550 1038 1531 
Q 1038 2456 902 2967 
Q 766 3478 531 3794 
Q 369 4013 78 4178 
L 78 4334 
Q 831 4081 1354 3301 
Q 1878 2522 1878 1509 
Q 1878 513 1354 -257 
Q 831 -1028 78 -1281 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPS-BoldMT-4d"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6f" transform="translate(94.384766 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-64" transform="translate(144.384766 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(200 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6c" transform="translate(244.384766 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-20" transform="translate(272.167969 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-50" transform="translate(297.167969 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(358.251953 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-72" transform="translate(402.636719 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-66" transform="translate(447.021484 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6f" transform="translate(480.322266 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-72" transform="translate(530.322266 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6d" transform="translate(574.707031 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-61" transform="translate(658.007812 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6e" transform="translate(708.007812 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-63" transform="translate(763.623047 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(808.007812 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-20" transform="translate(852.392578 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-28" transform="translate(877.392578 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-53" transform="translate(910.693359 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(966.308594 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-67" transform="translate(1010.693359 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6d" transform="translate(1060.693359 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(1143.994141 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6e" transform="translate(1188.378906 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-74" transform="translate(1243.994141 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(1277.294922 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-64" transform="translate(1321.679688 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-20" transform="translate(1377.294922 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-52" transform="translate(1402.294922 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-61" transform="translate(1474.511719 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-64" transform="translate(1524.511719 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-69" transform="translate(1580.126953 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-61" transform="translate(1607.910156 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6c" transform="translate(1657.910156 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-20" transform="translate(1685.693359 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-48" transform="translate(1710.693359 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-65" transform="translate(1788.476562 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-61" transform="translate(1832.861328 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-74" transform="translate(1882.861328 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-6d" transform="translate(1916.162109 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-61" transform="translate(1999.462891 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-70" transform="translate(2049.462891 0)"/>
     <use xlink:href="#TimesNewRomanPS-BoldMT-29" transform="translate(2105.078125 0)"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_4">
    <path d="M 482.112 413.825375 
L 498.8736 413.825375 
L 498.8736 78.593375 
L 482.112 78.593375 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
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" id="image9afc86724f" transform="scale(1 -1) translate(0 -335.04)" x="482.16" y="-78.48" width="16.8" height="335.04"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_14">
      <defs>
       <path id="mb45d889cc0" d="M 0 0 
L 3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb45d889cc0" x="498.8736" y="382.397375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 10 -->
      <g transform="translate(505.8736 385.174875) scale(0.08 -0.08)">
       <use xlink:href="#TimesNewRomanPSMT-31"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_15">
      <g>
       <use xlink:href="#mb45d889cc0" x="498.8736" y="336.849549" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 20 -->
      <g transform="translate(505.8736 339.627049) scale(0.08 -0.08)">
       <use xlink:href="#TimesNewRomanPSMT-32"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_16">
      <g>
       <use xlink:href="#mb45d889cc0" x="498.8736" y="291.301723" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 30 -->
      <g transform="translate(505.8736 294.079223) scale(0.08 -0.08)">
       <use xlink:href="#TimesNewRomanPSMT-33"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_17">
      <g>
       <use xlink:href="#mb45d889cc0" x="498.8736" y="245.753897" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_45">
      <!-- 40 -->
      <g transform="translate(505.8736 248.531397) scale(0.08 -0.08)">
       <use xlink:href="#TimesNewRomanPSMT-34"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_18">
      <g>
       <use xlink:href="#mb45d889cc0" x="498.8736" y="200.206071" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_46">
      <!-- 50 -->
      <g transform="translate(505.8736 202.983571) scale(0.08 -0.08)">
       <use xlink:href="#TimesNewRomanPSMT-35"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_19">
      <g>
       <use xlink:href="#mb45d889cc0" x="498.8736" y="154.658245" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_47">
      <!-- 60 -->
      <g transform="translate(505.8736 157.435745) scale(0.08 -0.08)">
       <use xlink:href="#TimesNewRomanPSMT-36"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_20">
      <g>
       <use xlink:href="#mb45d889cc0" x="498.8736" y="109.110418" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 70 -->
      <g transform="translate(505.8736 111.887918) scale(0.08 -0.08)">
       <use xlink:href="#TimesNewRomanPSMT-37"/>
       <use xlink:href="#TimesNewRomanPSMT-30" transform="translate(50 0)"/>
      </g>
     </g>
    </g>
    <g id="text_49">
     <!-- Score -->
     <g transform="translate(522.624225 235.963437) rotate(-270) scale(0.09 -0.09)">
      <defs>
       <path id="TimesNewRomanPSMT-53" d="M 2934 4334 
L 2934 2869 
L 2819 2869 
Q 2763 3291 2617 3541 
Q 2472 3791 2203 3937 
Q 1934 4084 1647 4084 
Q 1322 4084 1109 3886 
Q 897 3688 897 3434 
Q 897 3241 1031 3081 
Q 1225 2847 1953 2456 
Q 2547 2138 2764 1967 
Q 2981 1797 3098 1565 
Q 3216 1334 3216 1081 
Q 3216 600 2842 251 
Q 2469 -97 1881 -97 
Q 1697 -97 1534 -69 
Q 1438 -53 1133 45 
Q 828 144 747 144 
Q 669 144 623 97 
Q 578 50 556 -97 
L 441 -97 
L 441 1356 
L 556 1356 
Q 638 900 775 673 
Q 913 447 1195 297 
Q 1478 147 1816 147 
Q 2206 147 2432 353 
Q 2659 559 2659 841 
Q 2659 997 2573 1156 
Q 2488 1316 2306 1453 
Q 2184 1547 1640 1851 
Q 1097 2156 867 2337 
Q 638 2519 519 2737 
Q 400 2956 400 3219 
Q 400 3675 750 4004 
Q 1100 4334 1641 4334 
Q 1978 4334 2356 4169 
Q 2531 4091 2603 4091 
Q 2684 4091 2736 4139 
Q 2788 4188 2819 4334 
L 2934 4334 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-63" d="M 2631 1088 
Q 2516 522 2178 217 
Q 1841 -88 1431 -88 
Q 944 -88 581 321 
Q 219 731 219 1428 
Q 219 2103 620 2525 
Q 1022 2947 1584 2947 
Q 2006 2947 2278 2723 
Q 2550 2500 2550 2259 
Q 2550 2141 2473 2067 
Q 2397 1994 2259 1994 
Q 2075 1994 1981 2113 
Q 1928 2178 1911 2362 
Q 1894 2547 1784 2644 
Q 1675 2738 1481 2738 
Q 1169 2738 978 2506 
Q 725 2200 725 1697 
Q 725 1184 976 792 
Q 1228 400 1656 400 
Q 1963 400 2206 609 
Q 2378 753 2541 1131 
L 2631 1088 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-6f" d="M 1600 2947 
Q 2250 2947 2644 2453 
Q 2978 2031 2978 1484 
Q 2978 1100 2793 706 
Q 2609 313 2286 112 
Q 1963 -88 1566 -88 
Q 919 -88 538 428 
Q 216 863 216 1403 
Q 216 1797 411 2186 
Q 606 2575 925 2761 
Q 1244 2947 1600 2947 
z
M 1503 2744 
Q 1338 2744 1170 2645 
Q 1003 2547 900 2300 
Q 797 2053 797 1666 
Q 797 1041 1045 587 
Q 1294 134 1700 134 
Q 2003 134 2200 384 
Q 2397 634 2397 1244 
Q 2397 2006 2069 2444 
Q 1847 2744 1503 2744 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-72" d="M 1038 2947 
L 1038 2303 
Q 1397 2947 1775 2947 
Q 1947 2947 2059 2842 
Q 2172 2738 2172 2600 
Q 2172 2478 2090 2393 
Q 2009 2309 1897 2309 
Q 1788 2309 1652 2417 
Q 1516 2525 1450 2525 
Q 1394 2525 1328 2463 
Q 1188 2334 1038 2041 
L 1038 669 
Q 1038 431 1097 309 
Q 1138 225 1241 169 
Q 1344 113 1538 113 
L 1538 0 
L 72 0 
L 72 113 
Q 291 113 397 181 
Q 475 231 506 341 
Q 522 394 522 644 
L 522 1753 
Q 522 2253 501 2348 
Q 481 2444 426 2487 
Q 372 2531 291 2531 
Q 194 2531 72 2484 
L 41 2597 
L 906 2947 
L 1038 2947 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-65" d="M 681 1784 
Q 678 1147 991 784 
Q 1303 422 1725 422 
Q 2006 422 2214 576 
Q 2422 731 2563 1106 
L 2659 1044 
Q 2594 616 2278 264 
Q 1963 -88 1488 -88 
Q 972 -88 605 314 
Q 238 716 238 1394 
Q 238 2128 614 2539 
Q 991 2950 1559 2950 
Q 2041 2950 2350 2633 
Q 2659 2316 2659 1784 
L 681 1784 
z
M 681 1966 
L 2006 1966 
Q 1991 2241 1941 2353 
Q 1863 2528 1708 2628 
Q 1553 2728 1384 2728 
Q 1125 2728 920 2526 
Q 716 2325 681 1966 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-53"/>
      <use xlink:href="#TimesNewRomanPSMT-63" transform="translate(55.615234 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" transform="translate(100 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-72" transform="translate(150 0)"/>
      <use xlink:href="#TimesNewRomanPSMT-65" transform="translate(183.300781 0)"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_5">
    <path d="M 482.112 413.825375 
L 490.4928 413.825375 
L 498.8736 413.825375 
L 498.8736 78.593375 
L 490.4928 78.593375 
L 482.112 78.593375 
L 482.112 413.825375 
z
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pcc2cf0ad3a">
   <path d="M 426.24 246.209375 
C 426.24 218.695461 420.820359 191.449085 410.29124 166.029542 
C 399.76212 140.61 384.328288 117.511638 364.873013 98.056362 
C 345.417737 78.601087 322.319375 63.167255 296.899833 52.638135 
C 271.48029 42.109016 244.233914 36.689375 216.72 36.689375 
C 189.206086 36.689375 161.95971 42.109016 136.540167 52.638135 
C 111.120625 63.167255 88.022263 78.601087 68.566987 98.056362 
C 49.111712 117.511638 33.67788 140.61 23.14876 166.029542 
C 12.619641 191.449085 7.2 218.695461 7.2 246.209375 
C 7.2 273.723289 12.619641 300.969665 23.14876 326.389208 
C 33.67788 351.80875 49.111712 374.907112 68.566987 394.362388 
C 88.022263 413.817663 111.120625 429.251495 136.540167 439.780615 
C 161.95971 450.309734 189.206086 455.729375 216.72 455.729375 
C 244.233914 455.729375 271.48029 450.309734 296.899833 439.780615 
C 322.319375 429.251495 345.417737 413.817663 364.873013 394.362388 
C 384.328288 374.907112 399.76212 351.80875 410.29124 326.389208 
C 420.820359 300.969665 426.24 273.723289 426.24 246.209375 
M 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
C 216.72 246.209375 216.72 246.209375 216.72 246.209375 
z
"/>
  </clipPath>
 </defs>
</svg>
