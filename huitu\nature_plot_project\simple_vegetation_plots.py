import matplotlib.pyplot as plt
import numpy as np
from scipy import stats

# 设置更简单的字体配置，确保字体兼容性
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']  # 常见字体优先
plt.rcParams['font.size'] = 10
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 时间日期（x轴）
dates = ['1024', '1031', '1108', '1115', '1128', '1218']
x_numeric = np.arange(len(dates))

# 树冠1的数据
canopy1 = {
    'ExG': [0.4685979, 0.3948133, 0.3849822, 0.4232759, 0.4514346, 0.524074],
    'CVI': [0.3778748, 0.3609359, 0.3445884, 0.3775304, 0.417605, 0.4675018],
    'MGRVI': [0.80422, 0.7466185, 0.6947784, 0.8350153, 0.6765864, 0.8539257],
    'EXB': [0.7354636, 0.6994844, 0.8099242, 0.6946393, 0.6963692, 0.7525901]
}

# 树冠2的数据
canopy2 = {
    'ExG': [0.4639508, 0.3962725, 0.3961348, 0.3734632, 0.4585701, 0.480593],
    'CVI': [0.4172846, 0.1896627, 0.3470715, 0.3212644, 0.4146229, 0.4156693],
    'MGRVI': [0.7592297, 0.8725845, 0.8228972, 0.8509057, 0.7099463, 0.6306188],
    'EXB': [0.7590165, 0.6946992, 0.7686097, 0.72676501, 0.7563242, 0.7625658]
}

# 树冠3的数据
canopy3 = {
    'ExG': [0.4228484, 0.4100583, 0.4249226, 0.4216866, 0.4630271, 0.4681649],
    'CVI': [0.346686, 0.1988318, 0.3346633, 0.3618345, 0.4292103, 0.4019993],
    'MGRVI': [0.8356118, 0.7940347, 0.79819, 0.7767971, 0.6697405, 0.7593806],
    'EXB': [0.78208, 0.7343576, 0.7024199, 0.7156579, 0.7510814, 0.6928242]
}

# 颜色和标记设置
colors = {
    'ExG': '#2E5E4B',    # 绿色
    'CVI': '#4A7B8C',     # 蓝色  
    'MGRVI': '#8C4A59',   # 红色
    'EXB': '#6B929E'      # 灰蓝色
}

markers = {
    'ExG': 'o',  # 圆形
    'CVI': 's',  # 方形
    'MGRVI': '^', # 三角形
    'EXB': 'd'   # 菱形
}

def create_simple_index_plot(index_name):
    """为指定的植被指数创建简单的回归分析图，包含三个树冠的数据"""
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(6, 4), facecolor='white')
    
    # 提取这个指数在不同树冠的数据
    canopy1_data = canopy1[index_name]
    canopy2_data = canopy2[index_name]
    canopy3_data = canopy3[index_name]
    
    # 绘制散点
    ax.scatter(x_numeric, canopy1_data, color='#4A7B8C', marker='o', s=40, label='Canopy 1')
    ax.scatter(x_numeric, canopy2_data, color='#8C4A59', marker='s', s=40, label='Canopy 2')
    ax.scatter(x_numeric, canopy3_data, color='#2E5E4B', marker='^', s=40, label='Canopy 3')
    
    # 合并所有树冠数据进行回归分析
    all_x = np.concatenate([x_numeric, x_numeric, x_numeric])
    all_y = np.concatenate([canopy1_data, canopy2_data, canopy3_data])
    
    # 计算线性回归
    slope, intercept, r_value, p_value, std_err = stats.linregress(all_x, all_y)
    line = slope * x_numeric + intercept
    
    # 绘制回归线
    ax.plot(x_numeric, line, color='#E41A1C', linewidth=1.5)
    
    # 计算并绘制置信区间
    n = len(all_x)
    mean_x = np.mean(all_x)
    t_val = stats.t.ppf(0.975, n-2)
    s_err = np.sqrt(np.sum((all_y - (intercept + slope * all_x))**2) / (n - 2))
    confs = t_val * s_err * np.sqrt(1/n + (x_numeric - mean_x)**2 / np.sum((all_x - mean_x)**2))
    
    upper = line + confs
    lower = line - confs
    
    # 填充置信区间
    ax.fill_between(x_numeric, lower, upper, color='grey', alpha=0.2)
    
    # 添加回归统计信息
    stat_text = f"Slope={slope:.2f}, R²={r_value**2:.2f}, p={p_value:.3f}"
    ax.text(0.02, 0.95, stat_text, transform=ax.transAxes, fontsize=9, 
            color='#E41A1C', verticalalignment='top')
    
    # 设置轴标签
    ax.set_xlabel('Date', fontsize=10)
    ax.set_ylabel(f'{index_name} Index', fontsize=10)
    
    # 设置x轴刻度
    ax.set_xticks(x_numeric)
    ax.set_xticklabels(dates)
    
    # 添加图例
    ax.legend(loc='best', frameon=True, framealpha=0.9)
    
    # 关闭网格线
    ax.grid(False)
    
    # 设置轴范围，适当放宽
    y_values = np.concatenate([canopy1_data, canopy2_data, canopy3_data])
    y_min, y_max = min(y_values), max(y_values)
    y_range = y_max - y_min
    ax.set_ylim(y_min - 0.1*y_range, y_max + 0.1*y_range)
    
    # 优化布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(f'{index_name}_simple_plot.png', dpi=600, bbox_inches='tight')
    plt.savefig(f'{index_name}_simple_plot.pdf', bbox_inches='tight')
    
    return fig

# 为每个植被指数创建单独的图表
def main():
    for index_name in ['ExG', 'CVI', 'MGRVI', 'EXB']:
        fig = create_simple_index_plot(index_name)
        plt.show()
        plt.close(fig)  # 关闭图形以节省内存
        print(f"Created simple plot for {index_name}")

if __name__ == "__main__":
    main() 