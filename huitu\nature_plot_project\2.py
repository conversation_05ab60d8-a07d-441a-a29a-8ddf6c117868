import numpy as np
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import matplotlib.colors as mcolors
from matplotlib.patches import Wedge, Rectangle
from matplotlib.patheffects import withStroke

# 增加图表尺寸以减少密度
width_cm, height_cm = 12, 12  # 进一步增加尺寸
width_inch, height_inch = width_cm / 2.54, height_cm / 2.54

# 设置专业学术图表参数
plt.rcParams.update({
    'font.family': 'Times New Roman',
    'font.size': 7,  # 增加字体大小
    'axes.labelsize': 8,
    'xtick.labelsize': 7,
    'ytick.labelsize': 7,
    'legend.fontsize': 7,
    'figure.dpi': 600,
    'figure.figsize': (width_inch, height_inch),
    'savefig.dpi': 600,
    'savefig.bbox': 'tight'
})

# 数据准备
data = {
    'Zone': ['Zone 1', 'Zone 2', 'Zone 3'],
    'CSAF': [10248, 11027, 3294],
    'DT': [8649, 9031, 2779],
    'Real': [10982, 11563, 3589]
}

# 计算准确率
csaf_accuracy = [1 - abs(c - r) / r for c, r in zip(data['CSAF'], data['Real'])]
DT_accuracy = [1 - abs(c - r) / r for c, r in zip(data['DT'], data['Real'])]

# 创建颜色映射
cmap_green = plt.cm.Greens
cmap_blue = plt.cm.Blues
norm = mcolors.Normalize(0.7, 1.0)

# 创建精确大小的画布
fig = plt.figure(figsize=(width_inch, height_inch))
ax = fig.add_subplot(111)
# 调整布局，增加空间
fig.subplots_adjust(left=0.05, right=0.95, top=0.92, bottom=0.25)

# 核心参数调整以减少密度
inner_radius = 1.5  # 增加内半径
outer_radius = 3.2  # 增加外半径
center = (0, 0)

# 获取数据的最大值用于标准化
max_value = max(max(data['CSAF']), max(data['DT']), max(data['Real']))

# 计算每个区域的角度划分 - 显著增加区域间距
num_zones = len(data['Zone'])
angle_gap = np.pi / 4  # 极大地增加每个区域之间的间隙角度 (45度)

# 计算总的有效角度（排除间隙后的角度）
total_effective_angle = 2 * np.pi - (num_zones * angle_gap)
# 每个区域的角度宽度
zone_width = total_effective_angle / num_zones

# 创建区域角度数组
zone_angles = []
current_angle = 0
for i in range(num_zones):
    zone_angles.append(current_angle)
    current_angle += zone_width
    current_angle += angle_gap  # 添加间隙

# 添加最后一个点以闭合
zone_angles.append(2 * np.pi)

# 用于跟踪图例条目
legend_elements = []

# 绘制阳光图
for i, zone in enumerate(data['Zone']):
    start_angle = zone_angles[i]
    end_angle = start_angle + zone_width  # 确保只使用区域宽度，不包括间隙
    mid_angle = (start_angle + end_angle) / 2
    
    # 增加模型之间的间距
    model_angles = np.linspace(start_angle + np.pi/40, end_angle - np.pi/40, 3)

    # CSAF
    csaf_start, csaf_end = model_angles[0], model_angles[1] - np.pi/80
    csaf_mid = (csaf_start + csaf_end) / 2
    csaf_ratio = data['CSAF'][i] / max_value
    csaf_radius = inner_radius + csaf_ratio * (outer_radius - inner_radius)
    csaf_color = cmap_green(norm(csaf_accuracy[i]))
    csaf_wedge = Wedge(center, csaf_radius, np.degrees(csaf_start), np.degrees(csaf_end),
                       width=csaf_radius - inner_radius,
                       facecolor=csaf_color, edgecolor='white', linewidth=0.4, alpha=0.9)
    ax.add_patch(csaf_wedge)
    if i == 0:
        legend_elements.append(Rectangle((0, 0), 1, 1, facecolor=csaf_color, edgecolor='none', alpha=0.9, label='CSAF'))

    # CSAF 标签 - 简化显示
    label_radius_csaf = inner_radius + 0.6 * (csaf_radius - inner_radius)
    x_csaf = label_radius_csaf * np.cos(csaf_mid)
    y_csaf = label_radius_csaf * np.sin(csaf_mid)
    # 简化标签内容，只显示值
    ax.text(x_csaf, y_csaf, f"{data['CSAF'][i]}", ha='center', va='center',
            fontsize=7, fontweight='bold', color='black', fontname='Times New Roman',
            bbox=dict(boxstyle="round,pad=0.14", facecolor='white', alpha=0.7, edgecolor='none'))

    # DT
    DT_start, DT_end = model_angles[1] + np.pi/80, model_angles[2]
    DT_mid = (DT_start + DT_end) / 2
    DT_ratio = data['DT'][i] / max_value
    DT_radius = inner_radius + DT_ratio * (outer_radius - inner_radius)
    DT_color = cmap_blue(norm(DT_accuracy[i]))
    DT_wedge = Wedge(center, DT_radius, np.degrees(DT_start), np.degrees(DT_end),
                      width=DT_radius - inner_radius,
                      facecolor=DT_color, edgecolor='white', linewidth=0.4, alpha=0.9)
    ax.add_patch(DT_wedge)
    if i == 0:
        legend_elements.append(Rectangle((0, 0), 1, 1, facecolor=DT_color, edgecolor='none', alpha=0.9, label='DT'))

    # DT 标签 - 简化显示
    label_radius_DT = inner_radius + 0.6 * (DT_radius - inner_radius)
    x_DT = label_radius_DT * np.cos(DT_mid)
    y_DT = label_radius_DT * np.sin(DT_mid)
    ax.text(x_DT, y_DT, f"{data['DT'][i]}", ha='center', va='center',
            fontsize=7, fontweight='bold', color='black', fontname='Times New Roman',
            bbox=dict(boxstyle="round,pad=0.14", facecolor='white', alpha=0.7, edgecolor='none'))

    # 真实值参考线 - 增加宽度使其更明显
    real_ratio = data['Real'][i] / max_value
    real_radius = inner_radius + real_ratio * (outer_radius - inner_radius)
    real_wedge = Wedge(center, real_radius, np.degrees(start_angle), np.degrees(end_angle),
                       width=0.05, facecolor='gray', edgecolor='none', alpha=0.85)
    ax.add_patch(real_wedge)

    # 真实值标签 - 调整位置
    real_label_angle_offset = np.pi / 15  # 调整偏移
    real_label_radius_offset = 0.3  # 增大偏移
    if 0 <= mid_angle < np.pi/2:
        real_label_angle = mid_angle + real_label_angle_offset
    elif np.pi/2 <= mid_angle < np.pi:
         real_label_angle = mid_angle + real_label_angle_offset
    elif np.pi <= mid_angle < 3*np.pi/2:
        real_label_angle = mid_angle - real_label_angle_offset
    else:
         real_label_angle = mid_angle - real_label_angle_offset
    real_label_radius = real_radius + real_label_radius_offset
    real_x = real_label_radius * np.cos(real_label_angle)
    real_y = real_label_radius * np.sin(real_label_angle)
    ax.text(real_x, real_y, f"Real: {data['Real'][i]}", ha='center', va='center',
            fontsize=6.5, fontweight='bold', color='white', fontname='Times New Roman',
            bbox=dict(boxstyle="round,pad=0.1", facecolor='gray', alpha=0.9))

    # Zone 标签 - 调整位置更外移
    zone_label_radius = outer_radius + 0.9
    zone_label_angle = mid_angle
    label_x = zone_label_radius * np.cos(zone_label_angle)
    label_y = zone_label_radius * np.sin(zone_label_angle)
    rotation_angle = np.degrees(zone_label_angle)
    if np.pi/2 < zone_label_angle < 3*np.pi/2:
        rotation_angle += 180
    ax.text(label_x, label_y, zone, ha='center', va='center',
            fontsize=9, fontweight='bold', rotation=rotation_angle, rotation_mode='anchor', fontname='Times New Roman',
            bbox=dict(boxstyle="round,pad=0.25", facecolor='white', alpha=0.9, edgecolor='gray'))

# 绘制内圆
inner_circle = plt.Circle(center, inner_radius, facecolor='white', edgecolor='#aaaaaa', linewidth=0.6)
ax.add_patch(inner_circle)
ax.text(0, 0, "Comparison", ha='center', va='center', fontsize=10, fontweight='bold', fontname='Times New Roman')

# 添加真实值图例
legend_elements.append(Rectangle((0, 0), 1, 1, facecolor='gray', edgecolor='none', alpha=0.85, label='Real'))

# 设置图例 - 调整位置
leg = ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0.02, 0.98),
                fontsize=8, frameon=True, framealpha=0.9, edgecolor='gray',
                borderpad=0.4, labelspacing=0.6, handlelength=1.5, handletextpad=0.5, prop={'family': 'Times New Roman'})

# 设置轴范围 (扩大以容纳外移标签)
ax_limit = outer_radius + 1.4  # 增加显示范围
ax.set_xlim(-ax_limit, ax_limit)
ax.set_ylim(-ax_limit, ax_limit)
ax.set_aspect('equal')
ax.axis('off')

# 颜色条 (分开布局)
cb_y_start = 0.09
cb_height = 0.03
cb_gap = 0.03

cb_ax1 = fig.add_axes([0.2, cb_y_start + cb_height + cb_gap, 0.6, cb_height])
cb_green = plt.colorbar(cm.ScalarMappable(norm=norm, cmap=cmap_green), cax=cb_ax1,
                        orientation='horizontal')
cb_green.set_label("CSAF Accuracy", fontsize=8, fontname="Times New Roman", labelpad=3)
cb_green.ax.tick_params(labelsize=7, pad=2)
cb_green.outline.set_linewidth(0.5)
cb_green.outline.set_edgecolor('#cccccc')

cb_ax2 = fig.add_axes([0.2, cb_y_start, 0.6, cb_height])
cb_blue = plt.colorbar(cm.ScalarMappable(norm=norm, cmap=cmap_blue), cax=cb_ax2,
                       orientation='horizontal')
cb_blue.set_label("DT Accuracy", fontsize=8, fontname="Times New Roman", labelpad=3)
cb_blue.ax.tick_params(labelsize=7, pad=2)
cb_blue.outline.set_linewidth(0.5)
cb_blue.outline.set_edgecolor('#cccccc')

# 保存图像
plt.savefig('Sunburst_Large_Gaps.svg', format='svg', bbox_inches='tight', dpi=600)
plt.savefig('Sunburst_Large_Gaps.png', format='png', bbox_inches='tight', dpi=600)

plt.show()
