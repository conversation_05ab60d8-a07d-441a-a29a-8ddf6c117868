import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 数据
months = list(range(1, 13))
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'MRCNN': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [551, 548, 520, 547, 547, 547, 547, 547, 547, 547, 547, 547]
}

# 计算准确率 Accuracy = 1-(|Predicted-Real|)/Real
accuracy_csaf = [1 - (abs(csaf - real) / real) for csaf, real in zip(data['CSAF'], data['real'])]
accuracy_mrcnn = [1 - (abs(mrcnn - real) / real) for mrcnn, real in zip(data['MRCNN'], data['real'])]

# 创建图表
fig, ax = plt.subplots(figsize=(14, 7), facecolor='white')

# 颜色设置
colors = {
    'CSAF': '#5B9BD5',      # 蓝色
    'MRCNN': '#ED7D31',     # 橙色
    'real': '#70AD47'       # 绿色
}

# 条形图宽度和位置
bar_width = 0.25
r1 = np.arange(len(months))
r2 = [x + bar_width for x in r1]
r3 = [x + bar_width for x in r2]

# 绘制条形图
csaf_bars = ax.bar(r1, data['CSAF'], width=bar_width, color=colors['CSAF'], label='CSAF')
mrcnn_bars = ax.bar(r2, data['MRCNN'], width=bar_width, color=colors['MRCNN'], label='Cascade Mask R-CNN')
real_bars = ax.bar(r3, data['real'], width=bar_width, color=colors['real'], label='Real')

# 设置x轴标签
ax.set_xlabel('Month', fontsize=12, fontweight='bold')
ax.set_ylabel('Value', fontsize=12, fontweight='bold')
ax.set_xticks([r + bar_width for r in range(len(months))])
ax.set_xticklabels([f'Month {m}' for m in months])

# 添加图例
ax.legend(loc='upper right', frameon=True)

# 添加准确率标注
for i, (csaf_val, mrcnn_val) in enumerate(zip(data['CSAF'], data['MRCNN'])):
    # CSAF准确率
    acc_csaf = accuracy_csaf[i]
    ax.text(r1[i], csaf_val + 15, f"Acc: {acc_csaf:.2f}", 
            ha='center', va='bottom', fontsize=8, color=colors['CSAF'],
            fontweight='bold')
    
    # MRCNN准确率
    acc_mrcnn = accuracy_mrcnn[i]
    ax.text(r2[i], mrcnn_val + 15, f"Acc: {acc_mrcnn:.2f}", 
            ha='center', va='bottom', fontsize=8, color=colors['MRCNN'],
            fontweight='bold')
    
    # 在条形顶部显示具体数值
    ax.text(r1[i], csaf_val - 30, f"{csaf_val}", ha='center', va='top', 
            fontsize=8, color='white', fontweight='bold',
            path_effects=[path_effects.withStroke(linewidth=2, foreground=colors['CSAF'])])
    
    ax.text(r2[i], mrcnn_val - 30, f"{mrcnn_val}", ha='center', va='top', 
            fontsize=8, color='white', fontweight='bold',
            path_effects=[path_effects.withStroke(linewidth=2, foreground=colors['MRCNN'])])
    
    ax.text(r3[i], data['real'][i] - 30, f"{data['real'][i]}", ha='center', va='top', 
            fontsize=8, color='white', fontweight='bold',
            path_effects=[path_effects.withStroke(linewidth=2, foreground=colors['real'])])

# 添加标题
plt.title('Monthly Model Performance Comparison', fontsize=14, fontweight='bold')

# 网格线
ax.grid(True, axis='y', linestyle='--', alpha=0.3)

# 设置y轴范围，确保有足够空间显示标签
ax.set_ylim(0, max(max(data['CSAF']), max(data['MRCNN'])) + 100)

# 紧凑布局
plt.tight_layout()

# 保存图表
plt.savefig('direct_model_comparison.png', dpi=600, bbox_inches='tight')
plt.savefig('direct_model_comparison.pdf', bbox_inches='tight')

print("直观模型性能比较图已创建")
plt.show() 