import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon, PathPatch
from matplotlib.path import Path
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['savefig.dpi'] = 600

# 将12个月数据分组成5个部分
grouped_data = {
    'CSAF': [611, 592, 509, 611, 546],
    'MRCNN': [743, 846, 690, 774, 779],
    'real': [556, 548, 520, 551, 551]
}

# 计算总和和平均准确率
total_real = sum(grouped_data['real'])
total_csaf = sum(grouped_data['CSAF'])
total_mrcnn = sum(grouped_data['MRCNN'])
avg_acc_csaf = np.mean([1 - (abs(csaf - real) / real) for csaf, real in zip(grouped_data['CSAF'], grouped_data['real'])]) * 100
avg_acc_mrcnn = np.mean([1 - (abs(mrcnn - real) / real) for mrcnn, real in zip(grouped_data['MRCNN'], grouped_data['real'])]) * 100

# 创建两个图表
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7), facecolor='white')

# 完全匹配图片的花瓣颜色
petal_colors = ['#45D6D3', '#5E9BDF', '#A8D48E', '#F290CD', '#FF9060']
curve_colors = ['#EF7F94', '#3CD9E3', '#7BD55F', '#B08AFF', '#FF7E3D']

# 添加小图标和装饰
def add_decorations(ax, pos):
    if pos == 'left':
        # 添加左上角标记
        ax.text(-10.5, 10.5, "A", fontsize=16, fontweight='bold')
        
        # 添加左侧植物图标
        ax.text(-11, 0, r"$\it{Pv}$", fontsize=10, rotation=90, va='center')
        leaf_coords = [(-10.8, 2), (-10.3, 3), (-10.5, 4), (-11, 4.5), (-11.5, 4)]
        ax.plot([x for x, y in leaf_coords], [y for x, y in leaf_coords], 'k-', linewidth=1)
        
        # 添加左下角植物图标
        ax.text(-9, -9, r"$\it{Bv}$", fontsize=10)
        # 简化的树叶轮廓
        circle = Circle((-8, -8), 0.8, facecolor='gray', alpha=0.5)
        ax.add_patch(circle)
    else:
        # 添加右上角标记
        ax.text(10.5, 10.5, "B", fontsize=16, fontweight='bold')
        
        # 添加右侧虚线
        ax.plot([11, 11], [4, 8], 'k:', linewidth=1)
        ax.plot([11, 11], [-2, 2], 'k:', linewidth=1)
        
        # 添加右侧植物图标
        ax.text(11, 5, r"$\it{Sl}$", fontsize=10, va='center')
        
        # 添加右侧标签
        ax.text(12, 0, "% expressed genes", fontsize=8, rotation=90, va='center')

def create_pentapetalae(ax, model_values, real_values, model_name, total_model, total_real, avg_accuracy, pos):
    # 中心五边形
    n_sides = 5
    center_radius = 2.5
    
    # 创建蓝色填充的中心多边形
    pentagon_verts = [(center_radius*np.cos(2*np.pi*i/n_sides - np.pi/2), 
                      center_radius*np.sin(2*np.pi*i/n_sides - np.pi/2)) 
                      for i in range(n_sides)]
    pentagon = Polygon(pentagon_verts, closed=True, 
                      facecolor='#94ADDa', edgecolor='#2D5597', 
                      linewidth=1.5, alpha=0.6, zorder=10)
    ax.add_patch(pentagon)
    
    # 花瓣位置计算
    n_petals = len(model_values)
    radius = 7  # 花瓣中心到整体中心的距离
    petal_radius = 3.5  # 花瓣大小
    
    # 花瓣之间的连接曲线
    for i in range(n_petals):
        angle1 = 2 * np.pi * i / n_petals - np.pi/2  # 从顶部开始
        x1 = radius * np.cos(angle1)
        y1 = radius * np.sin(angle1)
        
        for j in range(i+1, n_petals):
            angle2 = 2 * np.pi * j / n_petals - np.pi/2
            x2 = radius * np.cos(angle2)
            y2 = radius * np.sin(angle2)
            
            # 计算控制点 (让曲线更自然)
            # 向外弯曲的控制点
            control_x = (x1 + x2) * 0.5
            control_y = (y1 + y2) * 0.5
            # 调整控制点距离
            dist = np.sqrt(control_x**2 + control_y**2)
            control_x = control_x / dist * (radius * 0.5)
            control_y = control_y / dist * (radius * 0.5)
            
            # 贝塞尔曲线
            verts = [
                (x1, y1),
                ((x1+control_x)*0.5, (y1+control_y)*0.5),
                (control_x, control_y),
                ((x2+control_x)*0.5, (y2+control_y)*0.5),
                (x2, y2)
            ]
            codes = [Path.MOVETO, Path.CURVE4, Path.CURVE4, Path.CURVE4, Path.LINETO]
            path = Path(verts, codes)
            
            # 随机选择颜色
            curve_color = curve_colors[(i+j) % len(curve_colors)]
            
            patch = PathPatch(path, facecolor='none', edgecolor=curve_color, 
                             linestyle='-', alpha=0.6, linewidth=1.2, zorder=1)
            ax.add_patch(patch)
    
    # 创建花瓣 (从顶部开始, 顺时针)
    for i in range(n_petals):
        angle = 2 * np.pi * i / n_petals - np.pi/2  # 从顶部开始
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # 根据值的大小稍微调整花瓣大小
        size_factor = 0.85 + 0.3 * (model_values[i] / max(model_values))
        size = petal_radius * size_factor
        
        # 创建填充花瓣
        circle = Circle((x, y), size, 
                        facecolor=petal_colors[i], alpha=0.5, 
                        edgecolor='none', zorder=5)
        ax.add_patch(circle)
        
        # 在花瓣中添加数值 (格式化为千分位)
        value_str = f"{model_values[i]:,}"
        ax.text(x, y, value_str, 
                ha='center', va='center', fontsize=12, fontweight='bold', 
                color='black', zorder=6)
    
    # 添加中心信息
    if model_name == "CSAF":
        name = model_name
    else:
        name = "Cascade\nMask R-CNN" if pos == 'right' else model_name
        
    ax.text(0, 0.8, name, ha='center', va='center', 
            fontsize=11, fontweight='bold', color='navy', zorder=11)
    ax.text(0, 0, "Core", ha='center', va='center', 
            fontsize=10, color='navy', zorder=11)
    ax.text(0, -0.5, f"{int(total_model):,}\n{int(total_real):,}", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='navy', zorder=11)
    ax.text(0, -1.5, f"[{avg_accuracy:.1f}%]", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='navy', zorder=11)
    
    # 设置图表范围和关闭坐标轴
    margin = 1.5
    ax.set_xlim(-radius*margin, radius*margin)
    ax.set_ylim(-radius*margin, radius*margin)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 添加装饰元素
    add_decorations(ax, pos)

# 创建两个花瓣图
create_pentapetalae(ax1, grouped_data['CSAF'], grouped_data['real'], 
                   "CSAF", total_csaf, total_real, avg_acc_csaf, 'left')
create_pentapetalae(ax2, grouped_data['MRCNN'], grouped_data['real'], 
                   "Mask R-CNN", total_mrcnn, total_real, avg_acc_mrcnn, 'right')

# 微调图表间距
plt.subplots_adjust(wspace=0.05)

# 保存图表
plt.savefig('pentapetalae_precise.png', dpi=600, bbox_inches='tight')
plt.savefig('pentapetalae_precise.pdf', bbox_inches='tight')

print("精确还原的五瓣花图表已创建")
plt.show() 