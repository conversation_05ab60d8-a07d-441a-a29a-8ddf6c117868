import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon, PathPatch
from matplotlib.path import Path
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['savefig.dpi'] = 600

# 完整12个月数据
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'MRCNN': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [556, 548, 520, 551, 551, 551, 551, 551, 551, 551, 551, 551]
}

# 计算准确率 Accuracy = 1-(|Predicted-Real|)/Real
accuracy_csaf = [1 - (abs(csaf - real) / real) for csaf, real in zip(data['CSAF'], data['real'])]
accuracy_mrcnn = [1 - (abs(mrcnn - real) / real) for mrcnn, real in zip(data['MRCNN'], data['real'])]

# 计算总和和平均准确率
total_real = sum(data['real'])
total_csaf = sum(data['CSAF'])
total_mrcnn = sum(data['MRCNN'])
avg_acc_csaf = sum(accuracy_csaf) / len(accuracy_csaf) * 100
avg_acc_mrcnn = sum(accuracy_mrcnn) / len(accuracy_mrcnn) * 100

# 创建两个图表
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7), facecolor='white')

# 定义更多的花瓣颜色 (为12个月准备)
petal_colors = [
    '#59C1E8', '#3D9AD1', '#7C7FD3', '#AB5FC5', '#E64A9F', 
    '#FF6B6B', '#FF9966', '#FFCC33', '#B5D33D', '#7DC45D', 
    '#42B68B', '#33CCCC'
]

def create_pentapetalae(ax, model_values, real_values, accuracies, model_name, total_model, total_real, avg_accuracy):
    # 使用五边形作为核心，但有12个月的数据
    n_sides = 5
    center_radius = 2.5
    
    # 创建蓝色填充的中心多边形
    pentagon_verts = [(center_radius*np.cos(2*np.pi*i/n_sides), 
                      center_radius*np.sin(2*np.pi*i/n_sides)) 
                      for i in range(n_sides)]
    pentagon = Polygon(pentagon_verts, closed=True, 
                      facecolor='#4472c4', edgecolor='navy', 
                      linewidth=1.5, alpha=0.4, zorder=10)
    ax.add_patch(pentagon)
    
    # 花瓣位置计算 - 12个月
    n_petals = len(model_values)
    radius = 7  # 花瓣中心到整体中心的距离
    petal_radius = 3.5  # 花瓣基础大小调小以适应更多花瓣
    
    # 创建首尾相连的曲线
    for i in range(n_petals):
        angle1 = 2 * np.pi * i / n_petals
        x1 = radius * np.cos(angle1)
        y1 = radius * np.sin(angle1)
        
        # 连接到下一个花瓣
        j = (i + 1) % n_petals
        angle2 = 2 * np.pi * j / n_petals
        x2 = radius * np.cos(angle2)
        y2 = radius * np.sin(angle2)
        
        # 计算曲线的控制点
        control_x = (x1 + x2) * 0.5
        control_y = (y1 + y2) * 0.5
        # 让控制点向外弯曲
        norm = np.sqrt(control_x**2 + control_y**2)
        if norm > 0:
            control_x = control_x / norm * (radius * 1.3)
            control_y = control_y / norm * (radius * 1.3)
        
        # 创建曲线路径
        verts = [
            (x1, y1),  # 起点
            (control_x, control_y),  # 控制点
            (x2, y2)   # 终点
        ]
        codes = [Path.MOVETO, Path.CURVE3, Path.CURVE3]
        path = Path(verts, codes)
        
        # 使用花瓣颜色
        curve_color = petal_colors[i]
        patch = PathPatch(path, facecolor='none', edgecolor=curve_color, 
                         linestyle='-', alpha=0.6, linewidth=1.0, zorder=1)
        ax.add_patch(patch)
        
        # 创建到核心的连接线
        verts = [
            (0, 0),  # 从中心开始
            (x1*0.5, y1*0.5),  # 控制点
            (x1, y1)   # 到花瓣
        ]
        path = Path(verts, codes)
        patch = PathPatch(path, facecolor='none', edgecolor=curve_color, 
                         linestyle='-', alpha=0.4, linewidth=0.7, zorder=1)
        ax.add_patch(patch)
    
    # 创建花瓣
    for i in range(n_petals):
        angle = 2 * np.pi * i / n_petals
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # 根据值的大小调整花瓣尺寸
        model_size = petal_radius * np.sqrt(model_values[i] / max(model_values)) * 0.7 + petal_radius * 0.3
        
        # 创建花瓣圆形
        circle = Circle((x, y), model_size, 
                        facecolor=petal_colors[i], alpha=0.5, 
                        edgecolor=petal_colors[i], linewidth=0.5, zorder=5)
        ax.add_patch(circle)
        
        # 在花瓣中添加预测值
        ax.text(x, y, f"{model_values[i]}", 
                ha='center', va='center', fontsize=10, fontweight='bold', 
                color='black', zorder=6)
        
        # 添加月份标签
        angle_text = angle + np.pi/n_petals  # 稍微偏移以避免与连接线重叠
        text_dist = radius + model_size + 0.5
        text_x = text_dist * np.cos(angle)
        text_y = text_dist * np.sin(angle)
        
        # 根据位置调整文本对齐方式
        ha = 'center'
        if text_x > radius*0.7:
            ha = 'left'
        elif text_x < -radius*0.7:
            ha = 'right'
            
        va = 'center'
        if text_y > radius*0.7:
            va = 'bottom'
        elif text_y < -radius*0.7:
            va = 'top'
        
        # 添加月份、准确率和真实值
        month_label = f"Month {i+1}"
        ax.text(text_x, text_y, month_label, 
                ha=ha, va=va, fontsize=8, fontweight='bold')
        
        # 在花瓣内部添加真实值和准确率
        real_val = real_values[i]
        acc_val = accuracies[i]
        
        # 圆内底部显示真实值
        ax.text(x, y-model_size*0.4, f"R:{real_val}", 
                ha='center', va='center', fontsize=7, 
                color='dimgray', zorder=6)
        
        # 圆内顶部显示准确率
        ax.text(x, y+model_size*0.4, f"A:{acc_val:.2f}", 
                ha='center', va='center', fontsize=7, 
                color='dimgray', zorder=6)
    
    # 添加中心信息
    ax.text(0, 1.0, f"{model_name}", ha='center', va='center', 
            fontsize=11, fontweight='bold', color='navy', zorder=11)
    ax.text(0, 0.3, f"Core", ha='center', va='center', 
            fontsize=10, color='navy', zorder=11)
    ax.text(0, -0.3, f"{int(total_model)}\n{int(total_real)}", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='navy', zorder=11)
    ax.text(0, -1.2, f"[{avg_accuracy:.1f}%]", ha='center', va='center', 
            fontsize=10, fontweight='bold', color='navy', zorder=11)
    
    # 设置图表范围和关闭坐标轴
    margin = 1.3
    ax.set_xlim(-radius*margin, radius*margin)
    ax.set_ylim(-radius*margin, radius*margin)
    ax.set_aspect('equal')
    ax.axis('off')

# 创建两个花瓣图
create_pentapetalae(ax1, data['CSAF'], data['real'], accuracy_csaf, 
                   "CSAF", total_csaf, total_real, avg_acc_csaf)
create_pentapetalae(ax2, data['MRCNN'], data['real'], accuracy_mrcnn, 
                   "Cascade\nMask R-CNN", total_mrcnn, total_real, avg_acc_mrcnn)

# 添加标题和标记
fig.suptitle('Model Performance Comparison - 12 Month Analysis', fontsize=14, fontweight='bold', y=0.98)
ax1.text(-9, -9, 'A', fontsize=16, fontweight='bold')
ax2.text(-9, -9, 'B', fontsize=16, fontweight='bold')

# 保存图表
plt.tight_layout()
plt.savefig('pentapetalae_complete.png', dpi=600, bbox_inches='tight')
plt.savefig('pentapetalae_complete.pdf', bbox_inches='tight')

print("12个月五瓣花图表已创建")
plt.show() 