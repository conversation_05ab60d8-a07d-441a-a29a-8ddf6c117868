import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import pandas as pd

# --- 设置图表参数 ---
try:
    plt.rcParams['font.family'] = 'Times New Roman'
except RuntimeError:
    print("Times New Roman font not found, using default.")

plt.rcParams.update({
    'font.size': 8, # Smaller base font size for potentially crowded plot
    'axes.labelsize': 9,
    'xtick.labelsize': 8,
    'ytick.labelsize': 8,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# --- 数据准备 ---
models = [
    'CSAF', 'Cascade Mask R-CNN', 'Swin Transformer', 'Hybrid Task Cascade',
    'Mask R-CNN', 'YOLOv5', 'Yolact', 'QueryInst', 'Solov2'
][::-1] # Reverse models so CSAF is outermost ring (index 0 -> largest radius)
metrics = ['AP', 'AP50', 'AP75', 'API']
N_models = len(models)
N_metrics = len(metrics)

data = np.array([
    [11.4, 39.6, 3.1, 11.4],   # Solov2
    [13.8, 43.8, 6.2, 13.9],  # QueryInst
    [22.1, 53.0, 15.3, 22.2], # Yolact
    [18.3, 48.9, 11.0, 18.4], # YOLOv5
    [30.2, 63.9, 24.1, 30.1], # Mask R-CNN
    [22.0, 53.5, 16.3, 22.3], # Hybrid Task <PERSON>
    [27.1, 59.4, 24.3, 27.3], # Swin Transformer
    [29.3, 67.8, 24.6, 29.5], # Cascade Mask R-CNN
    [35.4, 76.7, 27.5, 35.4]  # CSAF
]) # Data rows reversed to match reversed models list

# Create DataFrame (optional but good practice)
df = pd.DataFrame(data, index=models, columns=metrics)
scores = df.values # Use numpy array for plotting

# --- 创建分段径向热力图 --- 

# Define angles for metrics (segments)
# We need N_metrics + 1 boundaries for N_metrics segments
theta_bins = np.linspace(0, 2 * np.pi, N_metrics + 1)
# Define radii for models (rings)
# We need N_models + 1 boundaries for N_models rings
# Let's map model index 0 to outermost, N_models-1 to innermost
radius_bins = np.arange(N_models + 1)

# Create mesh grid for plotting
# Note: theta needs to be 2D to match scores dimensions for pcolormesh if using shading='auto'
# But for flat shading, 1D boundaries are enough. Let's use flat.
Theta, R = np.meshgrid(theta_bins, radius_bins)

fig = plt.figure(figsize=(8, 8)) # Make figure square
ax = fig.add_subplot(111, projection='polar')

# Use pcolormesh
cmap = "Greens"
pcm = ax.pcolormesh(Theta, R, scores, # Use original scores (9x4), remove .T
                   cmap=cmap,
                   shading='flat', # Flat shading uses the corners directly
                   edgecolor='lightgray', linewidth=0.5)

# Add color bar
cbar = fig.colorbar(pcm, ax=ax, shrink=0.6, pad=0.1)
cbar.set_label('Score', rotation=270, labelpad=15)

# --- 添加标注和标签 ---

# Add score annotations
angle_centers = (theta_bins[:-1] + theta_bins[1:]) / 2
radius_centers = (radius_bins[:-1] + radius_bins[1:]) / 2

for i_metric, angle in enumerate(angle_centers):
    for i_model, radius in enumerate(radius_centers):
        score = scores[i_model, i_metric] # Get score for this cell
        ax.text(angle, radius, f'{score:.1f}', # Use center angle/radius
                ha='center', va='center', fontsize=6, color='black')

# Add Metric Labels (on circumference)
ax.set_xticks(angle_centers)
ax.set_xticklabels(metrics)
ax.tick_params(axis='x', pad=10) # Add padding to move labels outward

# Add Model Labels (along one radius, e.g., 0 degrees)
# This is tricky to place nicely. Let's only label the radial grid lines for now.
ax.set_yticks(radius_centers) # Place ticks at center of rings
# ax.set_yticklabels(models) # This will likely overlap
ax.set_yticklabels([]) # Hide default radial tick labels
# Add model labels manually along one axis if needed later

# --- 定制外观 ---
ax.set_title('Model Performance (Segmented Radial Heatmap)', fontsize=14, fontweight='bold', pad=20)

# Set radial limits (optional, pcolormesh might handle it)
ax.set_ylim(0, N_models) # Radius goes from 0 to N_models

# Grid lines
ax.yaxis.grid(True, linestyle='--', alpha=0.7)
ax.xaxis.grid(True, linestyle='-', alpha=0.5) # Show radial grid lines

# Set start angle (e.g., AP at top)
ax.set_theta_offset(np.pi / 2)
ax.set_theta_direction(-1)

# --- 布局和保存 ---
plt.tight_layout()
plt.savefig('Segmented_Radial_Heatmap.png', format='png')
plt.savefig('Segmented_Radial_Heatmap.svg', format='svg')
plt.savefig('Segmented_Radial_Heatmap.pdf', format='pdf')

plt.show() 