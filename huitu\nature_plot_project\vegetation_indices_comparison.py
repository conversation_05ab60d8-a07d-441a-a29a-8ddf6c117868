import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from scipy import stats
import matplotlib.gridspec as gridspec
from matplotlib.patches import Patch

# 设置模仿Nature期刊风格的绘图样式
plt.style.use('default')
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['xtick.labelsize'] = 9
plt.rcParams['ytick.labelsize'] = 9
plt.rcParams['legend.fontsize'] = 9
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# Nature颜色调色板
nature_colors = {
    'forest_green': '#2E5E4B',  # 深绿色
    'celadon_green': '#5A8776',  # 青绿色
    'glacier_blue': '#4A7B8C',   # 冰川蓝
    'lake_blue': '#6B929E',      # 湖水蓝
    'burgundy': '#8C4A59',       # 酒红色
    'purple': '#6B4A8C',         # 紫色
    'dark_slate': '#3C4A54'      # 深板岩色
}

# 时间日期（x轴）
dates = ['0809', '0905', '0909', '0926', '1009', '1024', '1031', '1108', '1115', '1128', '1218', '0124', '0206']

# 树冠1的数据
canopy1 = {
    'ExG': [0.5764534, 0.5035132, 0.3874947, 0.42891, 0.401387, 0.4685979, 0.3948133, 0.3849822, 0.4232759, 0.4514346, 0.524074, 0.454456, 0.414858],
    'CVI': [0.4723432, 0.41436, 0.35095483, 0.34607, 0.331621, 0.3778748, 0.3609359, 0.3445884, 0.3775304, 0.417605, 0.4675018, 0.430207, 0.386635],
    'MGRVI': [0.873251, 0.768565, 0.83448511, 0.86215, 0.800199, 0.80422, 0.7466185, 0.6947784, 0.8350153, 0.6765864, 0.8539257, 0.906734, 0.874675],
    'EXB': [0.695142341, 0.715696357, 0.81389591, 0.76324, 0.719253, 0.7354636, 0.6994844, 0.8099242, 0.6946393, 0.6963692, 0.7525901, 0.660574, 0.790366]
}

# 树冠2的数据
canopy2 = {
    'ExG': [0.541312, 0.4836543, 0.36926894, 0.44096, 0.388343, 0.4639508, 0.3962725, 0.3961348, 0.3734632, 0.4585701, 0.480593, 0.435674, 0.398589],
    'CVI': [0.4415142, 0.393474812, 0.34305723, 0.36144, 0.322027, 0.4172846, 0.1896627, 0.3470715, 0.3212644, 0.4146229, 0.4156693, 0.413336, 0.371472],
    'MGRVI': [0.861342135, 0.745434542, 0.85613901, 0.89123, 0.840209, 0.7592297, 0.8725845, 0.8228972, 0.8509057, 0.7099463, 0.6306188, 0.870216, 0.840374],
    'EXB': [0.667654, 0.69412545, 0.74805617, 0.79192, 0.683291, 0.7590165, 0.6946992, 0.7686097, 0.72676501, 0.7563242, 0.7625658, 0.63467, 0.759373]
}

# 树冠3的数据
canopy3 = {
    'ExG': [0.5231425, 0.4436543, 0.36214464, 0.45918, 0.421456, 0.4228484, 0.4100583, 0.4249226, 0.4216866, 0.4630271, 0.4681649, 0.451033, 0.412826],
    'CVI': [0.4551235, 0.383474812, 0.34688855, 0.35107, 0.340988, 0.346686, 0.1988318, 0.3346633, 0.3618345, 0.4292103, 0.4019993, 0.432416, 0.385426],
    'MGRVI': [0.8451325, 0.734575464, 0.84921457, 0.83404, 0.760189, 0.8356118, 0.7940347, 0.79819, 0.7767971, 0.6697405, 0.7593806, 0.913612, 0.871673],
    'EXB': [0.6883985, 0.665766555, 0.80576143, 0.73891, 0.755216, 0.78208, 0.7343576, 0.7024199, 0.7156579, 0.7510814, 0.6928242, 0.663046, 0.788854]
}


# 数字化日期用于计算趋势线
x_numeric = np.arange(len(dates))

# 为每个指数分配颜色和标记样式
index_colors = {
    'ExG': nature_colors['forest_green'],
    'CVI': nature_colors['glacier_blue'],
    'MGRVI': nature_colors['burgundy'],
    'EXB': nature_colors['lake_blue']
}

index_markers = {
    'ExG': 'o',      # 圆形
    'CVI': 's',      # 方形
    'MGRVI': '^',    # 三角形
    'EXB': 'd'       # 菱形
}

def create_vegetation_index_comparison():
    """创建展示三个树冠植被指数时间趋势的改进图表"""
    
    # 创建图表和子图布局，添加额外空间用于标题和注释
    fig = plt.figure(figsize=(14, 7), facecolor='white')
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1], wspace=0.15)
    
    # 为图表添加总标题
    fig.suptitle('植被指数时间变化趋势对比分析', fontsize=14, y=0.98, color=nature_colors['dark_slate'])
    
    # 用于存储子图对象
    axes = []
    
    # 所有数据集
    all_canopies = [canopy1, canopy2, canopy3]
    canopy_labels = ['树冠 1', '树冠 2', '树冠 3']
    
    # 获取所有指数值的范围，用于统一Y轴范围
    all_values = []
    for canopy in all_canopies:
        for index_values in canopy.values():
            all_values.extend(index_values)
    y_min = min(all_values) - 0.05
    y_max = max(all_values) + 0.05
    
    # 为每个树冠创建子图
    for i, (canopy, label) in enumerate(zip(all_canopies, canopy_labels)):
        ax = fig.add_subplot(gs[i])
        axes.append(ax)
        
        # 存储线性回归结果用于显示
        regression_results = {}
        
        # 绘制每个植被指数
        for index_name, index_values in canopy.items():
            color = index_colors[index_name]
            marker = index_markers[index_name]
            
            # 绘制散点
            ax.scatter(x_numeric, index_values, color=color, s=60, marker=marker, 
                       label=index_name, edgecolor='white', linewidth=0.5, alpha=0.9)
            
            # 计算并绘制趋势线
            slope, intercept, r_value, p_value, std_err = stats.linregress(x_numeric, index_values)
            trend_line = slope * x_numeric + intercept
            ax.plot(x_numeric, trend_line, color=color, linestyle='-', linewidth=1.5, alpha=0.7)
            
            # 存储回归结果
            regression_results[index_name] = {
                'slope': slope, 
                'r_squared': r_value**2, 
                'p_value': p_value
            }
            
        # 添加回归统计信息
        stat_text = []
        for idx, (name, res) in enumerate(regression_results.items()):
            p_value_star = '*' if res['p_value'] < 0.05 else ('**' if res['p_value'] < 0.01 else '')
            stat_text.append(f"{name}: 斜率={res['slope']:.2f}, R²={res['r_squared']:.2f}{p_value_star}")
        
        # 计算文本位置（底部）
        text_y_pos = y_min + 0.02
        for idx, text in enumerate(stat_text):
            ax.text(0.02, 0.03 + idx*0.05, text, transform=ax.transAxes, fontsize=7, 
                    color=index_colors[list(regression_results.keys())[idx]])
        
        # 设置子图标题
        ax.set_title(label, fontsize=12, color=nature_colors['dark_slate'], pad=10)
        
        # 设置x轴标签
        ax.set_xticks(x_numeric)
        ax.set_xticklabels(dates, rotation=45, ha='right')
        
        # 设置y轴范围（统一所有子图）
        ax.set_ylim(y_min, y_max)
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.3)
        
        # 仅在第一个子图上添加y轴标签
        if i == 0:
            ax.set_ylabel('植被指数值', fontsize=11, color=nature_colors['dark_slate'])
        
        # 添加灰色背景以突出趋势
        ax.set_facecolor('#f8f8f8')
        
        # 添加图例
        if i == 0:
            # 创建自定义图例
            legend_elements = []
            for name, color in index_colors.items():
                marker = index_markers[name]
                legend_elements.append(Patch(facecolor=color, label=name))
            ax.legend(handles=legend_elements, loc='upper right', framealpha=0.9, ncol=2, fontsize=8)
    
    # 添加共同的x轴标签
    fig.text(0.5, 0.02, '日期', ha='center', fontsize=11, color=nature_colors['dark_slate'])
    
    # 添加数据来源注释
    fig.text(0.02, 0.02, '数据来源: 实验观测数据', 
             fontsize=8, color=nature_colors['dark_slate'], style='italic')
    
    # 保存图表
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig('vegetation_indices_comparison.png', dpi=600, bbox_inches='tight', pad_inches=0.1)
    plt.savefig('vegetation_indices_comparison.pdf', bbox_inches='tight', pad_inches=0.1)
    plt.savefig('vegetation_indices_comparison.svg', bbox_inches='tight', pad_inches=0.1)
    
    print("改进版植被指数时间趋势对比图已生成: vegetation_indices_comparison.png")
    return fig

# 运行可视化函数
if __name__ == "__main__":
    fig = create_vegetation_index_comparison()
    plt.show() 