import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.patheffects as path_effects
import seaborn as sns # Import seaborn for color palette
# Remove 3D imports
# from mpl_toolkits.mplot3d import Axes3D
# from matplotlib.colors import LightSource

# --- 精确设置图表尺寸 (宽度 17.51 cm, 高度 8.24 cm) ---
width_cm, height_cm = 17.51, 8.24
width_inch, height_inch = width_cm/2.54, height_cm/2.54

# 设置专业学术图表参数 (更新figsize)
plt.rcParams.update({
    'font.family': 'Times New Roman',
    'font.size': 9, # Slightly larger default size for wider figure
    'axes.labelsize': 10,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 8,
    'figure.dpi':800,
    'figure.figsize': (width_inch, height_inch),
    'savefig.dpi': 800,
    'savefig.bbox': 'tight'
})

# --- 数据准备 ---
data_dict = {
    'Zone': ['Zone1', 'Zone2', 'Zone3'],
    'CSAF': [10248, 11027, 3294],
    'Detectree2': [10094, 10190, 2779],
    'Real': [10982, 11563, 3589]
}
# # Use new precise monthly data (11-7 sequence)
# data_dict = {
#     'Month': [11,12,1,2,3,4,5,6,7], # Months 11-7 sequence
#     'CSAF': [5478, 5518, 4782, 4490, 3922, 5376, 5592, 5483, 5491],  # 更新 CSAF 数据
#     'DT': [4412, 4432, 4079, 3939, 3667, 4089, 4364, 4879, 4764],  # 更新 DT 数据
#     'Real': [5630,5583,4950,4530,3967,5625,5625,5625,5625]  # 更新 Real 数据
# }
zones = data_dict['Zone']
# months = [str(m) for m in data_dict['Month']] # Use month numbers as strings for labels
# models = ['CSAF', 'DT', 'Real']
N = len(zones)
# N = len(months) # Now N is 9

# 获取原始数值
values_csaf = np.array(data_dict['CSAF'])
values_detectree2 = np.array(data_dict['Detectree2'])
values_real = np.array(data_dict['Real'])
max_value = max(values_csaf.max(), values_detectree2.max(), values_real.max())

# --- 计算准确率 (用于标注) ---
csaf_acc = []
detectree2_acc = []
real_values = data_dict['Real']
csaf_values = data_dict['CSAF']
detectree2_values = data_dict['Detectree2']
for i in range(len(zones)):
    if real_values[i] == 0:
        acc_c = 0 if csaf_values[i] != 0 else 1
        acc_m = 0 if detectree2_values[i] != 0 else 1
    else:
        acc_c = 1 - abs(csaf_values[i] - real_values[i]) / real_values[i]
        acc_m = 1 - abs(detectree2_values[i] - real_values[i]) / real_values[i]
    csaf_acc.append(max(0, acc_c))
    detectree2_acc.append(max(0, acc_m))

accuracy_csaf = np.array(csaf_acc)
accuracy_detectree2 = np.array(detectree2_acc)

# --- 创建径向条形图 (数值为半径, 准确率标注) ---

# 定义颜色 (使用 Seaborn 的 Greens 调色板)
# colors = ['#FFE8C9', '#FDCABF', 'lightgrey'] # CSAF, DT, Real - Old colors
# Get two distinct green colors from the Greens palette
greens = sns.color_palette("Greens", n_colors=4) # Get 4 shades
colors = [greens[1], greens[2], 'lightgrey'] # Assign middle greens to CSAF, DT; Keep Real grey

# 计算角度 (现在每个 Zone 有 3 个 bar)
# theta_zone_centers = np.linspace(0.0, 2 * np.pi, N, endpoint=False)
theta_month_centers = np.linspace(0.0, 2 * np.pi, N, endpoint=False) # Renamed for clarity

zone_slice_width = (2 * np.pi / N)
gap_fraction = 0.1 # Reduce gap fraction slightly for more bars
bar_area_width = zone_slice_width * (1 - gap_fraction)
bar_width = bar_area_width / len(zones) # Width for each of the 3 bars

# Calculate angles for the three bars within each month slice
# angles_csaf = theta_zone_centers - bar_width
# angles_detectree2 = theta_zone_centers
# angles_real = theta_zone_centers + bar_width
angles_csaf = theta_month_centers - bar_width
angles_detectree2 = theta_month_centers
angles_real = theta_month_centers + bar_width

# --- 绘图 ---
fig = plt.figure(figsize=(width_inch, height_inch))
ax = fig.add_subplot(111, projection='polar')

# 设置条形底部
bottom = 0 # Start bars from the center

# 绘制条形图 (半径代表原始数值)
bar1 = ax.bar(angles_csaf, values_csaf, width=bar_width, bottom=bottom,
              color=colors[0], alpha=0.8, label='CSAF', edgecolor='black', linewidth=0.5)
bar2 = ax.bar(angles_DT, values_DT, width=bar_width, bottom=bottom,
              color=colors[1], alpha=0.8, label='DT', edgecolor='black', linewidth=0.5)
bar3 = ax.bar(angles_real, values_real, width=bar_width, bottom=bottom,
              color=colors[2], alpha=0.7, label='Real', edgecolor='darkgrey', linewidth=0.5) # Slightly different style for Real

# --- 添加准确率标注 ---
for angle, radius, acc in zip(angles_csaf, values_csaf, accuracy_csaf):
    # Place text centered on the arc
    ax.text(angle + bar_width / 2, radius, f'{acc:.2f}', # Position text exactly at bar radius
            ha='center', va='center', fontsize=6, color='black') # Center vertically, back to fontsize 6

for angle, radius, acc in zip(angles_DT, values_DT, accuracy_DT):
    # Place text centered on the arc
    ax.text(angle + bar_width / 2, radius, f'{acc:.2f}', # Position text exactly at bar radius
            ha='center', va='center', fontsize=6, color='black') # Center vertically, back to fontsize 6

# --- 定制外观 ---

# 设置半径轴（Y轴）限制和标签 (基于原始数值)
ax.set_ylim(0, max_value * 1.15) # Extend ylim slightly to accommodate labels
rticks = np.linspace(0, max_value, 5) # Adjust number of ticks as needed
ax.set_yticks(rticks)
ax.set_yticklabels([f'{int(t)}' for t in rticks]) # Integer labels for values

# ax.set_rlabel_position(0) # Move radial labels - Commented out old one
# ax.set_rlabel_position(90) # Position labels at 90 degrees - Remove fixed position

# 设置角度轴（X轴）标签
# ax.set_xticks(theta_zone_centers)
# ax.set_xticklabels(zones)
ax.set_xticks(theta_month_centers)
ax.set_xticklabels(zones, fontsize=7) # Use smaller font for more labels

# 设置网格线
ax.yaxis.grid(True, linestyle='--', alpha=0.7)
ax.xaxis.grid(False)

# 设置图表标题
# ax.set_title('Model Values Comparison (Accuracy Annotated)', va='bottom', pad=20)

# 添加图例
ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1))

# 调整起始角度 (让 Month 1 在顶部)
ax.set_theta_offset(np.pi / 2)
ax.set_theta_direction(-1)

# --- 设置背景透明 ---
fig.patch.set_alpha(0.0)
ax.patch.set_alpha(0.0)

# 调整布局
plt.subplots_adjust(left=0.1, right=0.85, top=0.85, bottom=0.1)

# --- 保存图像 --- 
# plt.savefig('Radial_Bar_Values_Accuracy_Annotated.svg', format='svg', bbox_inches='tight', transparent=True)
# plt.savefig('Radial_Bar_Values_Accuracy_Annotated.png', format='png', bbox_inches='tight', transparent=True)
# plt.savefig('Radial_Bar_Values_Accuracy_Annotated.pdf', format='pdf', bbox_inches='tight', transparent=True)
# plt.savefig('Monthly_Radial_Bar_Values_Accuracy.svg', format='svg', bbox_inches='tight', transparent=True)
# plt.savefig('Monthly_Radial_Bar_Values_Accuracy.png', format='png', bbox_inches='tight', transparent=True)
# plt.savefig('Monthly_Radial_Bar_Values_Accuracy.pdf', format='pdf', bbox_inches='tight', transparent=True)
plt.savefig('Monthly_Radial_Bar_Values_Accuracy.svg', format='svg', bbox_inches='tight', transparent=True)
plt.savefig('Monthly_Radial_Bar_Values_Accuracy.png', format='png', bbox_inches='tight', transparent=True)
plt.savefig('E:\\deeplearn\\detectron2\\tgrs\\fig\\exp5.pdf', format='pdf', bbox_inches='tight', transparent=True)

plt.show()
