import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.gridspec as gridspec

# Setting plot style to mimic Nature journal style
plt.style.use('default')
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['xtick.labelsize'] = 9
plt.rcParams['ytick.labelsize'] = 9
plt.rcParams['legend.fontsize'] = 9
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# Nature color palette
nature_colors = {
    'forest_green': '#2E5E4B',  # Forest deep green (Nature cover classic, RGB:46/94/75)
    'celadon_green': '#5A8776',  # Celadon moss green (SCI paper heatmap color, CMYK:60/25/50/10)
    'mist_green': '#8CAF97',  # Rime gray-green (Best for chart background, Pantone 626 C)
    'glacier_blue': '#4A7B8C',  # Glacier indigo blue (Nature Methods chart signature, RAL 5021)
    'lake_blue': '#6B929E',  # Lake water blue (Color-blind compatible, safety index >8.5)
    'frost_blue': '#9FC2C7',  # Morning frost light blue (Data viz auxiliary color, CMYK:35/10/15/0)
    'spruce_light': '#C4D8C9',  # Spruce light green (Chart annotation color, L*=85)
    'snow_green': '#E3EDE6',  # Snow field micro-green (Paper background base, Yxy:0.33/0.35/0.90)
    'dark_slate': '#3C4A54'  # Basalt (Dark text color)
}

# Original time series data
dates = ['0809', '0905', '0909', '0926', '1009', '1024', '1031', '1108', '1115', '1128', '1218', '0124', '0206']
real_values = [538, 538, 180, 142, 121, 121, 121, 121, 121, 121, 121, 119, 117]
prediction_values = [519, 514, 169, 131, 110, 109, 112, 111, 112, 110, 111, 108, 107]

# Calculate accuracy ratio
accuracy_ratio = [pred / real for pred, real in zip(prediction_values, real_values)]


def create_pie_series_plot():
    """Create a pie chart series along the time axis showing real vs predicted value proportions"""

    # 修改：设置精确的图像尺寸为14.65 x 3.5
    fig, ax = plt.subplots(figsize=(14.65, 3.5), facecolor='white')
    ax.set_facecolor('white')

    # Set light gray grid lines - 删除或关闭网格线
    ax.grid(False)  # 修改：关闭网格线

    # Define colors from Nature palette
    real_color = nature_colors['celadon_green']  # Real values
    pred_color = nature_colors['glacier_blue']  # Predicted values

    # Define pie chart sizes
    min_size = 30  # Minimum pie size
    max_size = 80  # Maximum pie size

    # Calculate pie sizes based on total values
    sizes = [min_size + (max_size - min_size) *
             (real + pred) / (max(real_values) + max(prediction_values))
             for real, pred in zip(real_values, prediction_values)]

    # Set horizontal positions (dates as x-axis)
    x_positions = np.arange(len(dates))

    # Vertical position (centered) - 调整垂直位置适应新高度
    y_position = 1.5

    # Draw pie chart for each date
    for i, (date, real, pred, acc, size) in enumerate(
            zip(dates, real_values, prediction_values, accuracy_ratio, sizes)):
        # Calculate pie proportions
        values = [pred, real]
        colors = [pred_color, real_color]

        # Create pie chart
        wedges, texts = ax.pie(
            values,
            colors=colors,
            radius=size / 200,
            center=(i, y_position),
            wedgeprops=dict(edgecolor='black', linewidth=0.5),
            startangle=90
        )

        # Add value labels below - 调整标签间距适应新高度
        ax.text(i, y_position - 0.6, f"Real: {real}", fontsize=8, ha='center', va='top',
                color=nature_colors['dark_slate'])
        ax.text(i, y_position - 0.85, f"Pred: {pred}", fontsize=8, ha='center', va='top',
                color=nature_colors['dark_slate'])
        ax.text(i, y_position - 1.1, f"Acc: {acc:.2f}", fontsize=8, ha='center', va='top',
                color=nature_colors['dark_slate'])

    # Set axis limits
    ax.set_xlim(-0.5, len(dates) - 0.5)
    # 修改：调整y轴范围适应新高度
    ax.set_ylim(0, 2.4)

    # Set x-ticks to dates
    ax.set_xticks(x_positions)
    ax.set_xticklabels(dates, rotation=45, ha='right')
    ax.set_xlabel('Date', fontsize=10, labelpad=10, color=nature_colors['dark_slate'])

    # Hide y-ticks
    ax.set_yticks([])
    ax.spines['left'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['top'].set_visible(False)

    # Add legend - 修改图例位置
    legend_elements = [
        plt.Rectangle((0, 0), 1, 1, facecolor=real_color, edgecolor='black', linewidth=0.5, label='Real values'),
        plt.Rectangle((0, 0), 1, 1, facecolor=pred_color, edgecolor='black', linewidth=0.5, label='Predicted values')
    ]
    # 修改：将图例位置从'upper right'改为'lower right'，或使用bbox_to_anchor调整位置
    ax.legend(handles=legend_elements, loc='upper right', frameon=True, framealpha=0.9)

    # Save figure
    # 修改：调整保存参数以减少空白
    plt.tight_layout(pad=1.0)
    plt.savefig('nature_pie_series_plot.pdf', bbox_inches='tight', pad_inches=0.1)
    plt.savefig('nature_pie_series_plot.png', dpi=600, bbox_inches='tight', pad_inches=0.1)
    plt.savefig('nature_pie_series_plot.svg', bbox_inches='tight', pad_inches=0.1)

    print(
        "Pie chart series visualization generated: nature_pie_series_plot.pdf, nature_pie_series_plot.png, and nature_pie_series_plot.svg")
    return fig


# Run visualization function
if __name__ == "__main__":
    fig = create_pie_series_plot()
    plt.show() 