import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
from matplotlib import cm
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 更新的数据
months = list(range(1, 13))
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'Cascade': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [551, 548, 520, 547, 547, 547, 547, 547, 547, 547, 547, 547]
}

# 计算精度
precision_csaf = [min(real/csaf, 1.0) if csaf > 0 else 0 for real, csaf in zip(data['real'], data['CSAF'])]
precision_cascade = [min(real/cascade, 1.0) if cascade > 0 else 0 for real, cascade in zip(data['real'], data['Cascade'])]

# 创建图表
fig, axes = plt.subplots(1, 12, figsize=(15, 3.5), facecolor='white')
plt.subplots_adjust(wspace=0.05)

# 颜色设置 - 使用更美观的颜色
colors = {
    'CSAF': '#5B9BD5',      # 蓝色
    'Cascade': '#ED7D31',   # 橙红色
    'real': '#70AD47',      # 绿色
}

# 最大值用于缩放
max_value = max(max(data['CSAF']), max(data['Cascade']))

# 画每个月份的图表
for i, ax in enumerate(axes):
    month = months[i]
    csaf_val = data['CSAF'][i]
    cascade_val = data['Cascade'][i]
    real_val = data['real'][i]
    
    # 清除坐标轴
    ax.set_xlim(-1.5, 1.5)
    ax.set_ylim(-2.0, 1.8)
    ax.axis('off')
    
    # 添加月份标题
    ax.text(0, 1.6, f"Month {month}", ha='center', fontsize=10, fontweight='bold')
    
    # 计算椭圆的大小（直接按比例计算半径，让圆更饱满）
    size_factor = 0.9  # 增大椭圆整体大小
    csaf_width = size_factor * csaf_val / max_value * 2
    cascade_width = size_factor * cascade_val / max_value * 2
    real_width = size_factor * real_val / max_value * 2
    
    # 设置高宽比为0.6，使椭圆看起来更美观
    height_ratio = 0.6
    
    # 确定位置关系
    real_x = 0  # 实际值居中
    csaf_x = -0.3  # CSAF偏左
    cascade_x = 0.3  # Cascade偏右
    
    # 创建半透明椭圆
    real_ellipse = Ellipse((real_x, 0), real_width, real_width*height_ratio, 
                         alpha=0.7, color=colors['real'], zorder=1)
    csaf_ellipse = Ellipse((csaf_x, 0), csaf_width, csaf_width*height_ratio, 
                         alpha=0.7, color=colors['CSAF'], zorder=2)
    cascade_ellipse = Ellipse((cascade_x, 0), cascade_width, cascade_width*height_ratio, 
                            alpha=0.7, color=colors['Cascade'], zorder=3)
    
    # 添加椭圆
    ax.add_patch(real_ellipse)
    ax.add_patch(csaf_ellipse)
    ax.add_patch(cascade_ellipse)
    
    # 在椭圆中添加值
    text_style = dict(ha='center', va='center', fontsize=9, fontweight='bold',
                    path_effects=[path_effects.withStroke(linewidth=3, foreground='white')])
    
    ax.text(real_x, 0, f"{real_val}", color='white', **text_style)
    
    # 添加模型值和精度，格式整齐
    y_start = -1.6
    line_height = 0.25
    
    # CSAF信息
    ax.text(-0.6, y_start, "CSAF:", ha='right', fontsize=8, color='black')
    ax.text(-0.5, y_start, f"{csaf_val}", ha='left', fontsize=8, color=colors['CSAF'], fontweight='bold')
    ax.text(-0.6, y_start-line_height, "Prec:", ha='right', fontsize=8, color='black')
    ax.text(-0.5, y_start-line_height, f"{precision_csaf[i]:.2f}", ha='left', fontsize=8, 
            color=colors['CSAF'], fontweight='bold')
    
    # Cascade信息
    ax.text(0.6, y_start, "Casc:", ha='right', fontsize=8, color='black')
    ax.text(0.7, y_start, f"{cascade_val}", ha='left', fontsize=8, color=colors['Cascade'], fontweight='bold')
    ax.text(0.6, y_start-line_height, "Prec:", ha='right', fontsize=8, color='black')
    ax.text(0.7, y_start-line_height, f"{precision_cascade[i]:.2f}", ha='left', fontsize=8, 
            color=colors['Cascade'], fontweight='bold')

# 添加图例
legend_ax = fig.add_axes([0.92, 0.15, 0.07, 0.7])
legend_ax.axis('off')

legend_items = []
y_pos = 0.9
for model, color in colors.items():
    ellipse = Ellipse((0.3, y_pos), 0.4, 0.2, alpha=0.7, color=color)
    legend_ax.add_patch(ellipse)
    legend_ax.text(0.6, y_pos, model, va='center', fontsize=9, fontweight='bold')
    y_pos -= 0.2

# 添加标题
fig.suptitle('Monthly Model Performance Comparison', fontsize=14, y=0.98, fontweight='bold')

# 保存图表
plt.savefig('improved_model_comparison.png', dpi=600, bbox_inches='tight')
plt.savefig('improved_model_comparison.pdf', bbox_inches='tight')

print("改进的Nature风格模型比较图已创建")
plt.show() 