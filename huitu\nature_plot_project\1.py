import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
import matplotlib.gridspec as gridspec

# 设置Nature风格字体配置
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 10
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 时间日期（x轴）
dates = ['0809', '0905', '0909', '0926', '1009', '1024', '1031', '1108', '1115', '1128', '1218', '0124', '0206']

x_numeric = np.arange(len(dates))

# 树冠a的数据
canopy1 = {
    'ExG': [0.5764534, 0.5035132, 0.38749473, 0.4289121, 0.4013867, 0.3819193, 0.4100583, 0.3937344, 0.4945019,
            0.4514346, 0.524074, 0.4544564, 0.4148584],
    'CVI': [0.4723432, 0.41436, 0.35095483, 0.346066, 0.331621, 0.3263225, 0.3609359, 0.3280622, 0.4352461,
            0.417605, 0.4675018, 0.4302068, 0.3866352],
    'MGRVI': [0.873251, 0.768565, 0.83448511, 0.8621455, 0.8001987, 0.7664017, 0.7466158, 0.8714415, 0.830057,
              0.6765864, 0.8539257, 0.9067335, 0.8746754],
    'ExB': [0.695142341, 0.715696357, 0.81389591, 0.763236, 0.7192533, 0.7297851, 0.7343576, 0.690867, 0.7316,
            0.6963692, 0.7525901, 0.6605737, 0.7903663]
}

# 树冠b的数据
canopy2 = {
    'ExG': [0.541312, 0.4836543, 0.36926894, 0.4409633, 0.3883426, 0.3651706, 0.3962725, 0.382663, 0.4438652,
            0.4585701, 0.480593, 0.4356738, 0.3985894],
    'CVI': [0.4415142, 0.393474812, 0.34305723, 0.3614386, 0.3220274, 0.300074, 0.330657, 0.3415337, 0.4097526,
            0.4146229, 0.4156693, 0.4133359, 0.371472],
    'MGRVI': [0.861342135, 0.745434542, 0.85613901, 0.8912346, 0.8402086, 0.8555746, 0.8725845, 0.692771,
              0.8682479, 0.7099463, 0.6306188, 0.8702155, 0.8403744],
    'ExB': [0.667654, 0.69412545, 0.74805617, 0.7919237, 0.6832906, 0.713531, 0.6994844, 0.810263, 0.7987266,
            0.7563242, 0.7625658, 0.6346697, 0.7593725]
}

# 树冠c的数据
canopy3 = {
    'ExG': [0.5231425, 0.4436543, 0.36214464, 0.4591837, 0.4214559, 0.4113284, 0.3955082, 0.3930825, 0.4187964,
            0.4630271, 0.4681649, 0.4510329, 0.412826],
    'CVI': [0.4551235, 0.383474812, 0.34688855, 0.351066, 0.3409876, 0.3755737, 0.3584653, 0.3556957, 0.3344635,
            0.4292103, 0.4019993, 0.432416, 0.3854256],
    'MGRVI': [0.8451325, 0.734575464, 0.84921457, 0.8340362, 0.7601888, 0.8223141, 0.7940347, 0.7914594,
              0.8282361, 0.6697405, 0.7593806, 0.9136122, 0.8716726],
    'ExB': [0.6883985, 0.665766555, 0.80576143, 0.7389061, 0.7552159, 0.6917191, 0.6946992, 0.6960102, 0.7814626,
            0.7510814, 0.6928242, 0.6630457, 0.7888535]
}


# 植被指数的颜色和标记
index_colors = {
    'ExG': '#2E5E4B',  # 深绿色
    'CVI': '#4A7B8C',  # 蓝色
    'MGRVI': '#8C4A59',  # 红色
    'ExB': '#6B929E'  # 灰蓝色
}

index_markers = {
    'ExG': 'o',  # 圆形
    'CVI': 's',  # 方形
    'MGRVI': '^',  # 三角形
    'ExB': 'd'  # 菱形
}

# 统计信息位置偏移量，避免重叠
text_offsets = {
    'ExG': (0.0, 0.05),  # 下方偏移
    'CVI': (0.0, -0.05),  # 上方偏移
    'MGRVI': (0.0, 0.05),  # 下方偏移
    'ExB': (0.0, -0.05)  # 上方偏移
}


def create_simple_trend_plots():
    """创建三个树冠的横向排列图表，每个图表包含该树冠的所有植被指数"""

    # 创建大图和子图布局，增大图表尺寸
    fig = plt.figure(figsize=(20, 5), facecolor='white')  # 增加宽度至20
    gs = gridspec.GridSpec(1, 3, width_ratios=[1, 1, 1], wspace=0.3)  # 增加子图之间的间距

    # 所有树冠数据和标签
    all_canopies = [canopy1, canopy2, canopy3]
    canopy_labels = ['Canopy 1', 'Canopy 2', 'Canopy 3']

    # 确定每个树冠的y轴范围
    y_ranges = []
    for canopy_data in all_canopies:
        all_values = []
        for values in canopy_data.values():
            all_values.extend(values)
        y_min, y_max = min(all_values), max(all_values)
        y_range = y_max - y_min
        y_ranges.append((y_min - 0.15 * y_range, y_max + 0.15 * y_range))

    # 为每个树冠创建子图
    for i, (canopy_data, label, y_range) in enumerate(zip(all_canopies, canopy_labels, y_ranges)):
        ax = plt.subplot(gs[i])

        # 为每个植被指数绘制散点和趋势线
        for index_name, values in canopy_data.items():
            color = index_colors[index_name]
            marker = index_markers[index_name]

            # 绘制散点
            ax.scatter(x_numeric, values, color=color, s=40, marker=marker,
                       label=index_name, alpha=0.9)

            # 计算并绘制趋势线
            slope, intercept, r_value, p_value, std_err = stats.linregress(x_numeric, values)
            line = slope * x_numeric + intercept
            ax.plot(x_numeric, line, color=color, linestyle='-', linewidth=1.5)

            # 计算并绘制95%置信区间
            n = len(x_numeric)
            mean_x = np.mean(x_numeric)
            t_val = stats.t.ppf(0.975, n - 2)
            s_err = np.sqrt(np.sum((values - (intercept + slope * x_numeric)) ** 2) / (n - 2))
            confs = t_val * s_err * np.sqrt(1 / n + (x_numeric - mean_x) ** 2 / np.sum((x_numeric - mean_x) ** 2))

            upper = line + confs
            lower = line - confs
            ax.fill_between(x_numeric, lower, upper, color=color, alpha=0.1)

            # 添加简洁的统计信息在趋势线旁边
            text_x = x_numeric[len(x_numeric) // 2]  # 选择x轴中间位置
            offset_x, offset_y = text_offsets[index_name]

            text_y = slope * text_x + intercept
            text_y += offset_y

            stat_text = f"Slope={slope:.2f}, R²={r_value ** 2:.2f}, p={p_value:.3f}"

            ax.text(text_x, text_y, stat_text, fontsize=8, color=color,
                    ha='center', va='center',
                    bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=2))

        # 设置轴标签和标题
        ax.set_title(label, fontsize=12)
        ax.set_xlabel('Date', fontsize=10)

        if i == 0:
            ax.set_ylabel('Vegetation Index', fontsize=10)

        # 设置x轴刻度，并调整标签旋转角度
        ax.set_xticks(x_numeric)
        ax.set_xticklabels(dates, rotation=45, ha='right')  # 调整旋转角度为45度

        # 设置y轴范围
        ax.set_ylim(y_range)

        # 添加图例
        ax.legend(loc='lower right', frameon=True, framealpha=0.8)

        # 关闭网格线
        ax.grid(False)

    # 优化布局
    plt.tight_layout(pad=1.5)

    # 保存图表
    plt.savefig('simple_trend_plots.svg', dpi=600, bbox_inches='tight', pad_inches=0.1)
    plt.savefig('simple_trend_plots.pdf', bbox_inches='tight', pad_inches=0.1)

    print("Created simple trend plots with inline statistics")
    return fig


if __name__ == "__main__":
    fig = create_simple_trend_plots()
    plt.show()
