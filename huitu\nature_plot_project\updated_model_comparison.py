import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
from matplotlib import cm
import matplotlib.patheffects as path_effects

# 设置Nature风格
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.size'] = 9
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600

# 数据
months = list(range(1, 13))
data = {
    'CSAF': [611, 592, 509, 611, 546, 502, 624, 499, 475, 479, 458, 514],
    'MRCNN': [743, 846, 690, 774, 779, 653, 700, 604, 621, 684, 666, 606],
    'real': [551, 548, 520, 547, 547, 547, 547, 547, 547, 547, 547, 547]
}

# 计算准确率 Accuracy = 1-(|Predicted-Real|)/Real
accuracy_csaf = [1 - (abs(csaf - real) / real) for csaf, real in zip(data['CSAF'], data['real'])]
accuracy_mrcnn = [1 - (abs(mrcnn - real) / real) for mrcnn, real in zip(data['MRCNN'], data['real'])]

# 创建图表
fig, axes = plt.subplots(1, 12, figsize=(15, 2.5), facecolor='white')
plt.subplots_adjust(wspace=0.05, bottom=0.3)

# 颜色设置 - 使用图片中的颜色
colors = {
    'CSAF': '#5B9BD5',      # 蓝色
    'MRCNN': '#ED7D31',     # 橙色
    'real': '#70AD47'       # 绿色
}

# 画每个月份的图表
for i, ax in enumerate(axes):
    month = months[i]
    csaf_val = data['CSAF'][i]
    mrcnn_val = data['MRCNN'][i]
    real_val = data['real'][i]
    
    # 清除坐标轴
    ax.set_xlim(-0.7, 0.7)
    ax.set_ylim(-0.6, 0.6)
    ax.axis('off')
    
    # 添加月份标题
    ax.text(0, 0.5, f"Month {month}", ha='center', fontsize=9)
    
    # 椭圆的位置和大小
    width = 0.5
    height = 0.3
    
    # 创建椭圆 - 横向排列
    real_ellipse = Ellipse((0, 0), width, height, 
                         alpha=0.7, color=colors['real'], zorder=1)
    csaf_ellipse = Ellipse((-0.2, 0), width, height, 
                         alpha=0.7, color=colors['CSAF'], zorder=2)
    mrcnn_ellipse = Ellipse((0.2, 0), width, height, 
                           alpha=0.7, color=colors['MRCNN'], zorder=3)
    
    # 添加椭圆
    ax.add_patch(real_ellipse)
    ax.add_patch(csaf_ellipse)
    ax.add_patch(mrcnn_ellipse)
    
    # 在椭圆中添加值
    text_style = dict(ha='center', va='center', fontsize=9, fontweight='bold',
                      path_effects=[path_effects.withStroke(linewidth=3, foreground='white')])
    
    ax.text(0, 0, f"{real_val}", color='white', **text_style)

# 在底部添加模型值和精度信息
bottom_ax = fig.add_axes([0.05, 0.05, 0.9, 0.15])
bottom_ax.axis('off')

# 设置底部文本格式
x_positions = np.linspace(0.04, 0.96, 12)
y_pos = 0.8

for i in range(12):
    csaf_val = data['CSAF'][i]
    mrcnn_val = data['MRCNN'][i]
    acc_csaf = accuracy_csaf[i]
    acc_mrcnn = accuracy_mrcnn[i]
    
    # 使用格式"CSAF/MRCNN: 值"和"Acc: 准确率"
    bottom_ax.text(x_positions[i], y_pos, f"CSAF/MRCNN: {csaf_val}/{mrcnn_val}", 
                  ha='center', va='top', fontsize=7)
    bottom_ax.text(x_positions[i], y_pos-0.3, f"Acc: {acc_csaf:.2f}/Acc: {acc_mrcnn:.2f}", 
                  ha='center', va='top', fontsize=7)

# 添加图例
legend_ax = fig.add_axes([0.92, 0.3, 0.07, 0.5])
legend_ax.axis('off')

y_pos = 0.8
for model, color in colors.items():
    ellipse = Ellipse((0.2, y_pos), 0.3, 0.15, alpha=0.7, color=color)
    legend_ax.add_patch(ellipse)
    
    # 修改Cascade Mask R-CNN的标签
    if model == 'MRCNN':
        legend_ax.text(0.5, y_pos, "Cascade\nMask R-CNN", va='center', fontsize=8)
    else:
        legend_ax.text(0.5, y_pos, model, va='center', fontsize=8)
    
    y_pos -= 0.2

# 添加标题
fig.suptitle('Monthly Model Performance Comparison', fontsize=12, y=0.95)

# 保存图表
plt.savefig('model_comparison_accuracy.png', dpi=600, bbox_inches='tight')
plt.savefig('model_comparison_accuracy.pdf', bbox_inches='tight')

print("模型性能比较图已创建 - 使用新的准确率计算方法")
plt.show() 