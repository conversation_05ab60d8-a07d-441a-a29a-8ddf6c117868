import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from scipy import stats
import seaborn as sns  # Import seaborn
import matplotlib.gridspec as gridspec

# 设置模仿Nature期刊风格的绘图样式（可以调整或由Seaborn覆盖）
# sns.set_theme(style="ticks") # 使用 seaborn 风格
# plt.style.use('seaborn-v0_8-ticks') # More specific seaborn style if needed

# Let's keep some manual controls for finer tuning based on the target image
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans'] # Match target potentially
plt.rcParams['axes.linewidth'] = 1.0 # Slightly thicker for clarity like the example
plt.rcParams['xtick.major.width'] = 1.0
plt.rcParams['ytick.major.width'] = 1.0
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['xtick.labelsize'] = 9
plt.rcParams['ytick.labelsize'] = 9
plt.rcParams['legend.fontsize'] = 8 # Slightly smaller for fitting text
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600
# Removed Nature colors dict as we'll define them per index type

# --- Data ---
dates = ['0809', '0905', '0909', '0926', '1009', '1024', '1031', '1108', '1115', '1128', '1218', '0124', '0206']
x_numeric = np.arange(len(dates))

canopies_data = {
    'Canopy 1': {
        'ExG': [0.5764534, 0.5035132, 0.3874947, 0.42891, 0.401387, 0.4685979, 0.3948133, 0.3849822, 0.4232759, 0.4514346, 0.524074, 0.454456, 0.414858],
        'CVI': [0.4723432, 0.41436, 0.35095483, 0.34607, 0.331621, 0.3778748, 0.3609359, 0.3445884, 0.3775304, 0.417605, 0.4675018, 0.430207, 0.386635],
        'MGRVI': [0.873251, 0.768565, 0.83448511, 0.86215, 0.800199, 0.80422, 0.7466185, 0.6947784, 0.8350153, 0.6765864, 0.8539257, 0.906734, 0.874675],
        'ExB': [0.695142341, 0.715696357, 0.81389591, 0.76324, 0.719253, 0.7354636, 0.6994844, 0.8099242, 0.6946393, 0.6963692, 0.7525901, 0.660574, 0.790366]
    },
    'Canopy 2': {
        'ExG': [0.541312, 0.4836543, 0.36926894, 0.44096, 0.388343, 0.4639508, 0.3962725, 0.3961348, 0.3734632, 0.4585701, 0.480593, 0.435674, 0.398589],
        'CVI': [0.4415142, 0.393474812, 0.34305723, 0.36144, 0.322027, 0.4172846, 0.1896627, 0.3470715, 0.3212644, 0.4146229, 0.4156693, 0.413336, 0.371472],
        'MGRVI': [0.861342135, 0.745434542, 0.85613901, 0.89123, 0.840209, 0.7592297, 0.8725845, 0.8228972, 0.8509057, 0.7099463, 0.6306188, 0.870216, 0.840374],
        'ExB': [0.667654, 0.69412545, 0.74805617, 0.79192, 0.683291, 0.7590165, 0.6946992, 0.7686097, 0.72676501, 0.7563242, 0.7625658, 0.63467, 0.759373]
    },
    'Canopy 3': {
        'ExG': [0.5231425, 0.4436543, 0.36214464, 0.45918, 0.421456, 0.4228484, 0.4100583, 0.4249226, 0.4216866, 0.4630271, 0.4681649, 0.451033, 0.412826],
        'CVI': [0.4551235, 0.383474812, 0.34688855, 0.35107, 0.340988, 0.346686, 0.1988318, 0.3346633, 0.3618345, 0.4292103, 0.4019993, 0.432416, 0.385426],
        'MGRVI': [0.8451325, 0.734575464, 0.84921457, 0.83404, 0.760189, 0.8356118, 0.7940347, 0.79819, 0.7767971, 0.6697405, 0.7593806, 0.913612, 0.871673],
        'ExB': [0.6883985, 0.665766555, 0.80576143, 0.73891, 0.755216, 0.78208, 0.7343576, 0.7024199, 0.7156579, 0.7510814, 0.6928242, 0.663046, 0.788854]
    }
}

# Convert to long-form DataFrame
data_list = []
for canopy_label, indices in canopies_data.items():
    for index_name, values in indices.items():
        for i, value in enumerate(values):
            data_list.append({
                'Canopy': canopy_label,
                'Index_Type': index_name,
                'Date': dates[i],
                'x_numeric': x_numeric[i],
                'Value': value
            })
df = pd.DataFrame(data_list)

# Define colors and markers precisely matching the example image
palette = {
    'ExG': '#24754F',    # Darker Green
    'CVI': '#4E818F',    # Grayish Blue
    'MGRVI': '#99545F',  # Dusky Red/Brown
    'ExB': '#6BA3AD'     # Lighter Teal/Blue
}
markers = {
    'ExG': 'o',
    'CVI': 's',
    'MGRVI': '^',
    'ExB': 'D'
}

# --- Plotting Function ---
def create_vegetation_index_plot():
    """Creates a plot showing vegetation index time trends for three canopies, matching the example style."""

    fig, axes = plt.subplots(1, 3, figsize=(14, 4.5), sharey=True, facecolor='none') 
    fig.patch.set_alpha(0.0)  # 确保图表背景完全透明
    
    # 设置每个子图的背景为透明
    for ax in axes:
        ax.patch.set_alpha(0.0)
    
    fig.subplots_adjust(wspace=0.1) # Adjust spacing between subplots

    canopy_labels = ['Canopy 1', 'Canopy 2', 'Canopy 3']

    # Define text positions relative to axes (adjust these for optimal placement)
    text_positions = {
        'MGRVI': (0.45, 0.88),
        'ExB':   (0.45, 0.68),
        'ExG':   (0.45, 0.48),
        'CVI':   (0.45, 0.28)
    }
    # Text vertical alignment adjustment to place text slightly above the y-coordinate
    text_va = 'bottom'

    for i, label in enumerate(canopy_labels):
        ax = axes[i]
        canopy_df = df[df['Canopy'] == label]

        for index_name in ['ExG', 'CVI', 'MGRVI', 'ExB']: # Control plot order
            index_df = canopy_df[canopy_df['Index_Type'] == index_name]
            color = palette[index_name]
            marker = markers[index_name]

            # Plot using seaborn regplot
            sns.regplot(
                x='x_numeric', y='Value', data=index_df, ax=ax,
                color=color, marker=marker,
                scatter_kws={'s': 50, 'edgecolor': 'black', 'linewidth': 0.5}, # Adjusted marker edge for visibility like example
                line_kws={'linewidth': 1.5},
                ci=95 # Show 95% confidence interval
            )

            # Calculate stats for annotation
            slope, intercept, r_value, p_value, std_err = stats.linregress(index_df['x_numeric'], index_df['Value'])
            r_squared = r_value**2

            # Add text annotation
            stat_text = f"Slope={slope:.2f}, R²={r_squared:.2f}, p={p_value:.3f}"
            # Position text relative to the axes using pre-defined coordinates
            text_x, text_y = text_positions[index_name]
            ax.text(text_x, text_y, stat_text, transform=ax.transAxes, fontsize=8,
                    color=color, verticalalignment=text_va, horizontalalignment='left')

        # --- Subplot Formatting ---
        ax.set_title(label, fontsize=12, pad=10)
        ax.set_xticks(x_numeric)
        ax.set_xticklabels(dates, rotation=45, ha='right')
        ax.set_xlabel('') # Remove individual x-labels

        # Set Y-axis limits exactly as in the example
        ax.set_ylim(0.25, 1.0)
        ax.tick_params(axis='both', direction='in') # Ticks pointing in like example

        # Remove grid lines
        ax.grid(False)

        # --- Legend ONLY on the first plot ---
        if i == 0:
            handles = [plt.Line2D([0], [0], marker=markers[name], color='w', label=name,
                                  markerfacecolor=palette[name], markersize=7, markeredgecolor='grey') # Added grey edge to legend markers
                       for name in ['ExG', 'CVI', 'MGRVI', 'ExB']] # Explicit order
            ax.legend(handles=handles, loc='lower right', frameon=True, 
                      framealpha=0.6, # Make the frame semi-transparent
                      facecolor='white', 
                      edgecolor='none', 
                      fontsize=9) # Match example style

        # --- Y-axis label ONLY on the first plot ---
        if i == 0:
            ax.set_ylabel('Vegetation Index', fontsize=10)
        else:
            # Explicitly remove label possibly added by seaborn on other axes
            ax.set_ylabel('')

    # --- Global Figure Formatting ---
    # Add a common X-axis label
    fig.text(0.5, 0.01, 'Date', ha='center', va='center', fontsize=10)

    # Adjust layout to prevent labels overlapping
    plt.tight_layout(rect=[0.03, 0.03, 1, 0.95]) # Adjust rect slightly for y-label

    # Save the plot in multiple formats with transparent background
    plt.savefig('vegetation_indices_seaborn_plot.png', dpi=600, bbox_inches='tight', pad_inches=0.1, transparent=True)
    plt.savefig('vegetation_indices_seaborn_plot.pdf', bbox_inches='tight', pad_inches=0.1, transparent=True)
    plt.savefig('vegetation_indices_seaborn_plot.svg', bbox_inches='tight', pad_inches=0.1, transparent=True)

    print("Seaborn-style vegetation indices plot saved with transparent background: vegetation_indices_seaborn_plot.png/pdf/svg")
    return fig

# Run the plotting function
if __name__ == "__main__":
    fig = create_vegetation_index_plot()
    plt.show() 