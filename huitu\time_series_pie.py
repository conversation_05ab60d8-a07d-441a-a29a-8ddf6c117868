import numpy as np
import matplotlib.pyplot as plt

# 设置字体和样式
plt.rcParams.update({
    'font.family': 'Arial',
    'font.size': 20,
    'figure.figsize': (24, 6),
    'figure.dpi': 300
})

# 数据
dates = ['0809', '0905', '0909', '0926', '1009', '1024', '1031', '1108', '1115', '1128', '1218', '0124', '0206']
real_values = [538, 538, 180, 142, 121, 121, 121, 121, 121, 121, 121, 119, 117]
pred_values = [519, 514, 169, 131, 110, 109, 112, 111, 112, 110, 111, 108, 107]
accuracies = [pred / real if real != 0 else 0 for pred, real in zip(pred_values, real_values)]

# 创建图表
fig, ax = plt.subplots(figsize=(24, 6))
fig.patch.set_facecolor('#FFFFFF')
ax.set_facecolor('#FFFFFF')

# 使用与图片一致的红蓝配色
real_color = '#C8E6C9'  # 蓝色
pred_color = '#2E7D32'  # 红色

# 计算圆的大小和位置
max_value = max(max(real_values), max(pred_values))
sizes = [max(real, pred) / max_value for real, pred in zip(real_values, pred_values)]
base_radius = 0.25
larger_radius = 0.7
spacing_factor = 1.8

# 绘制饼图序列
for i, (date, real, pred, acc, size) in enumerate(zip(dates, real_values, pred_values, accuracies, sizes)):
    # 调整圆的大小
    radius = larger_radius if real > 500 else base_radius

    # 计算中心位置
    center = (i * spacing_factor, 0)

    # 绘制饼图
    wedges, _ = ax.pie([real, pred],
                       center=center,
                       radius=radius,
                       colors=[real_color, pred_color],
                       startangle=90,
                       counterclock=False)

    # 添加边框
    for wedge in wedges:
        wedge.set_edgecolor('#4A4A4A')  # 深灰色边框
        wedge.set_linewidth(0.6)  # 适中的边框宽度

    # 添加文本标注
    text_color = '#4A4A4A'  # 统一的文本颜色
    plt.text(i * spacing_factor, -0.8, f"Real: {real}", ha='center', va='center', fontsize=18, color=text_color)
    plt.text(i * spacing_factor, -1.1, f"Count: {pred}", ha='center', va='center', fontsize=18, color=text_color)
    plt.text(i * spacing_factor, -1.4, f"Acc: {acc:.2f}", ha='center', va='center', fontsize=18, color=text_color)
    plt.text(i * spacing_factor, -1.9, date, ha='center', va='center', rotation=45, fontsize=18, color=text_color)

# 设置坐标轴
ax.set_xlim(-1, len(dates) * spacing_factor)
ax.set_ylim(-2.0, 1)

# 移除坐标轴
ax.set_axis_off()

# 添加图例
real_patch = plt.Rectangle((0, 0), 1, 1, fc=real_color, ec='#4A4A4A', label='Real values')
pred_patch = plt.Rectangle((0, 0), 1, 1, fc=pred_color, ec='#4A4A4A', label='Counted values')
legend = plt.legend(handles=[real_patch, pred_patch],
                    loc='upper right',
                    bbox_to_anchor=(1.0, 1.2),  # 将图例位置上移
                    frameon=True,
                    fancybox=True,
                    framealpha=0.98,
                    edgecolor='#E5E5E5')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('time_series_pie.svg', dpi=300, bbox_inches='tight', pad_inches=0.5)
plt.savefig('time_series_pie.pdf', format='pdf', bbox_inches='tight', pad_inches=0.5)

plt.show() 